#!/bin/bash

# Script pour tester le module Chat
# Usage: ./scripts/test-chat.sh [option]

set -e

echo "🧪 Tests du Module Chat - Amorzee"
echo "================================="

case "${1:-all}" in
  "unit")
    echo "🔬 Lancement des tests unitaires..."
    npm run test:unit
    ;;
  "integration")
    echo "🔗 Lancement des tests d'intégration..."
    npm run test:integration
    ;;
  "coverage")
    echo "📊 Génération du rapport de couverture..."
    npm run test:coverage
    ;;
  "watch")
    echo "👀 Mode watch pour les tests du chat..."
    npm run test:chat:watch
    ;;
  "all")
    echo "🚀 Lancement de tous les tests..."
    echo ""
    echo "1️⃣ Tests unitaires..."
    npm run test:unit
    echo ""
    echo "2️⃣ Tests d'intégration..."
    npm run test:integration
    echo ""
    echo "✅ Tous les tests sont passés avec succès !"
    ;;
  "help"|"-h"|"--help")
    echo "Usage: $0 [option]"
    echo ""
    echo "Options disponibles:"
    echo "  unit         Lancer uniquement les tests unitaires"
    echo "  integration  Lancer uniquement les tests d'intégration"
    echo "  coverage     Générer un rapport de couverture"
    echo "  watch        Mode watch pour les tests du chat"
    echo "  all          Lancer tous les tests (défaut)"
    echo "  help         Afficher cette aide"
    echo ""
    echo "Exemples:"
    echo "  $0 unit"
    echo "  $0 coverage"
    echo "  $0"
    ;;
  *)
    echo "❌ Option inconnue: $1"
    echo "Utilisez '$0 help' pour voir les options disponibles."
    exit 1
    ;;
esac
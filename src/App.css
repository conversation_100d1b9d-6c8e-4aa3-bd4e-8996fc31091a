.App {
  text-align: center;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Navigation Styles */
nav {
  background-color: #282c34;
  padding: 15px 0;
  margin-bottom: 30px;
  border-radius: 5px;
}

nav ul {
  display: flex;
  list-style-type: none;
  margin: 0;
  padding: 0;
  justify-content: center;
}

nav li {
  margin: 0 15px;
}

nav a {
  color: white;
  text-decoration: none;
  font-size: 18px;
  font-weight: bold;
  transition: color 0.3s;
}

nav a:hover {
  color: #61dafb;
}

/* Page Styles */
.home-page, .login-page {
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Form Styles */
.form-group {
  margin-bottom: 15px;
  text-align: left;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button[type="submit"] {
  background-color: #282c34;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

button[type="submit"]:hover {
  background-color: #61dafb;
  color: #282c34;
}

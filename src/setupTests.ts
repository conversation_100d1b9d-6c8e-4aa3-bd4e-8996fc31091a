// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Mock WebSocket globally for tests
global.WebSocket = jest.fn(() => ({
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  send: jest.fn(),
  close: jest.fn(),
  readyState: WebSocket.OPEN,
})) as any;

// Mock console.error to avoid noise in tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is deprecated')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// Global test utilities
export const createMockMessage = (overrides: any = {}) => ({
  id: 'test-msg-123',
  content: 'Test message',
  senderId: 'user-1',
  receiverId: 'user-2',
  type: 'text',
  status: 'sent',
  sentAt: new Date(),
  ...overrides,
});

export const createMockChat = (overrides: any = {}) => ({
  id: 'test-chat-123',
  participantIds: ['user-1', 'user-2'],
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

// Mock environment variables
process.env.REACT_APP_API_URL = 'http://localhost:3001';
process.env.REACT_APP_WS_URL = 'http://localhost:3001';

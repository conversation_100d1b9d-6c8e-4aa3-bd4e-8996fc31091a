import React, { useState } from 'react';
import { BackgroundWrapper, ThemeProvider } from '@/modules/shared/presentation';
import { NavigationBar } from './NavigationBar';
import { useNavigate } from 'react-router-dom';
import { X } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const Layout: React.FC<LayoutProps> = ({ children, className }) => {
  const navigate = useNavigate();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleNavigateToChat = () => {
    navigate('/chat');
    setMobileMenuOpen(false);
  };

  const handleNavigateToHome = () => {
    navigate('/');
    setMobileMenuOpen(false);
  };

  return (
    <ThemeProvider>
      <BackgroundWrapper variant="app">
        <div className="h-screen relative flex flex-col">
          {/* Layout principal en flex-row */}
          <div className="flex flex-1 min-h-0">
            {/* Bouton hamburger pour mobile */}
            <button
              className="md:hidden fixed top-4 left-4 z-50 p-2 rounded-lg bg-white backdrop-blur-md border border-white/20"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6 text-black" />
              ) : (
                <svg
                  className="h-6 w-6 text-black"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              )}
            </button>

            {/* Sidebar */}
            <aside
              className={`fixed md:relative inset-y-0 left-0 w-64 min-w-[220px] h-screen bg-white backdrop-blur-xl border-r border-white/10 flex flex-col z-[9999] transform transition-transform duration-300 ease-in-out ${
                mobileMenuOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
              }`}
            >
              <NavigationBar 
                onNavigateToChat={handleNavigateToChat}
                onNavigateToHome={handleNavigateToHome}
              />
            </aside>
            
            {/* Contenu principal */}
            <main 
              className={`flex-1 overflow-y-auto min-h-0 p-4 md:p-8 ${className || ''} ${
                mobileMenuOpen ? 'opacity-30 md:opacity-100' : ''
              } transition-opacity duration-300`}
              onClick={() => mobileMenuOpen && setMobileMenuOpen(false)}
            >
              {children}
            </main>
          </div>
        </div>
      </BackgroundWrapper>
    </ThemeProvider>
  );
};
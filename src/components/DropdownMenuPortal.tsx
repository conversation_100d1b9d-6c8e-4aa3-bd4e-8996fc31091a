import React, { ReactNode, useEffect, useRef } from "react";
import { createPortal } from "react-dom";

interface DropdownMenuPortalProps {
  children: ReactNode;
  anchorRef: React.RefObject<HTMLElement | null>;
  open: boolean;
}

export const DropdownMenuPortal: React.FC<DropdownMenuPortalProps> = ({
  children,
  anchorRef,
  open,
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!open || !anchorRef.current || !menuRef.current) return;
    
    const anchorRect = anchorRef.current.getBoundingClientRect();
    menuRef.current.style.position = "fixed";
    menuRef.current.style.top = `${anchorRect.bottom - 140}px`;
    menuRef.current.style.left = `${anchorRect.right - 140}px`; // 160 = menu width
    menuRef.current.style.zIndex = "10000";
  }, [open, anchorRef]);

  if (!open) return null;

  return createPortal(
    <div ref={menuRef} style={{ width: 200 }}>
      {children}
    </div>,
    document.body
  );
};
import React, { useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  Card,
  CardContent,
  Button,
  Input,
} from "@/modules/shared/presentation";
import { User, UserBlockStatus } from "@/modules/users/domain/entities/User";
import { UserProfileFilters } from "@/modules/users/domain/repositories/UserRepository";
import { Country } from "@/modules/users/domain/valueobjects/Country";
import { Appearance } from "@/modules/users/domain/valueobjects/Appearance";
import { userApiRepository } from "@/modules/users/infrastructure/api/UserApiRepository";
import { useUserLikes } from "@/modules/users/presentation/hooks/useUserLikes";
import {
  MessageCircle,
  Heart,
  X,
  Search,
  Filter,
  Plus,
  Loader2,
  Users,
  MapPin,
  Calendar,
  Ruler,
  Globe,
  User as UserIcon,
  AlertCircle,
  Eye,
} from "lucide-react";

interface UserSuggestionsWithSearchProps {
  onStartChat: (user: User) => void;
  onReportUser: (user: User) => void; // Optionnel, si vous souhaitez gérer les rapports
}

interface SearchFilters {
  searchTerm: string;
  gender: string;
  minAge?: number;
  maxAge?: number;
  country: string;
  ethnicity: string;
  minHeight?: number;
  maxHeight?: number;
  isOnline?: boolean;
  relationType: string;
  hairColor: string;
  eyeColor: string;
  bodyType: string;
  bodyColor: string;
  religion: string;
}

export const UserSuggestionsWithSearch: React.FC<
  UserSuggestionsWithSearchProps
> = ({ onStartChat, onReportUser }) => {
  const navigate = useNavigate();
  const {
    likeUser,
    dislikeUser,
    isLiking,
    isDisliking,
    likedUsers,
    dislikedUsers,
  } = useUserLikes();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [offset, setOffset] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const [filters, setFilters] = useState<SearchFilters>({
    searchTerm: "",
    gender: "",
    minAge: 18,
    maxAge: 18,
    country: "",
    ethnicity: "",
    minHeight: 140,
    maxHeight: 140,
    isOnline: undefined,
    relationType: "",
    hairColor: "",
    eyeColor: "",
    bodyType: "",
    bodyColor: "",
    religion: "",
  });

  // Récupérer les listes d'options
  const countries = Country.getAllSupported();
  const ethnicityOptions = Appearance.getEthnicityOptions();
  const hairColorOptions = Appearance.getHairColorOptions();
  const eyeColorOptions = Appearance.getEyeColorOptions();
  const bodyTypeOptions = Appearance.getBodyTypeOptions();
  const bodyColorOptions = Appearance.getBodyColorOptions();
  const religionOptions = Appearance.getReligionOptions();

  const handleLikeUser = async (e: React.MouseEvent, user: User) => {
    e.stopPropagation();
    const success = await likeUser(user.id);
    if (success) {
      setUsers((prev) =>
        prev.map((u) => (u.id !== user.id ? u : u.withInteractionType("like")))
      );
    }
  };

  const handleDislikeUser = async (e: React.MouseEvent, user: User) => {
    e.stopPropagation();
    const success = await dislikeUser(user.id);
    if (success) {
      setUsers((prev) =>
        prev.map((u) =>
          u.id !== user.id ? u : u.withInteractionType("dislike")
        )
      );
    }
  };

  const searchUsers = useCallback(
    async (isLoadMore = false) => {
      if (isLoadMore) {
        setIsLoadingMore(true);
      } else {
        setIsLoading(true);
        setOffset(0);
        setHasSearched(true);
      }

      setError(null);

      try {
        const currentOffset = isLoadMore ? offset : 0;
        const profileFilters: UserProfileFilters = {};

        // Construction des filtres de profil
        if (filters.gender) profileFilters.gender = filters.gender;
        if (filters.minAge) profileFilters.minAge = filters.minAge;
        if (filters.maxAge) profileFilters.maxAge = filters.maxAge;
        if (filters.country) profileFilters.country = filters.country;
        if (filters.ethnicity) profileFilters.ethnicity = filters.ethnicity;
        if (filters.minHeight) profileFilters.minHeight = filters.minHeight;
        if (filters.maxHeight) profileFilters.maxHeight = filters.maxHeight;
        if (filters.relationType)
          profileFilters.relationType = filters.relationType;
        if (filters.hairColor) profileFilters.hairColor = filters.hairColor;
        if (filters.eyeColor) profileFilters.eyeColor = filters.eyeColor;
        if (filters.bodyType) profileFilters.bodyType = filters.bodyType;
        if (filters.bodyColor) profileFilters.bodyColor = filters.bodyColor;
        if (filters.religion) profileFilters.religion = filters.religion;
        if (filters.isOnline !== undefined)
          profileFilters.isOnline = filters.isOnline;

        const result = await userApiRepository.searchUsersWithFilters({
          searchTerm: filters.searchTerm || "",
          limit: 5,
          offset: currentOffset,
          profileFilters:
            Object.keys(profileFilters).length > 0 ? profileFilters : undefined,
        });

        if (isLoadMore) {
          setUsers((prev) => [...prev, ...result.users]);
        } else {
          setUsers(result.users);
        }

        setHasMore(result.hasMore);
        setOffset(currentOffset + result.users.length);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Error on searching users"
        );
        if (!isLoadMore) {
          setUsers([]);
        }
      } finally {
        setIsLoading(false);
        setIsLoadingMore(false);
      }
    },
    [filters, offset]
  );

  const loadMore = useCallback(() => {
    if (hasMore && !isLoadingMore) {
      searchUsers(true);
    }
  }, [searchUsers, hasMore, isLoadingMore]);

  // Fonction debounce pour les recherches automatiques
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

  const handleFilterChange = useCallback(
    (key: keyof SearchFilters, value: any) => {
      setFilters((prev) => ({ ...prev, [key]: value }));

      // Recherche automatique avec debounce pour les champs de texte
      if (key === "searchTerm") {
        if (searchTimeout) {
          clearTimeout(searchTimeout);
        }

        const timeout = setTimeout(() => {
          searchUsers(false);
        }, 500); // 500ms de délai

        setSearchTimeout(timeout);
      }
    },
    [searchUsers, searchTimeout]
  );

  // Nettoyer le timeout au démontage
  React.useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  // Filtres actifs
  const activeFilters = Object.entries(filters).filter(([key, value]) => {
    if (key === "searchTerm") return value && value.trim().length > 0;
    return value !== undefined && value !== "";
  });

  const getInitials = (username: string): string => {
    return username
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const avatarGradients = [
    "from-blue-400 via-purple-500 to-pink-500",
    "from-green-400 via-blue-500 to-purple-600",
    "from-pink-400 via-red-500 to-yellow-500",
    "from-indigo-400 via-purple-500 to-pink-500",
    "from-cyan-400 via-blue-500 to-indigo-600",
    "from-orange-400 via-pink-500 to-red-500",
  ];

  const getAvatarGradient = (userId: string) => {
    const index = userId.length % avatarGradients.length;
    return avatarGradients[index];
  };

  const handleUserClick = (user: User) => {
    navigate(`/profile/${user.id}`);
  };

  const relationsTypes = [
    { value: "single", label: "Single" },
    { value: "friends", label: "Friends" },
    { value: "in_a_relationship", label: "In a relationship" },
    { value: "married", label: "Married" },
    { value: "divorced", label: "Divorced" },
    { value: "widowed", label: "Widowed" },
  ];

  return (
    <div className="w-full space-y-6">
      {/* En-tête */}
      <div className="flex mt-12 lg:mt-0 items-center justify-between">
        <h2 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-purple-400 via-blue-400 to-pink-500 bg-clip-text text-transparent text-transparent flex items-center space-x-3">
          <Search className="h-8 w-8 text-pink-400" />
          <span>Search for people</span>
        </h2>
      </div>

      {/* Section des filtres */}
      <Card className="p-6 border-0 bg-white shadow-xl backdrop-blur-xl">
        <div className="space-y-4">
          {/* Bouton pour afficher/masquer les filtres */}
          <div className="flex flex-col lg:flex-row space-y-4 lg:space-y-0 items-center justify-between">
            <h3 className="text-lg font-semibold text-black flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Search filters</span>
            </h3>
            <div className="flex items-center space-x-3">
              {/* Bouton Rechercher */}
              <Button
                onClick={() => searchUsers(false)}
                disabled={isLoading}
                className="inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none rounded-lg disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-[var(--color-primary-500)] focus:ring-offset-2 shadow-md hover:shadow-lg hover:scale-105 px-4 py-2 text-sm bg-pink-600 hover:bg-pink-700 text-white border-0"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
                <span>Search</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 bg-black/10 hover:bg-black/20 border-black/20 text-black"
              >
                <Plus
                  className={`h-4 w-4 transition-transform ${
                    showFilters ? "rotate-45" : ""
                  }`}
                />
                <span>{showFilters ? "Hide" : "Add"} filters</span>
              </Button>
            </div>
          </div>

          {/* Chips des filtres actifs */}
          {activeFilters.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {activeFilters.map(([key, value]) => (
                <div
                  key={key}
                  className="flex items-center space-x-2 bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-sm border border-purple-400/30"
                >
                  <span>
                    {key === "searchTerm" ? "Search" : key}:{" "}
                    {value?.toString()}
                  </span>
                  <button
                    onClick={() =>
                      handleFilterChange(
                        key as keyof SearchFilters,
                        key === "searchTerm"
                          ? ""
                          : typeof value === "boolean"
                          ? undefined
                          : typeof value === "number"
                          ? undefined
                          : ""
                      )
                    }
                    className="text-purple-300 hover:text-red-400 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          )}

          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-white/5 rounded-lg border border-white/10">
              {/* Nom d'utilisateur */}
              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <UserIcon className="h-4 w-4" />
                  <span>Username</span>
                </label>
                <Input
                  placeholder="Search by name..."
                  value={filters.searchTerm}
                  onChange={(e) =>
                    handleFilterChange("searchTerm", e.target.value)
                  }
                  className="bg-black/10 border-black/20 text-black placeholder:text-black/60"
                />
              </div>

              {/* Genre */}
              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>Gender</span>
                </label>
                <select
                  value={filters.gender || ""}
                  onChange={(e) =>
                    handleFilterChange("gender", e.target.value || undefined)
                  }
                  className="w-full bg-black/10 border-black/20 text-gray-900 rounded-md p-2 h-10 hover:bg-black/10 focus:bg-black/10 focus:outline-none focus:ring-2 focus:ring-purple-400"
                >
                  <option value="" className="text-gray-900">
                    All
                  </option>
                  <option value="male" className="text-gray-900">
                    Male
                  </option>
                  <option value="female" className="text-gray-900">
                    Female
                  </option>
                  <option value="non_binary" className="text-gray-900">
                    Non-binary
                  </option>
                  <option value="other" className="text-gray-900">
                    Other
                  </option>
                </select>
              </div>

              {/* Âge */}
              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>Age</span>
                </label>
                <div className="flex space-x-2">
                  <Input
                    type="number"
                    min={18}
                    placeholder="Min"
                    value={filters.minAge || ""}
                    onChange={(e) =>
                      handleFilterChange(
                        "minAge",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                    className="bg-black/10 border-black/20 text-gray-900 placeholder:text-gray-500"
                  />
                  <Input
                    type="number"
                    min={18}
                    placeholder="Max"
                    value={filters.maxAge || ""}
                    onChange={(e) =>
                      handleFilterChange(
                        "maxAge",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                    className="bg-black/10 border-black/20 text-gray-900 placeholder:text-gray-500"
                  />
                </div>
              </div>

              {/* Pays */}
              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <MapPin className="h-4 w-4" />
                  <span>Country</span>
                </label>
                <select
                  value={filters.country || ""}
                  onChange={(e) =>
                    handleFilterChange("country", e.target.value || undefined)
                  }
                  className="w-full bg-black/10 border-black/20 text-gray-900 rounded-md p-2 h-10 hover:bg-black/10 focus:bg-black/10 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <option value="" className="text-gray-900">
                    All countries
                  </option>
                  {countries.map((country) => (
                    <option
                      key={country.code}
                      value={country.name}
                      className="text-gray-900"
                    >
                      {country.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Origine ethnique */}
              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>Ethnicity</span>
                </label>
                <select
                  value={filters.ethnicity || ""}
                  onChange={(e) =>
                    handleFilterChange("ethnicity", e.target.value || undefined)
                  }
                  className="w-full bg-black/10 border-black/20 text-gray-900 rounded-md p-2 h-10 hover:bg-black/10 focus:bg-black/10 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <option value="" className="text-gray-900">
                    All ethnicities
                  </option>
                  {ethnicityOptions.map((option) => (
                    <option
                      key={option.value}
                      value={option.value}
                      className="text-gray-900"
                    >
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Taille */}
              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <Ruler className="h-4 w-4" />
                  <span>Height (cm)</span>
                </label>
                <div className="flex space-x-2">
                  <Input
                    type="number"
                    min={140}
                    placeholder="Min"
                    value={filters.minHeight || ""}
                    onChange={(e) =>
                      handleFilterChange(
                        "minHeight",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                    className="bg-black/10 border-black/20 text-gray-900 placeholder:text-gray-500"
                  />
                  <Input
                    type="number"
                    min={140}
                    placeholder="Max"
                    value={filters.maxHeight || ""}
                    onChange={(e) =>
                      handleFilterChange(
                        "maxHeight",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                    className="bg-black/10 border-black/20 text-gray-900 placeholder:text-gray-500"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <Globe className="h-4 w-4" />
                  <span>Status</span>
                </label>
                <select
                  value={
                    filters.isOnline === undefined
                      ? ""
                      : filters.isOnline.toString()
                  }
                  onChange={(e) =>
                    handleFilterChange(
                      "isOnline",
                      e.target.value === ""
                        ? undefined
                        : e.target.value === "true"
                    )
                  }
                  className="w-full bg-black/10 border-black/20 text-gray-900 rounded-md p-2 h-10 hover:bg-black/10 focus:bg-black/10 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <option value="" className="text-gray-900">
                    All
                  </option>
                  <option value="true" className="text-gray-900">
                    Online
                  </option>
                  <option value="false" className="text-gray-900">
                    Offline
                  </option>
                </select>
              </div>
              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>Relation type</span>
                </label>
                <select
                  value={filters.relationType || ""}
                  onChange={(e) =>
                    handleFilterChange(
                      "relationType",
                      e.target.value || undefined
                    )
                  }
                  className="w-full bg-black/10 border-black/20 text-gray-900 rounded-md p-2 h-10 hover:bg-black/10 focus:bg-black/10 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <option value="" className="text-gray-900">
                    All relation types
                  </option>
                  {relationsTypes.map((option) => (
                    <option
                      key={option.value}
                      value={option.value}
                      className="text-gray-900"
                    >
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>Hair color</span>
                </label>

                <select
                  value={filters.hairColor || ""}
                  onChange={(e) =>
                    handleFilterChange("hairColor", e.target.value || undefined)
                  }
                  className="w-full bg-black/10 border-black/20 text-gray-900 rounded-md p-2 h-10 hover:bg-black/10 focus:bg-black/10 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <option value="" className="text-gray-900">
                    All hair colors
                  </option>
                  {hairColorOptions.map((option) => (
                    <option
                      key={option.value}
                      value={option.value}
                      className="text-gray-900"
                    >
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>Body color</span>
                </label>

                <select
                  value={filters.bodyColor || ""}
                  onChange={(e) =>
                    handleFilterChange("bodyColor", e.target.value || undefined)
                  }
                  className="w-full bg-black/10 border-black/20 text-gray-900 rounded-md p-2 h-10 hover:bg-black/10 focus:bg-black/10 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <option value="" className="text-gray-900">
                    All body colors
                  </option>
                  {bodyColorOptions.map((option) => (
                    <option
                      key={option.value}
                      value={option.value}
                      className="text-gray-900"
                    >
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <Eye className="h-4 w-4" />
                  <span>Eye color</span>
                </label>

                <select
                  value={filters.eyeColor || ""}
                  onChange={(e) =>
                    handleFilterChange("eyeColor", e.target.value || undefined)
                  }
                  className="w-full bg-black/10 border-black/20 text-gray-900 rounded-md p-2 h-10 hover:bg-black/10 focus:bg-black/10 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <option value="" className="text-gray-900">
                    All eye colors
                  </option>
                  {eyeColorOptions.map((option) => (
                    <option
                      key={option.value}
                      value={option.value}
                      className="text-gray-900"
                    >
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>Body type</span>
                </label>

                <select
                  value={filters.bodyType || ""}
                  onChange={(e) =>
                    handleFilterChange("bodyType", e.target.value || undefined)
                  }
                  className="w-full bg-black/10 border-black/20 text-gray-900 rounded-md p-2 h-10 hover:bg-black/10 focus:bg-black/10 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <option value="" className="text-gray-900">
                    All body types
                  </option>
                  {bodyTypeOptions.map((option) => (
                    <option
                      key={option.value}
                      value={option.value}
                      className="text-gray-900"
                    >
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <label className="text-sm text-black/80 flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>Religion</span>
                </label>

                <select
                  value={filters.religion || ""}
                  onChange={(e) =>
                    handleFilterChange("religion", e.target.value || undefined)
                  }
                  className="w-full bg-black/10 border-black/20 text-gray-900 rounded-md p-2 h-10 hover:bg-black/10 focus:bg-black/10 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <option value="" className="text-gray-900">
                    All religions
                  </option>
                  {religionOptions.map((option) => (
                    <option
                      key={option.value}
                      value={option.value}
                      className="text-gray-900"
                    >
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* État de chargement initial */}
      {isLoading && users.length === 0 && (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-purple-400" />
        </div>
      )}

      {/* Message d'erreur */}
      {error && (
        <Card className="p-6 border-0 shadow-xl backdrop-blur-xl bg-red-500/20 border-red-400/40">
          <div className="text-center text-red-300">{error}</div>
        </Card>
      )}

      {/* Résultats */}
      {users.length > 0 && (
        <div className="space-y-6">
          <div className="text-black/80 text-sm">
            {users.length} result{users.length > 1 ? "s" : ""} found
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {users.map((user) => (
              <Card
                key={user.id}
                className="group relative overflow-hidden border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 hover:scale-105 cursor-pointer backdrop-blur-xl bg-white"
                onClick={() => handleUserClick(user)}
              >
                <CardContent className="p-4">
                  {/* Photo de profil */}
                  <div className="flex justify-center mb-4">
                    <div className="relative mt-6">
                      {user.profilePhoto ? (
                        <img
                          src={user.profilePhoto}
                          alt={`Profil de ${user.username}`}
                          className="w-16 h-16 rounded-full object-cover ring-2 ring-white/20 shadow-lg"
                        />
                      ) : (
                        <div
                          className={`w-16 h-16 rounded-full bg-gradient-to-br ${getAvatarGradient(
                            user.id
                          )} flex items-center justify-center shadow-lg ring-2 ring-white/20`}
                        >
                          <span className="text-lg font-bold text-white">
                            {getInitials(user.username)}
                          </span>
                        </div>
                      )}

                      {user.isOnline && (
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full ring-2 ring-black/20">
                          <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-40"></div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Informations utilisateur */}
                  <div className="text-center space-y-2">
                    <h3
                      className="font-semibold text-black text-sm truncate"
                      title={user.username}
                    >
                      {user.lastName} {user.firstName}
                    </h3>

                    {user.getAge() && (
                      <p className="text-xs text-black/60">
                        {user.getAge()} years
                      </p>
                    )}

                    {user.country && (
                      <p className="text-xs text-black/60">{user.country}</p>
                    )}
                  </div>

                  {/* Boutons d'action */}
                  <div className="flex justify-center space-x-2 mt-4">
                    {user.interactionType === "NONE" && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={isDisliking === user.id}
                          className="w-8 h-8 rounded-full p-0 border border-black/20 hover:border-red-400 hover:bg-red-500/80"
                          onClick={(e) => handleDislikeUser(e, user)}
                        >
                          {isDisliking === user.id ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : (
                            <X className="h-3 w-3 text-black/80" />
                          )}
                        </Button>
                        <Button
                          size="sm"
                          disabled={isLiking === user.id}
                          className="w-8 h-8 rounded-full p-0 bg-gradient-to-r from-pink-500 to-red-500 hover:from-pink-400 hover:to-red-400"
                          onClick={(e) => handleLikeUser(e, user)}
                        >
                          {isLiking === user.id ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : (
                            <Heart className="h-3 w-3 text-white" />
                          )}
                        </Button>
                      </>
                    )}
                    {user.blockStatus !== UserBlockStatus.BLOCKED ? (
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-8 h-8 rounded-full p-0 border border-black/20 hover:border-purple-400 hover:bg-purple-500/80"
                        onClick={(e) => {
                          e.stopPropagation();
                          onStartChat(user);
                        }}
                      >
                        <MessageCircle className="h-3 w-3 text-black/80" />
                      </Button>
                    ) : (
                      <div className="flex items-center justify-end text-white/70">
                        {user.blockStatus === UserBlockStatus.BLOCKED && (
                          <div className="text-red-500 text-sm font-medium p-2 bg-red-500/20 rounded-xl capitalize">
                            This user is {UserBlockStatus.BLOCKED}, you can't
                            interact with them.
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  {/* Report button */}
                  {user.blockStatus !== UserBlockStatus.BLOCKED && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 rounded-xl w-full flex items-center justify-center gap-x-4 mt-6 p-0 border border-black/20 hover:border-red-400 hover:bg-red-500/80"
                      onClick={(e) => {
                        e.stopPropagation();
                        onReportUser(user);
                      }}
                    >
                      <AlertCircle className="h-3 w-3 text-black/80" />
                      <span className="text-xs text-black/80">Report user</span>
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Bouton charger plus */}
          {hasMore && (
            <div className="flex justify-center">
              <Button
                onClick={loadMore}
                disabled={isLoadingMore}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-2"
              >
                {isLoadingMore ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                {isLoadingMore ? "Chargement..." : "Charger plus"}
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Message si aucun résultat */}
      {hasSearched && users.length === 0 && !isLoading && !error && (
        <Card className="p-8 border-0 shadow-xl backdrop-blur-xl bg-white">
          <div className="text-center text-black/80">
            <Search className="h-12 w-12 mx-auto mb-4 text-black/30" />
            <p className="text-lg font-semibold mb-2">No results found</p>
            <p className="text-sm">Try adjusting your search criteria</p>
          </div>
        </Card>
      )}

      {/* Message d'aide si aucune recherche effectuée */}
      {!hasSearched && (
        <Card className="p-8 border-0 shadow-xl backdrop-blur-xl bg-pink-500/20">
          <div className="text-center text-pink-300">
            <Filter className="h-12 w-12 mx-auto mb-4 text-pink-300" />
            <p className="text-lg font-semibold mb-2">
              Click "Search" to get started
            </p>
            <p className="text-sm">
              Use the filters above to refine your search
            </p>
          </div>
        </Card>
      )}
    </div>
  );
};

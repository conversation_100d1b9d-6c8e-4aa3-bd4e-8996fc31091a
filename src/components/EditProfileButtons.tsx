import { But<PERSON>, <PERSON>, useThemeClasses } from "@/modules/shared";

interface EditProfileButtonsProps {
  handleSave: () => void;
  isUploading: boolean;
  themeClasses: ReturnType<typeof useThemeClasses>;
  isLoading: boolean;
}

export const EditProfileButtons: React.FC<EditProfileButtonsProps> = ({
  handleSave,
  isUploading,
  themeClasses,
isLoading
}) => {
  return (
    <div className="flex flex-row gap-4 mt-6">
      <Button
        onClick={handleSave}
        disabled={isLoading}
        className={`bg-purple-500 hover:bg-pink-500 w-1/3 p-4 mx-auto ${themeClasses} ${
          isUploading ? "loading" : ""
        }`}
      >
        Save
      </Button>
      
    </div>
  );
};

import React, { useMemo } from 'react';
import { Layout } from './Layout';
import { ChatProvider } from '@/modules/chat/presentation/context/ChatContext';
import { useAuth } from '@/modules/auth/presentation/hooks/useAuth';
import { useUserSearch } from '@/modules/users/presentation/hooks/useUserSearch';
import { AuthDependency, UserSearchDependency, UserSearchResult } from '@/modules/chat/presentation/context/types/ChatTypes';

interface ChatLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const ChatLayout: React.FC<ChatLayoutProps> = ({ children, className }) => {
  const { user, isAuthenticated } = useAuth();
  const { searchUsersUseCase } = useUserSearch();

  // Créer les dépendances à injecter dans le ChatProvider
  const authDependency: AuthDependency = useMemo(
    () => ({
      currentUser: user
        ? {
            id: user.id,
            email: user.email.getValue(),
            username: user.username.getValue(),
          }
        : null,
      isAuthenticated,
    }),
    [user, isAuthenticated]
  );

  const userSearchDependency: UserSearchDependency = useMemo(
    () => ({
      searchUsers: async (term: string): Promise<UserSearchResult[]> => {
        try {
          const response = await searchUsersUseCase.execute({
            searchTerm: term,
          });

          if (!response.users) {
            return [];
          }

          // Mapper les résultats au format attendu par ChatContext
          const searchResults: UserSearchResult[] = response.users.map(
            (user) => ({
              id: user.id,
              username: user.username,
              email: user.email,
              avatar: user.avatar,
              isOnline: user.isOnline || false,
            })
          );

          return searchResults;
        } catch (error) {
          console.error("Erreur lors de la recherche d'utilisateurs:", error);
          return [];
        }
      },
    }),
    [searchUsersUseCase]
  );

  return (
    <ChatProvider auth={authDependency} userSearch={userSearchDependency}>
      <Layout className={className}>
        {children}
      </Layout>
    </ChatProvider>
  );
}; 
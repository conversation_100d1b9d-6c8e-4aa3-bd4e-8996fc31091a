import React, { useEffect, useRef, useState } from "react";
import {
  MessageCircle,
  LogOut,
  Sparkles,
  Search,
  Heart,
  X,
  ChevronDown,
  LogOutIcon,
  UserIcon,
  AlertCircle,
} from "lucide-react";
import { <PERSON><PERSON>, <PERSON>, CardHeader } from "@/modules/shared/presentation";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import { useNavigate, useLocation } from "react-router-dom";
import { DropdownMenuPortal } from "./DropdownMenuPortal";
import { User } from "@/modules/users/domain/entities/User";
import userApiRepository from "@/modules/users/infrastructure/api/UserApiRepository";

interface NavigationBarProps {
  onNavigateToChat: () => void;
  onNavigateToHome?: () => void;
}

export const NavigationBar: React.FC<NavigationBarProps> = ({
  onNavigateToChat,
  onNavigateToHome,
}) => {
  const { logout, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [userProfile, setUserProfile] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [menuOpen, setMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const userData = await userApiRepository.getConnectedUserProfile();
        setUserProfile(userData);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Erreur lors du chargement du profil"
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadUserProfile();
  }, []);

  // Fermer le menu si on clique en dehors
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
    }

    if (menuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuOpen]);

  const handleProfile = () => {
    navigate(`/connected-user-profile`);
    setTimeout(() => setMenuOpen(false), 0);
  };

  const handleLogout = () => {
    logout();
    navigate("/splash");
    setTimeout(() => setMenuOpen(false), 0);
  };

  const handleNavigateToSearch = () => {
    navigate("/search");
  };

  const handleNavigateToLiked = () => {
    navigate("/liked");
  };

  const handleNavigateToDisliked = () => {
    navigate("/disliked");
  };

  const handleNavigateToReport = () => {
    navigate("/report");
  };

  const isChatPage = location.pathname === "/chat";
  const isSearchPage = location.pathname === "/search";
  const isLikedPage = location.pathname === "/liked";
  const isDislikedPage = location.pathname === "/disliked";
  const isReportPage = location.pathname === "/report";

  // ...existing imports...
  // ...existing code...

  return (
    <div className="flex-shrink-0 relative h-screen">
      {/* Enhanced background shine effect */}
      {/* <div className="absolute inset-0 bg-gradient-to-r from-blue-500/15 via-purple-500/20 to-pink-500/15 blur-lg pointer-events-none"></div> */}

      <div
        className="relative h-screen shadow-2xl overflow-hidden"
        style={{
          background: `linear-gradient(135deg, 
            rgba(255,255,255,0.06) 0%, 
            rgba(255,255,255,0.03) 50%, 
            rgba(255,255,255,0.08) 100%
          )`,
          backdropFilter: "blur(40px)",
          WebkitBackdropFilter: "blur(40px)",
        }}
      >
        {/* Enhanced top reflection */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/60 to-transparent pointer-events-none"></div>

        {/* Floating shine effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/8 via-transparent to-white/4 pointer-events-none"></div>

        <CardHeader className="py-4 h-screen relative">
          <div className="flex flex-col h-screen justify-between items-stretch">
            <div className="flex flex-col gap-2">
              <div
                className="flex items-center space-x-4 cursor-pointer group transition-all duration-200 mb-8"
                onClick={onNavigateToHome}
              >
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    {/* Logo with enhanced halo effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400/40 to-purple-500/40 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-150 pointer-events-none"></div>

                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 border border-pink-500 rounded-full flex items-center justify-center">
                        <Heart className="text-pink-500" size={16} />
                      </div>
                      <span className="text-xl font-bold text-purple-700">
                        TooDiscreet
                      </span>
                    </div>

                    {/* Small decorative star with subtle animation */}
                    <Sparkles className="absolute -top-1 -right-1 h-3 w-3 text-yellow-400 animate-pulse drop-shadow-sm" />
                  </div>

                  
                </div>
              </div>

              <div className="flex flex-col gap-2 flex-1">
                {/* Go Premium button */}
                <Button
                  variant="default"
                  size="sm"
                  className="flex items-center justify-start h-[50px] space-x-2 transition-all duration-300 relative overflow-hidden backdrop-blur-sm bg-gradient-to-r from-pink-600/90 to-purple-600/90 text-white shadow-xl shadow-blue-500/30 border-0 ring-1 ring-white/20 hover:bg-gradient-to-r hover:from-blue-700/90 hover:to-purple-700/90"
                  onClick={() => navigate("/booking")}
                >
                  <Sparkles className="h-4 w-4" />
                  <span>Go Premium</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-white/15 to-transparent opacity-60 pointer-events-none"></div>
                </Button>
                {/* Messages button with enhanced glassmorphism */}
                <Button
                  variant={isChatPage ? "default" : "ghost"}
                  size="sm"
                  onClick={onNavigateToChat}
                  className={`flex justify-start items-center h-[50px] space-x-2 transition-all duration-300 relative overflow-hidden backdrop-blur-sm ${
                    isChatPage
                      ? "bg-gradient-to-r from-pink-600/90 to-purple-600/90 text-white shadow-xl shadow-blue-500/30 border-0 ring-1 ring-white/20"
                      : "text-black hover:text-black/80 hover:bg-white/8 border-white/15 hover:border-white/25 backdrop-blur-md"
                  }`}
                  style={!isChatPage ? { backdropFilter: "blur(20px)" } : {}}
                >
                  <MessageCircle className="h-4 w-4" />
                  <span>Messages</span>
                  {isChatPage && (
                    <div className="absolute inset-0 bg-gradient-to-r from-white/15 to-transparent opacity-60 pointer-events-none"></div>
                  )}
                </Button>

                {/* Search button */}
                <Button
                  variant={isSearchPage ? "default" : "ghost"}
                  size="sm"
                  onClick={handleNavigateToSearch}
                  className={`flex justify-start items-center h-[50px] space-x-2 transition-all duration-300 relative overflow-hidden backdrop-blur-sm ${
                    isSearchPage
                      ? "bg-gradient-to-r from-purple-600/90 to-pink-600/90 text-white shadow-xl shadow-purple-500/30 border-0 ring-1 ring-white/20"
                      : "text-black hover:text-black/80 hover:bg-white/8 border-white/15 hover:border-white/25 backdrop-blur-md"
                  }`}
                  style={!isSearchPage ? { backdropFilter: "blur(20px)" } : {}}
                >
                  <Search className="h-4 w-4" />
                  <span>Search</span>
                  {isSearchPage && (
                    <div className="absolute inset-0 bg-gradient-to-r from-white/15 to-transparent opacity-60 pointer-events-none"></div>
                  )}
                </Button>

                {/* Liked button */}
                <Button
                  variant={isLikedPage ? "default" : "ghost"}
                  size="sm"
                  onClick={handleNavigateToLiked}
                  className={`flex justify-start items-center h-[50px] space-x-2 transition-all duration-300 relative overflow-hidden backdrop-blur-sm ${
                    isLikedPage
                      ? "bg-gradient-to-r from-pink-600/90 to-red-600/90 text-white shadow-xl shadow-pink-500/30 border-0 ring-1 ring-white/20"
                      : "text-black hover:text-black/80 hover:bg-white/8 border-white/15 hover:border-white/25 backdrop-blur-md"
                  }`}
                  style={!isLikedPage ? { backdropFilter: "blur(20px)" } : {}}
                >
                  <Heart className="h-4 w-4" />
                  <span>Liked</span>
                  {isLikedPage && (
                    <div className="absolute inset-0 bg-gradient-to-r from-white/15 to-transparent opacity-60 pointer-events-none"></div>
                  )}
                </Button>

                {/* Disliked button */}
                <Button
                  variant={isDislikedPage ? "default" : "ghost"}
                  size="sm"
                  onClick={handleNavigateToDisliked}
                  className={`flex justify-start items-center h-[50px] space-x-2 transition-all duration-300 relative overflow-hidden backdrop-blur-sm ${
                    isDislikedPage
                      ? "bg-gradient-to-r from-red-600/90 to-gray-600/90 text-white shadow-xl shadow-red-500/30 border-0 ring-1 ring-white/20"
                      : "text-black hover:text-black/80 hover:bg-white/8 border-white/15 hover:border-white/25 backdrop-blur-md"
                  }`}
                  style={
                    !isDislikedPage ? { backdropFilter: "blur(20px)" } : {}
                  }
                >
                  <X className="h-4 w-4" />
                  <span>Disliked</span>
                  {isDislikedPage && (
                    <div className="absolute inset-0 bg-gradient-to-r from-white/15 to-transparent opacity-60 pointer-events-none"></div>
                  )}
                </Button>
                {/* Report button */}
                {user?.roles.includes("admin") && (
                  <Button
                    variant={isReportPage ? "default" : "ghost"}
                    size="sm"
                    onClick={handleNavigateToReport}
                    className={`flex justify-start items-center h-[50px] space-x-2 transition-all duration-300 relative overflow-hidden backdrop-blur-sm ${
                      isReportPage
                        ? "bg-gradient-to-r from-gray-600/90 to-black/90 text-white shadow-xl shadow-gray-500/30 border-0 ring-1 ring-white/20"
                        : "text-black hover:text-black/80 hover:bg-white/8 border-white/15 hover:border-white/25 backdrop-blur-md"
                    }`}
                    style={
                      !isReportPage ? { backdropFilter: "blur(20px)" } : {}
                    }
                  >
                    <AlertCircle className="h-4 w-4" />
                    <span>Report</span>
                    {isReportPage && (
                      <div className="absolute inset-0 bg-gradient-to-r from-white/15 to-transparent opacity-60 pointer-events-none"></div>
                    )}
                  </Button>
                )}
              </div>
            </div>
            <div className="flex flex-col mb-8 gap-4">
              {/* User menu with dropdown */}
              <Button
                onClick={handleLogout}
                variant="ghost"
                size="sm"
                className={`flex text-black w-full justify-start items-center h-[50px] gap-2 space-x-2 transition-all duration-300 relative overflow-hidden}`}
              >
                <LogOutIcon className="h-4 w-4 inline-block" />
                Logout
              </Button>
              <div
                className="relative flex cursor-pointer justify-start items-center gap-2"
                onClick={handleProfile}
              >
                {userProfile?.profilePhoto ? (
                  <img
                    src={userProfile?.profilePhoto}
                    alt="User"
                    className="w-10 h-10 rounded-full cursor-pointer"
                    onClick={() => setMenuOpen(!menuOpen)}
                  />
                ) : (
                  <div
                    className="w-10 h-10 rounded-full cursor-pointer flex items-center justify-center bg-gray-300"
                    onClick={() => setMenuOpen(!menuOpen)}
                  >
                    <UserIcon className="h-6 w-6 text-gray-500" />
                  </div>
                )}
                <div className="flex text-black flex-wrap gap-1">
                  <p>{userProfile?.firstName}</p>
                  <p>{userProfile?.lastName}</p>
                </div>
              </div>
            </div>
          </div>
        </CardHeader>

        {/* Enhanced subtle bottom reflection */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/40 to-transparent pointer-events-none"></div>

        {/* Subtle inner border for more depth */}
        <div className="absolute inset-0 rounded-lg ring-1 ring-inset ring-white/10 pointer-events-none"></div>
      </div>
    </div>
  );
  // ...existing code...
};

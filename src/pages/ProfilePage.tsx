import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ChatLayout } from "../components/ChatLayout";
import { Card, Button } from "@/modules/shared/presentation";
import { User, UserBlockStatus } from "@/modules/users/domain/entities/User";
import { userApiRepository } from "@/modules/users/infrastructure/api/UserApiRepository";
import {
  MessageCircle,
  Heart,
  UserPlus,
  Calendar,
  Users,
  Ruler,
  MapPin,
  Baby,
  User as UserIcon,
  X,
  Lock,
  Eye,
  Church,
} from "lucide-react";
import { useUserLikes } from "@/modules/users/presentation/hooks/useUserLikes";

const ProfilePage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"personal" | "appearance">(
    "personal"
  );
  const { likeUser } = useUserLikes();


  useEffect(() => {
    const loadUserProfile = async () => {
      if (!userId) {
        setError("Utilisateur non trouvé");
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const userData = await userApiRepository.getUserById(userId);
        setUser(userData);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Erreur lors du chargement du profil"
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadUserProfile();
  }, [userId]);

  const getInitials = (username: string): string => {
    return username
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getAvatarGradient = (userId: string) => {
    const gradients = [
      "from-blue-400 via-purple-500 to-pink-500",
      "from-green-400 via-blue-500 to-purple-600",
      "from-pink-400 via-red-500 to-yellow-500",
      "from-indigo-400 via-purple-500 to-pink-500",
      "from-cyan-400 via-blue-500 to-indigo-600",
      "from-orange-400 via-pink-500 to-red-500",
    ];
    const index = userId.length % gradients.length;
    return gradients[index];
  };

  const handleStartChat = () => {
    if (user) {
      // Rediriger vers la page de chat avec cet utilisateur
      navigate(`/chat?user=${user.id}`);
    }
  };

  const handleLike = async () => {
    if (userId) {
      await likeUser(userId);
      const userData = await userApiRepository.getUserById(userId);
      setUser(userData);
    }
  };

  if (isLoading) {
    return (
      <ChatLayout className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <Card className="p-8 border-0 shadow-2xl backdrop-blur-xl">
              <div className="text-center space-y-6">
                <div className="w-32 h-32 bg-white/20 rounded-full mx-auto"></div>
                <div className="h-8 bg-white/20 rounded-full w-48 mx-auto"></div>
                <div className="h-4 bg-white/20 rounded-full w-32 mx-auto"></div>
                <div className="flex justify-center space-x-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full"></div>
                  <div className="w-12 h-12 bg-white/20 rounded-full"></div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </ChatLayout>
    );
  }

  if (error) {
    return (
      <ChatLayout className="p-6">
        <div className="max-w-4xl mx-auto">
          <Card
            className="p-8 border-0 shadow-2xl backdrop-blur-xl"
            style={{
              background: `linear-gradient(135deg, 
                rgba(239, 68, 68, 0.1) 0%, 
                rgba(220, 38, 127, 0.05) 50%, 
                rgba(239, 68, 68, 0.1) 100%
              )`,
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(239, 68, 68, 0.2)",
            }}
          >
            <div className="text-center">
              <div className="text-red-400 mb-6 text-lg font-medium">
                {error}
              </div>
            </div>
          </Card>
        </div>
      </ChatLayout>
    );
  }

  if (!user) {
    return (
      <ChatLayout className="p-6">
        <div className="max-w-4xl mx-auto">
          <Card className="p-12 border-0 shadow-2xl backdrop-blur-xl">
            <div className="text-center text-white/80">
              <p className="text-xl font-semibold mb-3 text-white">
                User not found
              </p>
              <p className="text-sm mb-6 text-white/70">
                This user does not exist or has been deleted
              </p>
            </div>
          </Card>
        </div>
      </ChatLayout>
    );
  }

  return (
    <ChatLayout className="p-6 mt-12 lg:mt-0 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        {/* Profil principal */}
        <Card
          className="p-8 border-0 shadow-2xl backdrop-blur-xl mb-6"
          // style={{
          //   background: `linear-gradient(135deg, 
          //     rgba(255,255,255,0.1) 0%, 
          //     rgba(255,255,255,0.05) 50%, 
          //     rgba(255,255,255,0.1) 100%
          //   )`,
          //   backdropFilter: "blur(20px)",
          //   border: "1px solid rgba(255,255,255,0.1)",
          // }}
        >
          <div className="text-center">
            {/* Photo de profil */}
            <div className="flex justify-center mb-8">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-md opacity-30 scale-110"></div>

                {user.profilePhoto ? (
                  <img
                    src={user.profilePhoto}
                    alt={`${user.username} profile picture`}
                    className="w-32 h-32 rounded-full object-cover ring-4 ring-white/20 shadow-2xl relative z-10"
                  />
                ) : (
                  <div
                    className={`w-32 h-32 rounded-full bg-gradient-to-br ${getAvatarGradient(
                      user.id
                    )} flex items-center justify-center shadow-2xl relative z-10 ring-4 ring-white/20`}
                  >
                    <span className="text-3xl font-bold text-white drop-shadow-lg">
                      {getInitials(user.username)}
                    </span>
                  </div>
                )}

                {/* Indicateur en ligne */}
                {user.isOnline && (
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full ring-4 ring-black/20 shadow-lg z-20">
                    <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-40"></div>
                    <div className="absolute inset-1 bg-green-400 rounded-full"></div>
                  </div>
                )}
              </div>
            </div>

            {/* Informations utilisateur */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-black mb-3 drop-shadow-sm">
                {user.getFullName() || user.username}
              </h1>
              {user.getFullName() && (
                <p className="text-black/70 mb-3">@{user.username}</p>
              )}
              <div className="flex justify-center items-center mb-6">
                {user.isOnline ? (
                  <span className="text-green-300 font-semibold flex items-center space-x-2 bg-green-500/20 backdrop-blur-sm px-4 py-2 rounded-full border border-green-400/30">
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span>Online</span>
                  </span>
                ) : (
                  <span className="text-black/60 bg-black/10 backdrop-blur-sm px-4 py-2 rounded-full border border-white/20">
                    Offline
                  </span>
                )}
              </div>
            </div>

            {/* Boutons d'action */}
            <div className="flex flex-col md:flex-row justify-center space-y-4 md:space-y-0 md:space-x-6">
              {
                user.blockStatus !== UserBlockStatus.BLOCKED && (
                  <>
                  <Button
                size="lg"
                className="bg-gradient-to-r from-pink-500 via-rose-500 to-red-500 hover:from-pink-400 hover:via-rose-400 hover:to-red-400 shadow-2xl hover:shadow-pink-500/25 transition-all duration-300 hover:scale-105 flex items-center space-x-3 px-6 py-3"
                onClick={handleStartChat}
              >
                <MessageCircle className="h-5 w-5" />
                <span>Start a conversation</span>
              </Button>
              {user.interactionType === "NONE" && (
                <Button
                  onClick={handleLike}
                  variant="outline"
                  size="lg"
                  className="border-2 border-black/30 hover:border-red-400 hover:bg-red-500/20 text-black/90 hover:text-purle-400 backdrop-blur-sm transition-all duration-300 hover:scale-105 flex items-center space-x-3 px-6 py-3"
                >
                  <Heart className="h-5 w-5" />
                  <span>Like</span>
                </Button>
              )}
                  </>
                )
                  
              }
              {user.blockStatus === UserBlockStatus.BLOCKED ? (
                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-white/30 bg-red-500/20 hover:border-red-400 hover:bg-red-500/20 text-black/90 hover:text-white backdrop-blur-sm transition-all duration-300 hover:scale-105 flex items-center space-x-3 px-6 py-3"
                >
                  <Lock className="h-5 w-5" />
                  <span>This user is blocked</span>
                </Button>
              ) : user.blockStatus === UserBlockStatus.PENDING ? (
                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-black/30 bg-yellow-500/20 hover:border-red-400 hover:bg-red-500/20 text-black/90 hover:text-white backdrop-blur-sm transition-all duration-300 hover:scale-105 flex items-center space-x-3 px-6 py-3"
                >
                  <Lock className="h-5 w-5" />
                  <span>Pending report</span>
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-black/30 hover:border-purple-400 hover:bg-purple-500/20 text-black/90 hover:text-black backdrop-blur-sm transition-all duration-300 hover:scale-105 flex items-center space-x-3 px-6 py-3"
                  onClick={() => navigate("/report")}
                >
                  <X className="h-5 w-5" />
                  <span>Report</span>
                </Button>
              )}
            </div>
          </div>
        </Card>

        {/* Onglets */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-white/10 backdrop-blur-xl rounded-lg p-1 border border-white/20">
            <button
              onClick={() => setActiveTab("personal")}
              className={`flex-1 py-3 px-6 rounded-md font-medium transition-all duration-300 flex items-center justify-center space-x-2 ${
                activeTab === "personal"
                  ? "bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-lg"
                  : "text-black/70 bg-black/10 hover:text-white hover:bg-black/10"
              }`}
            >
              <UserIcon className="h-4 w-4" />
              <span>Personal Information</span>
            </button>
            <button
              onClick={() => setActiveTab("appearance")}
              className={`flex-1 py-3 px-6 rounded-md font-medium transition-all duration-300 flex items-center justify-center space-x-2 ${
                activeTab === "appearance"
                  ? "bg-gradient-to-r from-purple-500 to-pink-600 text-white shadow-lg"
                  : "text-black/70 bg-black/10 hover:text-white hover:bg-black/10"
              }`}
            >
              <Users className="h-4 w-4" />
              <span>Appearance</span>
            </button>
          </div>
        </div>

        {/* Contenu des onglets */}
        {activeTab === "personal" && (
          <Card
            className="p-6 border-0 shadow-xl backdrop-blur-xl"
            // style={{
            //   background: `linear-gradient(135deg, 
            //     rgba(59, 130, 246, 0.1) 0%, 
            //     rgba(147, 51, 234, 0.05) 50%, 
            //     rgba(59, 130, 246, 0.1) 100%
            //   )`,
            //   backdropFilter: "blur(20px)",
            //   border: "1px solid rgba(59, 130, 246, 0.2)",
            // }}
          >
            <h3 className="text-lg font-semibold text-black mb-6 flex items-center space-x-2">
              <UserIcon className="h-5 w-5 text-pink-400" />
              <span>Personal Information</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {user.firstName && (
                <div className="flex items-center space-x-3">
                  <UserPlus className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">First Name</p>
                    <p className="text-black font-medium">{user.firstName}</p>
                  </div>
                </div>
              )}

              {user.lastName && (
                <div className="flex items-center space-x-3">
                  <UserPlus className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Last Name</p>
                    <p className="text-black font-medium">{user.lastName}</p>
                  </div>
                </div>
              )}

              {user.getAge() && (
                <div className="flex items-center space-x-3">
                  <Baby className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Age</p>
                    <p className="text-black font-medium">
                      {user.getAge()} years old
                    </p>
                  </div>
                </div>
              )}

              {user.getGenderDisplay() && (
                <div className="flex items-center space-x-3">
                  <Users className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Gender</p>
                    <p className="text-black font-medium">
                      {user.getGenderDisplay()}
                    </p>
                  </div>
                </div>
              )}

              {user.country && (
                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Country</p>
                    <p className="text-black font-medium">{user.country}</p>
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-black/60" />
                <div>
                  <p className="text-sm text-black/60">Member since</p>
                  <p className="text-black font-medium">Recently</p>
                </div>
              </div>
            </div>

            {!user.firstName &&
              !user.lastName &&
              !user.getAge() &&
              !user.getGenderDisplay() &&
              !user.country && (
                <div className="text-center py-8">
                  <UserIcon className="h-12 w-12 mx-auto text-black/30 mb-4" />
                  <p className="text-black/60">
                    No personal information available
                  </p>
                </div>
              )}
          </Card>
        )}

        {activeTab === "appearance" && (
          <Card
            className="p-6 border-0 shadow-xl backdrop-blur-xl"
            // style={{
            //   background: `linear-gradient(135deg, 
            //     rgba(236, 72, 153, 0.1) 0%, 
            //     rgba(147, 51, 234, 0.05) 50%, 
            //     rgba(236, 72, 153, 0.1) 100%
            //   )`,
            //   backdropFilter: "blur(20px)",
            //   border: "1px solid rgba(236, 72, 153, 0.2)",
            // }}
          >
            <h3 className="text-lg font-semibold text-black mb-6 flex items-center space-x-2">
              <Users className="h-5 w-5 text-purple-400" />
              <span>Appearance</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {user.getEthnicityDisplay() && (
                <div className="flex items-center space-x-3">
                  <Users className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Ethnicity</p>
                    <p className="text-black font-medium">
                      {user.getEthnicityDisplay()}
                    </p>
                  </div>
                </div>
              )}

              {user.getHeightDisplay() && (
                <div className="flex items-center space-x-3">
                  <Ruler className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Height</p>
                    <p className="text-black font-medium">
                      {user.getHeightDisplay()}
                    </p>
                  </div>
                </div>
              )}
              {user.getHairColorDisplay() && (
                <div className="flex items-center space-x-3">
                  <Users className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Hair Color</p>
                    <p className="text-black font-medium">
                      {user.getHairColorDisplay()}
                    </p>
                  </div>
                </div>
              )}
              {user.getEyeColorDisplay() && (
                <div className="flex items-center space-x-3">
                  <Eye className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Eye Color</p>
                    <p className="text-black font-medium">
                      {user.getEyeColorDisplay()}
                    </p>
                  </div>
                </div>
              )}
              {user.getBodyColorDisplay() && (
                <div className="flex items-center space-x-3">
                  <Users className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Body Color</p>
                    <p className="text-black font-medium">
                      {user.getBodyColorDisplay()}
                    </p>
                  </div>
                </div>
              )}
              {user.getBodyTypeDisplay() && (
                <div className="flex items-center space-x-3">
                  <Users className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Body Type</p>
                    <p className="text-black font-medium">
                      {user.getBodyTypeDisplay()}
                    </p>
                  </div>
                </div>
              )}
              {user.getReligionDisplay() && (
                <div className="flex items-center space-x-3">
                  <Church className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Religion</p>
                    <p className="text-black font-medium">
                      {user.getReligionDisplay()}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {!user.getEthnicityDisplay() && !user.getHeightDisplay() && (
              <div className="text-center py-8">
                <Users className="h-12 w-12 mx-auto text-black/30 mb-4" />
                <p className="text-black/60">
                  No appearance information available
                </p>
              </div>
            )}
          </Card>
        )}
      </div>
    </ChatLayout>
  );
};

export default ProfilePage;

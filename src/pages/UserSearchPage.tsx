import React from "react";
import { useNavigate } from "react-router-dom";
import { ChatLayout } from "../components/ChatLayout";
import { UserSuggestionsWithSearch } from "../components/UserSuggestionsWithSearch";
import { User } from "@/modules/users/domain/entities/User";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import userApiRepository from "@/modules/users/infrastructure/api/UserApiRepository";
import { Loader2 } from "lucide-react";

const UserSearchPage: React.FC = () => {
  const navigate = useNavigate();
  const [openModalReportUser, setOpenModalReportUser] = React.useState(false);
  const [reportDescription, setReportDescription] = React.useState("");
  const [userReport, setUserreport] = React.useState<User | null>(null);
  const [isReporting, setIsReporting] = React.useState(false);
  const { user } = useAuth();

  const handleStartChat = (user: User) => {
    // Naviguer vers la page de chat avec l'utilisateur sélectionné
    navigate("/chat", {
      state: {
        startChatWith: {
          id: user.id,
          username: user.username,
          email: user.email,
          avatar: user.avatar,
          isOnline: user.isOnline,
        },
      },
    });
  };

  const handleOpenModalReportUser = (user: User) => {
    setOpenModalReportUser(true);
    setUserreport(user);
  };

  // Report user search modal

  const handleReportUser = async (): Promise<void> => {
    if (!userReport || !user) {
      console.error("User or userReport is not defined");
      return;
    }
    setIsReporting(true);
    try {
      const data = {
        reportedByID: user?.id || "",
        reportedToID: userReport?.id || "",
        reason: reportDescription || "",
      };
      console.log("DATA", data);
      await userApiRepository.reportUser(data);
      setTimeout(() => {
        setOpenModalReportUser(false);
        setReportDescription("");
        setUserreport(null);
        setIsReporting(false);
      }, 5000);
      // Logique pour signaler l'utilisateur
      console.log(`Reporting user: ${userReport?.username}`);
      // Vous pouvez appeler une API ou une fonction pour gérer le signalement ici
    } catch (error) {
      console.error("Erreur lors du signalement de l'utilisateur:", error);
      setIsReporting(false);
    }
  };

  return (
    <ChatLayout className="p-6 overflow-y-auto">
      <div className="max-w-7xl mx-auto">
        <UserSuggestionsWithSearch
          onStartChat={handleStartChat}
          onReportUser={handleOpenModalReportUser}
        />
      </div>
      {/* Modal de signalement d'utilisateur */}
      {openModalReportUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white/100 p-8 rounded-2xl shadow-2xl w-full max-w-md">
            <h2 className="text-2xl font-bold mb-4 text-gray-900">
              Report User
            </h2>
            <label
              className="block mb-2 text-gray-700 font-medium"
              htmlFor="report-description"
            >
              Please describe the reason for reporting:
            </label>
            <textarea
              id="report-description"
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 text-gray-900 bg-white mb-6"
              rows={4}
              placeholder="Describe the issue..."
              value={reportDescription}
              onChange={(e) => setReportDescription(e.target.value)}
            />
            <div className="mt-4 flex justify-end space-x-2">
              <button
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                onClick={() => setOpenModalReportUser(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-60"
                disabled={isReporting}
                onClick={handleReportUser}
              >
                {isReporting ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  "Report"
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </ChatLayout>
  );
};

export default UserSearchPage;

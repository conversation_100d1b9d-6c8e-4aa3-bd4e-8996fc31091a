import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, CardContent, Button } from '@/modules/shared/presentation';
import { ChatLayout } from '../components/ChatLayout';
import { useUserLikes } from '@/modules/users/presentation/hooks/useUserLikes';
import { MessageCircle, Heart, Loader2, Users, X } from 'lucide-react';

const LikedUsersPage: React.FC = () => {
  const navigate = useNavigate();
  const { 
    likedUsers, 
    isLoadingLiked, 
    errorLiked, 
    loadLikedUsers,
    removeUserInteraction,
    loadingUsers
  } = useUserLikes();

  useEffect(() => {
    loadLikedUsers();
  }, [loadLikedUsers]);

  const handleStartChat = (user: any) => {
    // Naviguer vers la page de chat avec l'utilisateur sélectionné
    navigate('/chat', {
      state: {
        startChatWith: {
          id: user.id,
          username: user.username,
          email: user.email,
          avatar: user.avatar,
          isOnline: user.isOnline
        }
      }
    });
  };

  const handleUserClick = (user: any) => {
    navigate(`/profile/${user.id}`);
  };

  const getInitials = (username: string): string => {
    return username
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const avatarGradients = [
    'from-blue-400 via-purple-500 to-pink-500',
    'from-green-400 via-blue-500 to-purple-600',
    'from-pink-400 via-red-500 to-yellow-500',
    'from-indigo-400 via-purple-500 to-pink-500',
    'from-cyan-400 via-blue-500 to-indigo-600',
    'from-orange-400 via-pink-500 to-red-500'
  ];

  const getAvatarGradient = (userId: string) => {
    const index = userId.length % avatarGradients.length;
    return avatarGradients[index];
  };

  const calculateAge = (birthdate?: Date): number | null => {
    if (!birthdate) return null;
    const today = new Date();
    const birth = new Date(birthdate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const handleRemoveUser = async (user: any, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await removeUserInteraction(user.id);
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
    }
  };

  if (isLoadingLiked) {
    return (
      <ChatLayout className="p-6 overflow-y-auto">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-pink-400" />
          </div>
        </div>
      </ChatLayout>
    );
  }

  if (errorLiked) {
    return (
      <ChatLayout className="p-6 overflow-y-auto">
        <div className="max-w-7xl mx-auto">
          <Card className="p-8 border-0 shadow-xl backdrop-blur-xl bg-red-500/20 border-red-400/40">
            <div className="text-center text-red-300">
              {errorLiked}
            </div>
          </Card>
        </div>
      </ChatLayout>
    );
  }

  return (
    <ChatLayout className="p-6 overflow-y-auto">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* En-tête */}
        <div className="flex mt-12 lg:mt-0 items-center justify-between">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-400 via-blue-400 to-pink-500 bg-clip-text text-transparent flex items-center space-x-3">
            <Heart className="h-8 w-8 text-pink-400" />
            <span>People I Like</span>
          </h2>
        </div>

        {/* Contenu */}
        {likedUsers.length === 0 ? (
          <Card className="p-12 border-0 shadow-xl backdrop-blur-xl bg-pink-500/20 border-pink-400/30">
            <div className="text-center">
              <Heart className="h-16 w-16 mx-auto mb-6 text-pink-300" />
              <p className="text-xl font-semibold mb-3 text-pink-300">No liked users at the moment</p>
              <p className="text-sm mb-6 text-pink-300/80">
                Explore suggestions and search to discover interesting people!
              </p>
              <div className="flex justify-center space-x-4">
                <Button
                  onClick={() => navigate('/')}
                  className="bg-pink-600 hover:bg-pink-700 text-white border-0"
                >
                  <Users className="h-4 w-4 mr-2" />
                  View Suggestions
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate('/search')}
                  className="border-pink-400/40 hover:bg-pink-500/20 text-pink-200 hover:text-pink-100"
                >
                  Search
                </Button>
              </div>
            </div>
          </Card>
        ) : (
          <div className="space-y-6">
            <div className="text-pink-500 text-sm font-medium">
              {likedUsers.length} person{likedUsers.length > 1 ? 's' : ''} liked
            </div>
            
            <div className="grid grid-cols-1  md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {likedUsers.map((user) => (
                <Card 
                  key={user.id} 
                  className="group relative overflow-hidden border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 hover:scale-105 cursor-pointer backdrop-blur-xl bg-white border-pink-400/20 hover:bg-white/90"
                  onClick={() => handleUserClick(user)}
                >
                  <CardContent className="p-4">
                    {/* Photo de profil */}
                    <div className="flex justify-center mb-4">
                      <div className="relative mt-6">
                        {user.profilePhoto ? (
                          <img
                            src={user.profilePhoto}
                            alt={`Profil de ${user.username}`}
                            className="w-16 h-16 rounded-full object-cover ring-2 ring-pink-400/40 shadow-lg"
                          />
                        ) : (
                          <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${getAvatarGradient(user.id)} flex items-center justify-center shadow-lg ring-2 ring-pink-400/40`}>
                            <span className="text-lg font-bold text-white">
                              {getInitials(user.username)}
                            </span>
                          </div>
                        )}
                        
                        {user.isOnline && (
                          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full ring-2 ring-black/20">
                            <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-40"></div>
                          </div>
                        )}

                        {/* Indicateur de like */}
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center ring-2 ring-white/20">
                          <Heart className="h-3 w-3 text-white fill-white" />
                        </div>
                      </div>
                    </div>
                    
                    {/* Informations utilisateur */}
                    <div className="text-center space-y-2">
                      <h3 className="font-semibold text-pink-500 text-sm truncate" title={user.username}>
                        {user.lastName} {user.firstName}
                      </h3>
                      
                      {user.birthdate && (
                        <p className="text-xs text-pink-500/80">{calculateAge(user.birthdate)} ans</p>
                      )}
                      
                      {user.country && (
                        <p className="text-xs text-pink-500/80">{user.country}</p>
                      )}
                    </div>
                    
                    {/* Boutons d'actions */}
                    <div className="flex justify-center space-x-2 mt-4">
                      <Button
                        size="sm"
                        className="flex-1 bg-gradient-to-r from-pink-500 to-red-500 hover:from-pink-400 hover:to-red-400 text-white"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStartChat(user);
                        }}
                      >
                        <MessageCircle className="h-3 w-3 mr-2" />
                        Message
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-pink-400/40 hover:bg-pink-500/20 text-pink-200 hover:text-pink-300"
                        onClick={(e) => handleRemoveUser(user, e)}
                        disabled={loadingUsers.has(user.id)}
                      >
                        {loadingUsers.has(user.id) ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          <X className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </ChatLayout>
  );
};

export default LikedUsersPage; 
import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, CardContent, But<PERSON> } from "@/modules/shared/presentation";
import { ChatLayout } from "../components/ChatLayout";
import { useUserLikes } from "@/modules/users/presentation/hooks/useUserLikes";
import { X, Loader2, Users, RotateCcw } from "lucide-react";

const DislikedUsersPage: React.FC = () => {
  const navigate = useNavigate();
  const {
    dislikedUsers,
    isLoadingDisliked,
    errorDisliked,
    loadDislikedUsers,
    removeUserInteraction,
    loadingUsers,
  } = useUserLikes();

  useEffect(() => {
    loadDislikedUsers();
  }, [loadDislikedUsers]);

  const handleUserClick = (user: any) => {
    navigate(`/profile/${user.id}`);
  };

  const handleRemoveUser = async (user: any, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await removeUserInteraction(user.id);
    } catch (error) {
      console.error("Erreur lors de la suppression:", error);
    }
  };

  const getInitials = (username: string): string => {
    return username
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const avatarGradients = [
    "from-blue-400 via-purple-500 to-pink-500",
    "from-green-400 via-blue-500 to-purple-600",
    "from-pink-400 via-red-500 to-yellow-500",
    "from-indigo-400 via-purple-500 to-pink-500",
    "from-cyan-400 via-blue-500 to-indigo-600",
    "from-orange-400 via-pink-500 to-red-500",
  ];

  const getAvatarGradient = (userId: string) => {
    const index = userId.length % avatarGradients.length;
    return avatarGradients[index];
  };

  const calculateAge = (birthdate?: Date): number | null => {
    if (!birthdate) return null;
    const today = new Date();
    const birth = new Date(birthdate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birth.getDate())
    ) {
      age--;
    }

    return age;
  };

  if (isLoadingDisliked) {
    return (
      <ChatLayout className="p-6 overflow-y-auto">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-red-400" />
          </div>
        </div>
      </ChatLayout>
    );
  }

  if (errorDisliked) {
    return (
      <ChatLayout className="p-6 overflow-y-auto">
        <div className="max-w-7xl mx-auto">
          <Card className="p-8 border-0 shadow-xl backdrop-blur-xl bg-red-500/20 border-red-400/40">
            <div className="text-center text-red-300">{errorDisliked}</div>
          </Card>
        </div>
      </ChatLayout>
    );
  }

  return (
    <ChatLayout className="p-6 overflow-y-auto">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* En-tête */}
        <div className="flex mt-12 lg:mt-0 items-center justify-between">
          <h2 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-red-700 via-red-500 to-red-600 bg-clip-text text-transparent flex items-center space-x-3">
            <X className="h-8 w-8 text-red-400" />
            <span>People I Dislike</span>
          </h2>
        </div>

        {/* Contenu */}
        {dislikedUsers.length === 0 ? (
          <Card className="p-12 border-0 shadow-xl backdrop-blur-xl bg-red-500/20 border-red-400/30">
            <div className="text-center">
              <X className="h-16 w-16 mx-auto mb-6 text-red-300" />
              <p className="text-xl font-semibold mb-3 text-red-300">
                No disliked person yet
              </p>
              <p className="text-sm mb-6 text-red-300/80">
                This list will populate as you explore.
              </p>
              <div className="flex flex-col lg:flex-row space-y-4 lg:space-y-0 justify-center lg:space-x-4">
                <Button
                  onClick={() => navigate("/")}
                  className="bg-pink-600 hover:bg-blue-700 text-white border-0 w-full"
                >
                  <Users className="h-4 w-4 mr-2" />
                  See suggestions
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate("/search")}
                  className="border-red-400/40 hover:bg-red-500/20 text-red-200 hover:text-red-100 w-full"
                >
                  Search
                </Button>
              </div>
            </div>
          </Card>
        ) : (
          <div className="space-y-6">
            <div className="text-red-500 text-sm font-medium">
              {dislikedUsers.length} disliked{" "}
              {dislikedUsers.length === 1 ? "person" : "people"}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {dislikedUsers.map((user) => (
                <Card
                  key={user.id}
                  className="group relative overflow-hidden border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 hover:scale-105 cursor-pointer backdrop-blur-xl bg-white border-red-400/20 opacity-60 hover:opacity-80"
                  onClick={() => handleUserClick(user)}
                >
                  <CardContent className="p-4">
                    {/* Photo de profil */}
                    <div className="flex justify-center mb-4">
                      <div className="relative mt-6">
                        {user.profilePhoto ? (
                          <img
                            src={user.profilePhoto}
                            alt={`Profil de ${user.username}`}
                            className="w-16 h-16 rounded-full object-cover ring-2 ring-red-400/40 shadow-lg grayscale"
                          />
                        ) : (
                          <div
                            className={`w-16 h-16 rounded-full bg-gradient-to-br ${getAvatarGradient(
                              user.id
                            )} flex items-center justify-center shadow-lg ring-2 ring-red-400/40 grayscale`}
                          >
                            <span className="text-lg font-bold text-white">
                              {getInitials(user.username)}
                            </span>
                          </div>
                        )}

                        {user.isOnline && (
                          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full ring-2 ring-black/20">
                            <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-40"></div>
                          </div>
                        )}

                        {/* Indicateur de dislike */}
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center ring-2 ring-white/20">
                          <X className="h-3 w-3 text-white" />
                        </div>
                      </div>
                    </div>

                    {/* Informations utilisateur */}
                    <div className="text-center space-y-2">
                      <h3
                        className="font-semibold text-black text-sm truncate"
                        title={user.username}
                      >
                        {user.lastName} {user.firstName}
                      </h3>

                      {user.birthdate && (
                        <p className="text-xs text-black/80">
                          {calculateAge(user.birthdate)} years old
                        </p>
                      )}

                      {user.country && (
                        <p className="text-xs text-black/80">
                          {user.country}
                        </p>
                      )}
                    </div>

                    {/* Bouton de suppression */}
                    <div className="flex justify-center mt-4">
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-red-400/60 hover:bg-red-500/40 text-red-400 hover:text-red-500"
                        onClick={(e) => handleRemoveUser(user, e)}
                        disabled={loadingUsers.has(user.id)}
                      >
                        {loadingUsers.has(user.id) ? (
                          <Loader2 className="h-3 w-3 animate-spin mr-2" />
                        ) : (
                          <RotateCcw className="h-3 w-3 mr-2" />
                        )}
                        Remove
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </ChatLayout>
  );
};

export default DislikedUsersPage;

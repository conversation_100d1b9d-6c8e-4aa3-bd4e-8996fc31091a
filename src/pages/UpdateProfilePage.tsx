import { ChatLayout } from "@/components/ChatLayout";
import { EditProfile } from "@/components/EditProfile";
import { User } from "@/modules/users/domain/entities/User";
import userApiRepository from "@/modules/users/infrastructure/api/UserApiRepository";
import { User2Icon } from "lucide-react";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

export const UpdateProfilePage = () => {
  const { userId } = useParams<{ userId: string }>();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadUserProfile = async () => {
      if (!userId) {
        setError("Utilisateur non trouvé");
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const userData = await userApiRepository.getUserById(userId);
        setUser(userData);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Erreur lors du chargement du profil"
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadUserProfile();
  }, [userId]);
  return (
    <ChatLayout className="flex flex-col mt-12 lg:mt-0">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-pink-400 via-red-400 to-pink-500 bg-clip-text text-transparent text-transparent flex items-center space-x-3">
          <User2Icon className="h-8 w-8 text-pink-400" />
          <span>Edit my profile</span>
        </h2>
      </div>
      <EditProfile user={user} userId={userId} />
    </ChatLayout>
  );
};

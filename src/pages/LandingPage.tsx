import AuthNav from "@/components/AuthNav";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useMatchForm } from "@/modules/shared/presentation/hooks/useMatchForm";
import {
  Heart,
  Lock,
  Mail,
  MapPin,
  MessageCircle,
  Phone,
  Shield,
  UserPlus,
  Users,
} from "lucide-react";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";

function LandingPage() {
  const { form, handleChange } = useMatchForm();
  const navigate = useNavigate();
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    navigate("/signup", { state: form });

    e.preventDefault();
    console.log(form);
  };
  return (
    <>
      <AuthNav />
      <div className="min-h-screen">
        {/* Hero Section */}
        <section className="relative min-h-screen z-10 flex flex-col items-start overflow-hidden pt-16">
          <div className="absolute inset-0">
            <img
              src="/assets/bg/bg3.png"
              alt="Portrait woman"
              className="w-full h-full object-cover object-center"
            />
            <div className="absolute inset-0 bg-black/20"></div>
          </div>

          <div className="relative z-10 px-4 py-8">
            <div className="text-white order-1 mt-12">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 leading-tight">
                Find new and
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-purple-400">
                  interesting
                </span>
                <br />
                people
              </h1>
              <p className="text-lg sm:text-xl mb-6 sm:mb-8 text-white/90 max-w-md">
                Join TooDiscreet, where you could meet anyone, anywhere! It is a
                complete fun to find a perfect match for you and continue to
                hook up.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/signup">
                  <Button className="w-full sm:w-auto bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:from-pink-600 hover:to-purple-700 rounded-full px-6 sm:px-8 py-3 text-lg">
                    Get Started
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  className="w-full bg-transparent sm:w-auto border-2 border-white text-white  hover:text-gray-900 rounded-full px-6 sm:px-8 py-3 text-lg"
                >
                  Know More
                </Button>
              </div>
            </div>
          </div>

          {/* Registration Form - Second on mobile, second on desktop */}
          <div className="md:absolute -bottom-2 !z-50 left-0 order-2 w-full md:w-1/2 mx-auto lg:max-w-none">
            <div className="bg-gradient-to-tl from-pink-500 to-purple-600 p-6 sm:p-8">
              <h3 className="text-white text-xl sm:text-2xl font-bold mb-4 sm:mb-6">
                Let's Begin Finding Matches
              </h3>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white/80 text-sm mb-2">
                      I am a
                    </label>
                    <select name="gender" onChange={handleChange} value={form.gender} className="w-full px-3 sm:px-4 py-2 sm:py-3 bg-white/20 text-white placeholder-white/70 border border-white/30 focus:outline-none focus:border-white text-sm sm:text-base">
                      <option className="text-black" value="">
                        Select
                      </option>
                      <option className="text-black" value="man">
                        Man
                      </option>
                      <option className="text-black" value="woman">
                        Woman
                      </option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-white/80 text-sm mb-2">
                      Looking for a
                    </label>
                    <select name="lookingFor" onChange={handleChange} value={form.lookingFor} className="w-full px-3 sm:px-4 py-2 sm:py-3 bg-white/20 text-white placeholder-white/70 border border-white/30 focus:outline-none focus:border-white text-sm sm:text-base">
                      <option className="text-black" value="">
                        Select
                      </option>
                      <option className="text-black" value="man">
                        Man
                      </option>
                      <option className="text-black" value="woman">
                        Woman
                      </option>
                    </select>
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white/80 text-sm mb-2">
                      Between Ages
                    </label>
                    <select name="ageFrom" onChange={handleChange} value={form.ageFrom} className="w-full px-3 sm:px-4 py-2 sm:py-3 bg-white/20 text-white placeholder-white/70 border border-white/30 focus:outline-none focus:border-white text-sm sm:text-base">
                      {Array.from({ length: 80 }, (_, i) => (
                        <option className="text-black" key={i} value={i + 18}>
                          {i + 18}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-white/80 text-sm mb-2">
                      and
                    </label>
                    <select name="ageTo" onChange={handleChange} value={form.ageTo} className="w-full px-3 sm:px-4 py-2 sm:py-3 bg-white/20 text-white placeholder-white/70 border border-white/30 focus:outline-none focus:border-white text-sm sm:text-base">
                      {Array.from({ length: 80 }, (_, i) => (
                        <option className="text-black" key={i} value={i + 18}>
                          {i + 18}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <Button className="w-full md:w-auto bg-black/20 text-white hover:bg-gray-100 hover:text-black rounded-full py-3 mt-12 md:py-4 text-base sm:text-lg font-normal">
                  Let's Begin Finding Matches
                </Button>
              </form>
            </div>
          </div>
        </section>

        {/* Best Dating Website Section */}
        <section className="py-12 sm:py-16 z-0 lg:py-20 bg-gradient-to-br from-pink-50 to-purple-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-3xl mx-auto">
              <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-800 mb-4 sm:mb-6">
                Best dating website
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600">
                  for any age
                </span>
              </h2>
              <p className="text-lg sm:text-xl text-gray-600 leading-relaxed">
                Join TooDiscreet, where you could meet anyone, anywhere! It is a
                complete fun to find a perfect match for you and continue to
                hook up.
              </p>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section className="px-6 md:px-0 py-12 sm:py-16 lg:py-20 bg-white">
          <div className="w-full">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-0 items-center">
              <div className="order-1 flex flex-col pr-0 md:pr-6 justify-center md:ml-32 lg:order-1">
                <p className="text-purple-600 text-lg font-semibold mb-4">
                  How it works
                </p>
                <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-800 mb-6 sm:mb-8 leading-tight">
                  We have made it easy for you to have fun while you use our
                  TooDiscreet platform.
                </h2>
              </div>

              <div className="order-2 lg:order-2">
                <div className="bg-gradient-to-br from-pink-500 to-purple-600 px-6 md:px-8 py-6 md:py-20">
                  <div className="space-y-8 md:space-y-20">
                    <div className="flex items-start space-x-4">
                      <div className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-full flex-shrink-0">
                        <UserPlus className="text-white" size={20} />
                      </div>
                      <div>
                        <h3 className="text-white text-lg sm:text-xl font-bold mb-2">
                          Create Account
                        </h3>
                        <p className="text-white/80 text-sm sm:text-base">
                          Create your account and start meeting others. Connect
                          with people and start dating today.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-full flex-shrink-0">
                        <Users className="text-white" size={20} />
                      </div>
                      <div>
                        <h3 className="text-white text-lg sm:text-xl font-bold mb-2">
                          Find Matches
                        </h3>
                        <p className="text-white/80 text-sm sm:text-base">
                          Browse through profiles and find people you are
                          excited to meet and connect with.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-full flex-shrink-0">
                        <MessageCircle className="text-white" size={20} />
                      </div>
                      <div>
                        <h3 className="text-white text-lg sm:text-xl font-bold mb-2">
                          Start Dating
                        </h3>
                        <p className="text-white/80 text-sm sm:text-base">
                          Start chatting and get to know each other. Meet up and
                          enjoy your time together.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="order-4 lg:order-3">
                <div className="bg-gradient-to-br from-pink-500 to-purple-600 px-6 md:px-8 py-6 md:py-20">
                  <div className="space-y-8 md:space-y-20">
                    <div className="flex items-start space-x-4">
                      <div className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-full flex-shrink-0">
                        <Heart className="text-white" size={20} />
                      </div>
                      <div>
                        <h3 className="text-white text-lg sm:text-xl font-bold mb-2">
                          Find your best match
                        </h3>
                        <p className="text-white/80 text-sm sm:text-base">
                          Quality first
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-full flex-shrink-0">
                        <Shield className="text-white" size={20} />
                      </div>
                      <div>
                        <h3 className="text-white text-lg sm:text-xl font-bold mb-2">
                          Fully secure & encrypted
                        </h3>
                        <p className="text-white/80 text-sm sm:text-base">
                          Security first
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-full flex-shrink-0">
                        <Lock className="text-white" size={20} />
                      </div>
                      <div>
                        <h3 className="text-white text-lg sm:text-xl font-bold mb-2">
                          100% data privacy
                        </h3>
                        <p className="text-white/80 text-sm sm:text-base">
                          Your data is safe with us. We never share your
                          personal information with anyone.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="order-3 pl-0 md:pl-6 lg:order-4 w-full md:w-2/3">
                <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-800 mb-6 sm:mb-8 leading-tight">
                  Why TooDiscreet is the
                  <br />
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600">
                    best platform?
                  </span>
                </h2>
                <p className="text-lg sm:text-xl text-gray-600 leading-relaxed">
                  TooDiscreet, where you could meet anyone digitally! It is a
                  complete fun to find a perfect match for you and continue to
                  hook up. Real-time messaging & lot of features that keeps you
                  connected with your love buddies 24/7.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-12 sm:py-16 lg:py-20 bg-white">
          <div className="w-full px-4 sm:px-6 lg:px-8 text-center">
            <p className="text-purple-600 font-semibold mb-4">
              Anytime & Anywhere
            </p>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-800 mb-6 sm:mb-8 leading-tight">
              Connect with your perfect
              <br />
              Soulmate here, on TooDiscreet.
            </h2>
            <Link to="/signup">
              <Button className="bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:from-pink-600 hover:to-purple-700 rounded-full px-6 sm:px-8 py-3 sm:py-4 text-lg font-semibold">
                Get Started
              </Button>
            </Link>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-50 py-12 sm:py-16">
          <div className="w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="sm:col-span-2 lg:col-span-1">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                    <Heart className="text-white" size={16} />
                  </div>
                  <span className="text-xl font-bold text-gray-800">
                    TooDiscreet
                  </span>
                </div>
                <div className="space-y-2 text-gray-600 text-sm">
                  <div className="flex items-center space-x-2">
                    <Phone size={16} />
                    <span>+44 7537906743</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail size={16} />
                    <span>Company number 8478596</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MapPin size={16} />
                    <span>First Baptist Church, Luxembourg 2547</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-bold text-gray-800 mb-4">Quick Links</h3>
                <ul className="space-y-2 text-gray-600 text-sm">
                  <li>
                    <a href="#" className="hover:text-purple-600">
                      Home
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-purple-600">
                      Terms & Conditions
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-purple-600">
                      Privacy Policy
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-purple-600">
                      Contact
                    </a>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-bold text-gray-800 mb-4">Help</h3>
                <ul className="space-y-2 text-gray-600 text-sm">
                  <li>
                    <a href="#" className="hover:text-purple-600">
                      FAQ
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-purple-600">
                      Guidelines
                    </a>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-bold text-gray-800 mb-4 text-sm sm:text-base">
                  Stay up to date with our latest news and discounts
                </h3>
                <div className="flex flex-col sm:flex-row">
                  <input
                    type="email"
                    placeholder="Enter your email address"
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg sm:rounded-r-none rounded-r-lg focus:outline-none focus:border-purple-500 text-sm mb-2 sm:mb-0"
                  />
                  <Button className="bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:from-pink-600 hover:to-purple-700 rounded-r-lg sm:rounded-l-none rounded-l-lg px-4 sm:px-6 text-sm">
                    Join Us
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  This website uses cookies to ensure you get the best
                  experience on our website.
                </p>
              </div>
            </div>

            <div className="border-t border-gray-200 mt-8 sm:mt-12 pt-6 sm:pt-8 text-center text-gray-500 text-sm">
              <p>Copyright © 2024 TooDiscreet. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}

export default LandingPage;

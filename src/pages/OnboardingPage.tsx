import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  ThemeProvider,
  useThemeClasses,
  Button,
  BackgroundWrapper,
} from "@/modules/shared/presentation";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import { useOnboarding } from "@/modules/users/presentation/hooks/useOnboarding";
import ProgressBar from "@/modules/users/presentation/components/onboarding/ProgressBar";
import PersonalInfoStep from "@/modules/users/presentation/components/onboarding/PersonalInfoStep";
import PhotosStep from "@/modules/users/presentation/components/onboarding/PhotosStep";
import AppearanceStep from "../modules/users/presentation/components/onboarding/AppearanceStep";
import { Heart } from "lucide-react";

const OnboardingPageContent: React.FC = () => {
  const navigate = useNavigate();
  const { logout } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [stepInitialized, setStepInitialized] = useState(false);
  const themeClasses = useThemeClasses();
  const {
    onboardingData,
    handleStepComplete,
    getStepValidation,
    getProgress,
    isOnboardingComplete,
    canAccessStep,
    getCurrentStep,
    shouldRedirectToOnboarding,
    loading,
    saving,
    error,
    clearError,
    isAuthenticated,
    saveOnboarding,
  } = useOnboarding();

  // Rediriger si non authentifié
  React.useEffect(() => {
    if (!loading && !isAuthenticated) {
      navigate("/splash");
    }
  }, [loading, isAuthenticated, navigate]);

  // Initialiser l'étape en fonction des données utilisateur
  React.useEffect(() => {
    if (!loading && isAuthenticated && !stepInitialized) {
      const appropriateStep = getCurrentStep();
      setCurrentStep(appropriateStep);
      setStepInitialized(true);

      // Si l'onboarding est déjà complet, rediriger vers l'accueil
      if (isOnboardingComplete()) {
        navigate("/");
      }
    }
  }, [loading, isAuthenticated, stepInitialized, onboardingData, navigate]);

  // Mettre à jour l'étape quand les données d'onboarding changent (après chargement)
  React.useEffect(() => {
    if (!loading && isAuthenticated && stepInitialized && onboardingData) {
      const appropriateStep = getCurrentStep();

      // Ne mettre à jour que si l'étape a réellement changé
      if (appropriateStep !== currentStep) {
        setCurrentStep(appropriateStep);
      }

      // Si l'onboarding est déjà complet, rediriger vers l'accueil
      if (isOnboardingComplete()) {
        navigate("/");
      }
    }
  }, [
    onboardingData,
    loading,
    isAuthenticated,
    stepInitialized,
    currentStep,
    navigate,
  ]);

  // Déterminer si l'utilisateur reprend son onboarding
  const isResuming = getCurrentStep() > 1 && stepInitialized;

  const handleStepCompleteWithNavigation = async (step: number, data: any) => {
    try {
      clearError();
      await handleStepComplete(step, data);

      if (step < 3) {
        setCurrentStep(step + 1);
      } else {
        // Onboarding terminé
        handleOnboardingComplete();
      }
    } catch (error) {
      console.error("Error completing step:", error);
      // L'erreur est déjà gérée dans handleStepComplete
    }
  };

  const handleSkip = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      handleOnboardingComplete();
    }
  };

  const handleOnboardingComplete = async () => {
    try {
      // Sauvegarder une dernière fois si nécessaire
      if (isOnboardingComplete()) {
        await saveOnboarding();
      }
      // Rediriger vers la page d'accueil
      navigate("/");
    } catch (error) {
      console.error("Error completing onboarding:", error);
      // Même en cas d'erreur, on redirige (les données peuvent être partiellement sauvegardées)
      navigate("/");
    }
  };

  const handleLogout = () => {
    logout();
    navigate("/splash");
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <PhotosStep
            data={onboardingData.photo}
            onComplete={(data) => handleStepCompleteWithNavigation(1, data)}
            onSkip={handleSkip}
          />
        );
      case 2:
        return (
          <PersonalInfoStep
            data={onboardingData.personalInfo}
            onComplete={(data) => handleStepCompleteWithNavigation(2, data)}
            onSkip={handleSkip}
          />
        );
      case 3:
        return (
          <AppearanceStep
            data={onboardingData.appearance}
            onComplete={(data) => handleStepCompleteWithNavigation(3, data)}
            onSkip={handleSkip}
          />
        );
      default:
        return null;
    }
  };

  const progress = getProgress();

  const goToPreviousStep = () => {
    const newStep = Math.max(1, currentStep - 1);
    if (canAccessStep(newStep)) {
      setCurrentStep(newStep);
      clearError(); // Effacer les erreurs lors de la navigation
    }
  };

  const goToNextStep = () => {
    const newStep = Math.min(3, currentStep + 1);
    if (canAccessStep(newStep)) {
      setCurrentStep(newStep);
      clearError(); // Effacer les erreurs lors de la navigation
    }
  };

  // Afficher un loader pendant le chargement initial
  if (loading) {
    return (
      <ThemeProvider>
        <BackgroundWrapper
          variant="onboarding"
          className="flex items-center justify-center min-h-screen"
        >
          <div className="text-center">
            <div className="relative inline-block mb-6">
              {/* Halo effect multicolore */}
              <div className="absolute inset-0 bg-gradient-to-r from-violet-400/30 via-pink-400/30 to-orange-400/30 rounded-full blur-xl scale-150 animate-pulse"></div>

              {/* Spinner glassmorphique */}
              <div className="relative w-16 h-16 rounded-2xl bg-white/10 backdrop-blur-xl border border-white/20 flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-white/30 border-t-white"></div>
              </div>
            </div>
            <p className="text-2xl font-bold text-white mb-2">Loading...</p>
            <p className="text-white/60">Preparing your experience</p>
          </div>
        </BackgroundWrapper>
      </ThemeProvider>
    );
  }

  return (
    <BackgroundWrapper
      variant="onboarding"
      className="flex flex-col min-h-screen"
    >
      {/* Header glassmorphique premium */}
      <div className="w-full p-6">
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Effet de brillance d'arrière-plan */}
            <div className="absolute inset-0 bg-gradient-to-r from-violet-500/10 via-pink-500/10 to-orange-500/10 rounded-2xl blur-xl"></div>

            <div className="relative bg-white backdrop-blur-xl rounded-2xl border border-white/20 shadow-xl p-4">
              <div className="flex items-center justify-between">
                {/* Logo premium */}
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-violet-400/30 via-pink-400/30 to-orange-400/30 rounded-xl blur-lg scale-110 animate-pulse"></div>

                    <div className="relative w-12 h-12 rounded-xl bg-gradient-to-br from-violet-500 via-pink-600 to-orange-600 flex items-center justify-center shadow-xl border border-white/20">
                      <Heart className="w-6 h-6 text-white" />
                      {/* Reflet */}
                      <div className="absolute inset-1 bg-gradient-to-br from-white/30 to-transparent rounded-lg pointer-events-none"></div>
                    </div>
                  </div>

                  <div>
                    <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-500 via-violet-400 to-pink-500 bg-clip-text text-transparent">
                      Toodiscreet
                    </h1>
                    <p className="text-black/60 text-sm">
                      Setting up your profile
                    </p>
                  </div>
                </div>

                {/* Bouton quitter modernisé */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleLogout}
                  className="bg-black/5 border border-black/20 text-black/80 hover:text-black hover:bg-black/10 hover:border-black/30 backdrop-blur-sm transition-all duration-300"
                >
                  Leave
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Affichage des erreurs glassmorphique */}
      {error && (
        <div className="w-full max-w-3xl mx-auto mb-6 px-4">
          <div className="bg-red-500/10 border border-red-400/30 backdrop-blur-sm rounded-2xl p-4 shadow-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center">
                  <span className="text-red-300 font-bold text-sm">!</span>
                </div>
                <span className="text-red-300 font-medium">{error}</span>
              </div>
              <button
                onClick={clearError}
                className="text-red-300 hover:text-red-200 font-bold text-xl hover:scale-110 transition-all duration-200"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Message de reprise d'onboarding glassmorphique */}
      {isResuming && stepInitialized && (
        <div className="w-full max-w-3xl mx-auto mb-6 px-4">
          <div className="bg-pink-500/10 border border-pink-400/30 backdrop-blur-sm rounded-2xl p-4 shadow-xl">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                <svg
                  className="w-4 h-4 text-blue-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <span className="text-pink-300 font-medium">
                Resuming your profile – Step {currentStep} of 3. Your previous
                information has been saved.
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Contenu principal */}
      <div className="flex-1 flex flex-col items-center justify-center p-6">
        {/* Barre de progression premium */}
        <div className="w-full max-w-3xl mb-8">
          <div className="relative">
            {/* Effet de brillance d'arrière-plan */}
            <div className="absolute inset-0 bg-gradient-to-r from-violet-500/10 to-orange-500/10 rounded-2xl blur-xl"></div>

            <div className="relative bg-black/5 backdrop-blur-sm rounded-2xl border border-white/10 p-6">
              <ProgressBar currentStep={currentStep} totalSteps={3} />
            </div>
          </div>
        </div>

        {/* Étape actuelle avec indicateur de sauvegarde */}
        <div className="w-full flex justify-center mb-8 relative">
          {saving && (
            <div className="absolute -top-4 right-4 z-20">
              <div className="flex items-center gap-2 bg-blue-500/20 backdrop-blur-sm border border-blue-400/30 text-blue-300 px-4 py-2 rounded-full text-sm shadow-xl">
                <div className="animate-spin rounded-full h-3 w-3 border-2 border-blue-300/30 border-t-blue-300"></div>
                Save...
              </div>
            </div>
          )}
          {renderCurrentStep()}
        </div>

        {/* Navigation glassmorphique premium */}
        <div className="w-full max-w-lg">
          <div className="relative">
            {/* Effet de brillance d'arrière-plan */}
            <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-white/10 rounded-2xl blur-sm"></div>

            <div className="relative bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-xl p-6">
              <div className="flex items-center justify-between">
                {/* Bouton précédent */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={goToPreviousStep}
                  disabled={currentStep === 1}
                  className="flex items-center gap-2 bg-black/5 border border-black/20 text-black/80 hover:text-black hover:bg-black/10 hover:border-black/30 disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-300"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                  Back
                </Button>

                {/* Indicateur de progression central */}
                <div className="text-center">
                  <div className="relative w-32 h-2 bg-black/10 rounded-full overflow-hidden mb-2">
                    <div
                      className="h-full bg-gradient-to-r from-violet-500 via-pink-500 to-orange-500 rounded-full transition-all duration-700 shadow-lg"
                      style={{ width: `${progress.percentage}%` }}
                    />
                    {/* Brillance sur la barre */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-black/20 to-transparent animate-pulse"></div>
                  </div>
                  <div className="text-white/60 text-sm font-medium">
                    {progress.percentage}% completed
                  </div>
                </div>

                {/* Bouton suivant */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={goToNextStep}
                  disabled={
                    currentStep === 3 || !canAccessStep(currentStep + 1)
                  }
                  className="flex items-center gap-2 bg-black/5 border border-black/20 text-black/80 hover:text-black hover:bg-black/10 hover:border-black/30 disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-300"
                >
                  Next
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BackgroundWrapper>
  );
};

const OnboardingPage: React.FC = () => {
  return (
    <ThemeProvider>
      <OnboardingPageContent />
    </ThemeProvider>
  );
};

export default OnboardingPage;

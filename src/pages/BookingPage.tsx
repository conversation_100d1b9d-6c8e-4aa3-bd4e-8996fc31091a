import React from "react";
import { Gem } from "lucide-react";
import { ChatLayout } from "@/components/ChatLayout";
import { Card } from "@/modules/shared";

const plans = [
  {
    name: "Weekly",
    price: "8 Credit",
    color: "border-green-500 bg-white text-black",
    buttonColor: "bg-pink-500",
    iconColor: "text-green-500",
  },
  {
    name: "Monthly",
    price: "25 Credit",
    special: true,
    color: "border-yellow-500 bg-black text-white",
    buttonColor: "bg-pink-500",
    iconColor: "text-yellow-400",
  },
  {
    name: "Yearly",
    price: "280 Credit",
    color: "border-blue-500 bg-white text-black",
    buttonColor: "bg-pink-500",
    iconColor: "text-blue-500",
  },
  {
    name: "Lifetime",
    price: "500 Credit",
    color: "border-pink-500 bg-white text-black",
    buttonColor: "bg-pink-500",
    iconColor: "text-pink-500",
  },
];

const features = [
  "Get more stickers in chat",
  "Show yourself in premium bar",
  "View likes notifications",
  'Get a discount when using "boost me"',
  "Display first in find matches",
  "Display on top in random users",
  "Find potential matches by country",
];

const PricingPage = () => {
  return (
    <ChatLayout className="flex flex-col mt-12 lg:mt-0">
      <div className="p-8 max-w-7xl mx-auto">
        <Card className="mb-8 bg-white backdrop-blur-md shadow-lg rounded-xl p-6">
          <h1 className="text-3xl font-bold text-black text-center mb-4">
            Amazing toodiscreet features you can’t live without.
          </h1>
          <p className="text-center text-black/80">
            Activating Premium will help you meet more people, faster.
          </p>
        </Card>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {plans.map((plan) => (
            <div
              key={plan.name}
              className={`border relative rounded-xl p-6 flex flex-col justify-between ${plan.color}`}
            >
              {plan.special && (
                <div className="absolute -top-4 left-1/2 -translate-x-1/2 bg-pink-600 text-white rounded-full px-3 py-1 mb-4 w-fit shadow font-semibold text-sm z-10">
                  Special
                </div>
              )}
              <div className="flex flex-row items-center justify-center mb-4">
                <Gem className={`w-10 h-10 ${plan.iconColor}`} />
                <div className="flex flex-col gap-1 ml-4">
                  <h2 className="text-xl font-semibold">{plan.name}</h2>
                  <p>Package</p>
                </div>
              </div>
              <p className="text-2xl text-center font-bold mt-2">{plan.price}</p>
              <div className="w-full h-[1px] mt-5 bg-white/10"> </div>
              <ul className="mt-4 space-y-2">
                {features.map((feature) => (
                  <li key={feature} className="flex items-center gap-2">
                    <span className="text-green-500">✔</span>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
              <button
                className={`mt-6 ${plan.buttonColor} text-white py-2 rounded-lg`}
              >
                Choose Plan
              </button>
            </div>
          ))}
        </div>
      </div>
    </ChatLayout>
  );
};

export default PricingPage;

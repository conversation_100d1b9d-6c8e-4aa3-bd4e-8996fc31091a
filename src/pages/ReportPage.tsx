import { ChatLayout } from "@/components/ChatLayout";
import ReportUsers from "@/components/ReportUsers";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import { User } from "@/modules/users/domain/entities/User";
import userApiRepository from "@/modules/users/infrastructure/api/UserApiRepository";
import React, { useEffect } from "react";

function ReportPage() {
  const [reportedUsers, setReportedUsers] = React.useState<User[] | null>([]); // State to hold reported users
  const [isLoading, setIsLoading] = React.useState(false); // State to manage loading state
  const [error, setError] = React.useState<string | null>(null); //
  const { user } = useAuth(); // Assuming you have a useAuth hook to get the current user

  const loadReportedUsers = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Fetch reported users from your API or service
      const data = await userApiRepository.getReportUserByUserId();
      setReportedUsers(data);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while fetching reported users"
      );
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadReportedUsers();
  }, []);

  return (
    <ChatLayout className="p-6 mt-12 lg:mt-0 overflow-y-auto">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-4xl font-bold mb-4 text-black">Report Page</h1>
        <p className="text-lg text-black/70">
          This is the report page where you can view and manage reports.
        </p>
        {/* Add your report management components here */}
        <ReportUsers users={reportedUsers} isLoading={isLoading} />
      </div>
    </ChatLayout>
  );
}

export default ReportPage;

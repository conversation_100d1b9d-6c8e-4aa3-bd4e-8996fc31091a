import { Button, Input, Label } from "@/modules/shared/presentation";
import { useParams } from "react-router-dom";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import { useState } from "react";
import { Password } from "@/modules/auth/domain/valueobjects/Password";
import { InvalidPasswordException } from "@/modules/auth/domain/exceptions/InvalidPassword";
import { PasswordTooShortException } from "@/modules/auth/domain/exceptions/PasswordTooShort";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/modules/shared/presentation";

export const ResetPasswordPage = () => {
  const { token } = useParams();
  const { resetPassword } = useAuth();
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (token) {
      if (newPassword !== confirmPassword) {
        setError("Passwords do not match");
        return;
      }
      try {
        const password = new Password(newPassword);
        resetPassword(token, password.getValue());
      } catch (error) {
        if (error instanceof InvalidPasswordException) {
          setError(
            "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character"
          );
        } else if (error instanceof PasswordTooShortException) {
          setError("Password must be at least 8 characters long");
        } else {
          setError("An error occurred");
        }
      }
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Reset Password</CardTitle>
        <CardDescription>Enter your new password</CardDescription>
      </CardHeader>
      <CardContent>
        {token ? (
          <>
            {error && <p className="text-red-500">{error}</p>}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="newPassword">New Password</Label>
                <Input
                  type="password"
                  id="newPassword"
                  value={newPassword}
                  placeholder="New Password"
                  onChange={(e) => setNewPassword(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  type="password"
                  id="confirmPassword"
                  value={confirmPassword}
                  placeholder="Confirm Password"
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
              </div>
              <Button type="submit" className="w-full">
                Reset Password
              </Button>
            </form>
          </>
        ) : (
          <div>Invalid url</div>
        )}
      </CardContent>
    </Card>
  );
};

import React from 'react';
import { Message } from '../../domain/entities/Message';
import { MessageTypeEnum } from '../../domain/valueobjects/MessageType';
import { ChatUser } from '../context/ChatContext';
import { ImageMessage } from './ImageMessage';
import { VideoMessage } from './VideoMessage';
import { TextMessage } from './TextMessage';
import { MessageContentAdapter } from '../../infrastructure/adapters/MessageContentAdapter';

// Interface pour les renderers de messages
export interface MessageRenderer {
  canRender(messageType: MessageTypeEnum): boolean;
  render(props: MessageRendererProps): React.ReactElement;
}

export interface MessageRendererProps {
  message: Message;
  sender?: ChatUser;
  isFromCurrentUser: boolean;
  onLoad?: () => void;
}

// Renderer pour les messages texte
class TextMessageRenderer implements MessageRenderer {
  canRender(messageType: MessageTypeEnum): boolean {
    return messageType === MessageTypeEnum.TEXT || messageType === MessageTypeEnum.SYSTEM;
  }

  render({ message, isFromCurrentUser }: MessageRendererProps): React.ReactElement {
    return (
      <TextMessage 
        message={message}
        isFromCurrentUser={isFromCurrentUser}
      />
    );
  }
}

// Renderer pour les messages image
class ImageMessageRenderer implements MessageRenderer {
  canRender(messageType: MessageTypeEnum): boolean {
    return messageType === MessageTypeEnum.IMAGE;
  }

  render({ message, isFromCurrentUser, onLoad }: MessageRendererProps): React.ReactElement {
    try {
      const parsedContent = MessageContentAdapter.parseMessageContent(message);
      return (
        <ImageMessage 
          imageData={parsedContent.data}
          isFromCurrentUser={isFromCurrentUser}
          onImageLoad={onLoad}
        />
      );
    } catch (error) {
      console.error('Error parsing image content:', error);
      return (
        <div className="text-sm text-destructive">
          Error loading image
        </div>
      );
    }
  }
}

// Renderer pour les messages vidéo
class VideoMessageRenderer implements MessageRenderer {
  canRender(messageType: MessageTypeEnum): boolean {
    return messageType === MessageTypeEnum.VIDEO;
  }

  render({ message, isFromCurrentUser, onLoad }: MessageRendererProps): React.ReactElement {
    try {
      const parsedContent = MessageContentAdapter.parseMessageContent(message);
      return (
        <VideoMessage 
          videoData={parsedContent.data}
          isFromCurrentUser={isFromCurrentUser}
          onVideoLoad={onLoad}
        />
      );
    } catch (error) {
      console.error('Error parsing video content:', error);
      return (
        <div className="text-sm text-destructive">
          Error loading video
        </div>
      );
    }
  }
}

// Registry des renderers
class MessageRendererRegistry {
  private renderers: MessageRenderer[] = [];

  constructor() {
    // Enregistrement des renderers par défaut
    this.register(new TextMessageRenderer());
    this.register(new ImageMessageRenderer());
    this.register(new VideoMessageRenderer());
  }

  register(renderer: MessageRenderer): void {
    this.renderers.push(renderer);
  }

  getRenderer(messageType: MessageTypeEnum): MessageRenderer | null {
    return this.renderers.find(renderer => renderer.canRender(messageType)) || null;
  }
}

// Instance singleton du registry
export const messageRendererRegistry = new MessageRendererRegistry();

// Hook pour utiliser le système de rendu
export const useMessageRenderer = () => {
  const renderMessage = (props: MessageRendererProps): React.ReactElement => {
    const renderer = messageRendererRegistry.getRenderer(props.message.type.getValue());
    
    if (!renderer) {
      console.warn(`No renderer found for message type: ${props.message.type.getValue()}`);
      return (
        <div className="text-sm text-destructive">
          Unsupported message type: {props.message.type.getValue()}
        </div>
      );
    }

    return renderer.render(props);
  };

  return { renderMessage };
}; 
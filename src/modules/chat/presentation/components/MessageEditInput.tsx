import React, { useEffect } from 'react';
import { Button, Input } from '@/modules/shared/presentation';
import { X, Play } from 'lucide-react';
import { EmojiPicker } from './EmojiPicker';
import { cn } from '@/modules/shared/infrastructure';
import { Message } from '../../domain/entities/Message';
import { MessageTypeEnum } from '../../domain/valueobjects/MessageType';
import { useMessageEdit } from '../hooks/useMessageEdit';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';
import { useTypingIndicator } from '../hooks/useTypingIndicator';

interface MessageEditInputProps {
  editingMessage: Message;
  onSaveEdit: (newContent: string) => Promise<void>;
  onCancelEdit: () => void;
  onStartTyping?: (chatId: string, receiverId: string) => void;
  onStopTyping?: (chatId: string, receiverId: string) => void;
  chatId?: string;
  receiverId?: string;
  disabled?: boolean;
  className?: string;
}

export const MessageEditInput: React.FC<MessageEditInputProps> = ({
  editingMessage,
  onSaveEdit,
  onCancelEdit,
  onStartTyping,
  onStopTyping,
  chatId,
  receiverId,
  disabled = false,
  className
}) => {
  const { handleMessageChange: handleTypingChange, cleanup } = useTypingIndicator({
    chatId,
    receiverId,
    onStartTyping,
    onStopTyping
  });

  const {
    message,
    setMessage,
    isSaving,
    inputRef,
    mediaData,
    messageTypeLabel,
    placeholder,
    handleSave,
    handleEmojiSelect
  } = useMessageEdit({ 
    editingMessage, 
    onSaveEdit,
    onMessageChange: handleTypingChange
  });

  const { handleKeyPress } = useKeyboardShortcuts({
    onSave: handleSave,
    onCancel: onCancelEdit,
    disabled: disabled || isSaving
  });

  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const handleMessageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMessage = e.target.value;
    setMessage(newMessage);
    handleTypingChange(newMessage);
  };

  return (
    <div className={cn('bg-white/5 backdrop-blur-xl border-t border-white/10', className)}>
      {/* Indicateur de mode édition */}
      <div className="p-3 bg-blue-500/20 backdrop-blur-sm border-t border-blue-400/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-1 h-6 bg-blue-400 rounded shadow-blue-400/50 shadow-sm" />
            <p className="text-sm font-medium text-blue-200">
              Modification {messageTypeLabel === 'message' ? 'du message' : `de la légende de l'${messageTypeLabel}`}
            </p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancelEdit}
            className="text-blue-300 hover:text-blue-100 hover:bg-blue-500/20 backdrop-blur-sm"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Aperçu du média en cours d'édition */}
      {mediaData && (
        <div className="p-3 border-t border-white/10 bg-white/5">
          <div className="flex justify-center">
            <div className="w-32 h-32 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden shadow-lg">
              {editingMessage.type.getValue() === MessageTypeEnum.IMAGE ? (
                <img
                  src={mediaData.url}
                  alt="Aperçu"
                  className="w-full h-full object-cover"
                />
              ) : editingMessage.type.getValue() === MessageTypeEnum.VIDEO ? (
                <div className="relative w-full h-full bg-black/20">
                  <video
                    src={mediaData.url}
                    poster={mediaData.thumbnail}
                    className="w-full h-full object-cover"
                    muted
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black/30 backdrop-blur-sm">
                    <Play className="h-4 w-4 text-white drop-shadow-lg" />
                  </div>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      )}

      {/* Zone d'input */}
      <div className="flex gap-3 p-4">
        <EmojiPicker 
          onEmojiSelect={handleEmojiSelect}
          disabled={disabled || isSaving}
        />
        
        <Input
          ref={inputRef}
          value={message}
          onChange={handleMessageChange}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          disabled={disabled || isSaving}
          className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-white/50 backdrop-blur-sm focus:bg-white/15 focus:border-white/30 transition-all duration-300"
          maxLength={5000}
        />
        
        <div className="flex gap-2">
          <Button
            onClick={onCancelEdit}
            disabled={isSaving}
            size="sm"
            variant="outline"
            className="bg-white/10 hover:bg-white/20 text-white border-white/20 backdrop-blur-sm transition-all duration-300"
          >
            Annuler
          </Button>
          <Button
            onClick={handleSave}
            disabled={!message.trim() || isSaving}
            size="sm"
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-blue-400/50 shadow-lg backdrop-blur-sm transition-all duration-300"
          >
            Sauver
          </Button>
        </div>
      </div>
    </div>
  );
}; 
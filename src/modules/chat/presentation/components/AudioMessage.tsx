import React, { useState, useRef } from 'react';
import { cn } from '@/modules/shared/infrastructure';
import { FormatService } from '@/modules/shared/domain/services/FormatService';

export interface AudioMessageContent {
  url: string;
  originalName: string;
  size: number;
  duration?: number;
  mimeType?: string;
}

interface AudioMessageProps {
  audioData: AudioMessageContent;
  isFromCurrentUser: boolean;
  className?: string;
  onAudioLoad?: () => void;
}

export const AudioMessage: React.FC<AudioMessageProps> = ({
  audioData,
  isFromCurrentUser,
  className,
  onAudioLoad
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(audioData.duration || 0);
  const audioRef = useRef<HTMLAudioElement>(null);

  const handleAudioLoad = () => {
    setIsLoading(false);
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
    if (onAudioLoad) {
      onAudioLoad();
    }
  };

  const handleAudioError = () => {
    setIsLoading(false);
    setHasError(true);
    if (onAudioLoad) {
      onAudioLoad();
    }
  };

  const togglePlay = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    if (audioRef.current && duration > 0) {
      const rect = e.currentTarget.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const width = rect.width;
      const newTime = (clickX / width) * duration;
      
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  // Utilisation des services de formatage
  const formattedCurrentTime = FormatService.formatDuration(currentTime);
  const formattedDuration = FormatService.formatDuration(duration);
  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div className={cn('max-w-xs', className)}>
      <div 
        className={cn(
          'flex items-center gap-3 p-3 rounded-lg border',
          isFromCurrentUser ? 'bg-primary/10' : 'bg-muted'
        )}
      >
        <audio
          ref={audioRef}
          src={audioData.url}
          onLoadedData={handleAudioLoad}
          onError={handleAudioError}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onEnded={() => setIsPlaying(false)}
          onTimeUpdate={handleTimeUpdate}
          className="hidden"
        />

        {/* Play/Pause Button */}
        <button
          onClick={togglePlay}
          disabled={isLoading || hasError}
          className={cn(
            'w-10 h-10 rounded-full flex items-center justify-center transition-colors',
            isFromCurrentUser 
              ? 'bg-primary text-primary-foreground hover:bg-primary/90' 
              : 'bg-primary text-primary-foreground hover:bg-primary/90',
            (isLoading || hasError) && 'opacity-50 cursor-not-allowed'
          )}
        >
          {isLoading ? (
            <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
          ) : hasError ? (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ) : isPlaying ? (
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
            </svg>
          ) : (
            <svg className="w-4 h-4 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          )}
        </button>

        {/* Audio Info */}
        <div className="flex-1 min-w-0">
          {hasError ? (
            <div className="text-sm text-destructive">
              Erreur de lecture audio
            </div>
          ) : (
            <>
              {/* Waveform/Progress Bar */}
              <div 
                className="relative h-2 bg-muted-foreground/20 rounded-full cursor-pointer mb-1"
                onClick={handleSeek}
              >
                <div 
                  className="absolute left-0 top-0 h-full bg-primary rounded-full transition-all duration-100"
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
              
              {/* Time and File Info */}
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>
                  {formattedCurrentTime} / {formattedDuration}
                </span>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}; 
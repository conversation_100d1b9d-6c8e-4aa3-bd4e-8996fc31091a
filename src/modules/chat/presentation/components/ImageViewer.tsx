import React, { useState } from 'react';
import { Modal } from '@/modules/shared/presentation/components/ui/modal';
import { cn } from '@/modules/shared/infrastructure';
import { ImageMessageContent } from '../../infrastructure/api/types/ChatTypes';
import { MediaViewerHeader } from './MediaViewerHeader';

interface ImageViewerProps {
  isOpen: boolean;
  onClose: () => void;
  imageData: ImageMessageContent;
}

export const ImageViewer: React.FC<ImageViewerProps> = ({
  isOpen,
  onClose,
  imageData
}) => {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  const handleImageLoad = () => {
    setIsImageLoaded(true);
    setHasError(false);
  };

  const handleImageError = () => {
    setIsImageLoaded(false);
    setHasError(true);
  };

  const downloadImage = () => {
    const link = document.createElement('a');
    link.href = imageData.url;
    link.download = imageData.originalName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const openInNewTab = () => {
    window.open(imageData.url, '_blank');
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="w-auto h-auto">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-2xl overflow-hidden">
        {/* Header */}
        <MediaViewerHeader
          onDownload={downloadImage}
          onOpenInNewTab={openInNewTab}
          onClose={onClose}
        />

        {/* Contenu image */}
        <div>
          <div className="relative">
            {!isImageLoaded && !hasError && (
              <div className="flex items-center justify-center w-full h-64 bg-gray-100 dark:bg-gray-800 rounded">
                <div className="animate-spin h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full" />
              </div>
            )}
            
            {hasError ? (
              <div className="flex flex-col items-center justify-center w-full h-64 bg-gray-100 dark:bg-gray-800 rounded">
                <div className="w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                  <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  Impossible de charger l'image
                </p>
              </div>
            ) : (
              <img
                src={imageData.url}
                alt={imageData.caption || imageData.originalName}
                onLoad={handleImageLoad}
                onError={handleImageError}
                className={cn(
                  'max-w-full max-h-[70vh] object-contain mx-auto rounded',
                  !isImageLoaded && 'opacity-0'
                )}
              />
            )}
          </div>

          {/* Caption */}
          {imageData.caption && (
            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {imageData.caption}
              </p>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
}; 
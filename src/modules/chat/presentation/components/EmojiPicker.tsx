import React, { useState, useRef, useEffect } from 'react';
import { Button, Input } from '@/modules/shared/presentation';
import { Smile, Search } from 'lucide-react';
import { cn } from '@/modules/shared/infrastructure';

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  disabled?: boolean;
  className?: string;
}

// Emojis organisés par catégories compactes
const EMOJI_CATEGORIES = {
  recent: {
    icon: '🕒',
    emojis: ['😀', '😂', '😍', '😭', '😊', '🤔', '👍', '❤️', '🎉', '🔥', '😘', '😎']
  },
  smileys: {
    icon: '😀',
    emojis: ['😀', '😃', '😄', '😁', '😅', '😂', '🤣', '😊', '😇', '🙂', '😉', '😌', '😍', '🥰', '😘', '😋', '😛', '😜', '🤪', '😎']
  },
  emotions: {
    icon: '😢',
    emojis: ['😔', '😞', '😟', '😕', '🙁', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '😱', '😨', '😰', '🤗']
  },
  gestures: {
    icon: '👍',
    emojis: ['👍', '👎', '👌', '🤌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '👇', '☝️', '👋', '🤚', '✋', '👏', '🙌', '🙏']
  },
  hearts: {
    icon: '❤️',
    emojis: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💋', '💍']
  },
  objects: {
    icon: '🎉',
    emojis: ['🎉', '🎊', '🎈', '🎁', '🎂', '🍰', '🍕', '🍔', '🍟', '🌭', '🍿', '☕', '🍺', '🍻', '🥂', '🍷', '🔥', '⭐', '✨', '💎']
  }
};

// Fonction de recherche simplifiée
const searchEmojis = (searchTerm: string): string[] => {
  if (!searchTerm.trim()) return [];
  
  const allEmojis = Object.values(EMOJI_CATEGORIES).flatMap(cat => cat.emojis);
  // Recherche simple par correspondance de caractères
  return allEmojis.filter(emoji => 
    emoji.includes(searchTerm) || 
    searchTerm.toLowerCase().includes('coeur') && ['❤️', '💛', '💚', '💙', '💜', '🧡'].includes(emoji) ||
    searchTerm.toLowerCase().includes('fire') && emoji === '🔥' ||
    searchTerm.toLowerCase().includes('love') && ['❤️', '😍', '🥰', '😘', '💕'].includes(emoji)
  );
};

export const EmojiPicker: React.FC<EmojiPickerProps> = ({
  onEmojiSelect,
  disabled = false,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState<keyof typeof EMOJI_CATEGORIES>('recent');
  const [searchTerm, setSearchTerm] = useState('');
  const pickerRef = useRef<HTMLDivElement>(null);

  // Fermer le picker quand on clique à l'extérieur
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleEmojiClick = (emoji: string) => {
    onEmojiSelect(emoji);
    setIsOpen(false); // Fermer automatiquement après sélection
  };

  const getDisplayEmojis = (): string[] => {
    if (searchTerm) {
      return searchEmojis(searchTerm);
    }
    return EMOJI_CATEGORIES[activeCategory].emojis;
  };

  return (
    <div className="relative" ref={pickerRef}>
      <Button
        variant="outline"
        size="sm"
        disabled={disabled}
        onClick={() => setIsOpen(!isOpen)}
        className={cn('gap-2 h-full bg-white/10 hover:bg-white/20 text-white border-white/20 backdrop-blur-sm transition-all duration-300', className)}
        title="Emoji"
      >
        <Smile className="h-4 w-4" />
      </Button>

      {isOpen && (
        <div className="absolute bottom-full right-0 mb-2 w-72 bg-white/20 backdrop-blur-xl border border-white/30 rounded-2xl shadow-2xl z-50 p-3">
          {/* Barre de recherche compacte */}
          <div className="relative mb-3">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-white/50" />
            <Input
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-7 h-7 text-xs bg-white/10 border-white/20 text-white placeholder:text-white/50 backdrop-blur-sm focus:bg-white/15 focus:border-white/30"
            />
          </div>

          {!searchTerm && (
            /* Onglets catégories - version icônes uniquement */
            <div className="flex justify-center gap-1 mb-3 p-1 bg-white/10 rounded-xl backdrop-blur-sm">
              {Object.entries(EMOJI_CATEGORIES).map(([key, category]) => (
                <button
                  key={key}
                  onClick={() => setActiveCategory(key as keyof typeof EMOJI_CATEGORIES)}
                  className={cn(
                    'w-8 h-8 flex items-center justify-center rounded-lg transition-all duration-300',
                    activeCategory === key
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg scale-110'
                      : 'text-white/70 hover:bg-white/20 hover:text-white hover:scale-105'
                  )}
                  title={key}
                >
                  <span className="text-sm">{category.icon}</span>
                </button>
              ))}
            </div>
          )}

          {/* Grille d'emojis compacte */}
          <div className="grid grid-cols-8 gap-1 max-h-40 overflow-y-auto pr-1">
            {getDisplayEmojis().map((emoji, index) => (
              <button
                key={`${emoji}-${index}`}
                onClick={() => handleEmojiClick(emoji)}
                className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-white/30 transition-all duration-200 text-lg hover:scale-125 transform backdrop-blur-sm"
                title={emoji}
              >
                {emoji}
              </button>
            ))}
          </div>

          {searchTerm && getDisplayEmojis().length === 0 && (
            <div className="text-center py-4 text-white/50 text-xs">
              Aucun emoji trouvé
            </div>
          )}
        </div>
      )}
    </div>
  );
}; 
import React from 'react';
import { Button } from '@/modules/shared/presentation';

interface MediaViewerHeaderProps {
  onDownload: () => void;
  onOpenInNewTab: () => void;
  onClose: () => void;
}

export const MediaViewerHeader: React.FC<MediaViewerHeaderProps> = ({
  onDownload,
  onOpenInNewTab,
  onClose
}) => {
  return (
    <div className="flex items-center justify-end p-4 border-b border-gray-200 dark:border-gray-700">      
      {/* Actions */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onDownload}
          title="Télécharger"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={onOpenInNewTab}
          title="Ouvrir dans un nouvel onglet"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
          title="Fermer"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </Button>
      </div>
    </div>
  );
}; 
import React, { useRef, useState } from 'react';
import { Button, Input, Label, Card, CardContent, CardHeader, CardTitle } from '@/modules/shared/presentation';
import { ImageIcon, Upload, X } from 'lucide-react';
import { cn } from '@/modules/shared/infrastructure';
import { FormatService } from '@/modules/shared/domain/services/FormatService';

interface ImageUploadProps {
  onImageUpload: (file: File, caption?: string) => Promise<void>;
  disabled?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageUpload,
  disabled = false,
  className,
  children
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [caption, setCaption] = useState('');
  const [preview, setPreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // File validation
    if (!file.type.startsWith('image/')) {
      alert('Please select a valid image file');
      return;
    }

    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      alert('File size cannot exceed 10MB');
      return;
    }

    setSelectedFile(file);

    // Créer un aperçu
    const reader = new FileReader();
    reader.onload = (event: ProgressEvent<FileReader>) => {
      if (event.target?.result) {
        setPreview(event.target.result as string);
      }
    };
    reader.readAsDataURL(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      setIsUploading(true);
      await onImageUpload(selectedFile, caption);
      
      // Reset form
      setSelectedFile(null);
      setCaption('');
      setPreview(null);
      setIsOpen(false);
      
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error during upload:', error);
      alert('Error uploading image');
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancel = () => {
    setSelectedFile(null);
    setCaption('');
    setPreview(null);
    setIsOpen(false);
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  if (!isOpen) {
    return (
      <Button
        variant="outline"
        size="sm"
        disabled={disabled}
        onClick={() => setIsOpen(true)}
        className={cn('gap-2', className)}
      >
        <ImageIcon className="h-4 w-4" />
        Image
      </Button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Send Image</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* File input */}
          <div className="space-y-2">
            <Label htmlFor="image-input">Select an image</Label>
            <Input
              ref={fileInputRef}
              id="image-input"
              type="file"
              accept="image/jpeg,image/png,image/gif,image/webp"
              onChange={handleFileSelect}
              disabled={isUploading}
            />
          </div>

          {/* Preview */}
          {preview && selectedFile && (
            <div className="space-y-2">
              <Label>Preview</Label>
              <div className="relative border rounded-lg p-4 bg-muted/50">
                <div className="flex items-start gap-3">
                  <img 
                    src={preview} 
                    alt="Preview" 
                    className="w-20 h-20 object-cover rounded border"
                  />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {selectedFile.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {FormatService.formatFileSize(selectedFile.size)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {selectedFile.type}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCancel}
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Caption */}
          {selectedFile && (
            <div className="space-y-2">
              <Label htmlFor="caption">Caption (optional)</Label>
              <textarea
                id="caption"
                placeholder="Add a caption to your image..."
                value={caption}
                onChange={(e) => setCaption(e.target.value)}
                disabled={isUploading}
                maxLength={500}
                rows={3}
                className="w-full p-2 border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-muted-foreground">
                {caption.length}/500 characters
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isUploading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              disabled={!selectedFile || isUploading}
              className="gap-2"
            >
              {isUploading ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  Send
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}; 
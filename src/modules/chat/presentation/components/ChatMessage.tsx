import React from 'react';
import { MessageActions } from './MessageActions';
import { Message } from '../../domain/entities/Message';
import { MessageStatusEnum } from '../../domain/valueobjects/MessageStatus';
import { ChatUser } from '../context/ChatContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/modules/shared/presentation';
import { cn } from '@/modules/shared/infrastructure';
import { useMessageRenderer } from './MessageRenderer';
import { useChat } from '../context/ChatContext';

interface ChatMessageProps {
  message: Message;
  sender?: ChatUser;
  isFromCurrentUser: boolean;
  showAvatar?: boolean;
  showTime?: boolean;
  isGrouped?: boolean;
  className?: string;
  onImageLoad?: () => void;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  sender,
  isFromCurrentUser,
  showAvatar = true,
  showTime = true,
  isGrouped = false,
  className,
  onImageLoad
}) => {
  const { renderMessage } = useMessageRenderer();
  const { state, actions } = useChat();

  const getStatusIcon = () => {
    switch (message.status.getValue()) {
      case MessageStatusEnum.SENT:
        return <span className="text-black/60">✓</span>;
      case MessageStatusEnum.DELIVERED:
        return <span className="text-black/70">✓✓</span>;
      case MessageStatusEnum.READ:
        return <span className="text-purple-400">✓✓</span>;
      default:
        return '';
    }
  };

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getAvatarGradient = (name: string): string => {
    const gradients = [
      'from-pink-500 to-rose-500',
      'from-blue-500 to-cyan-500', 
      'from-green-500 to-emerald-500',
      'from-purple-500 to-violet-500',
      'from-orange-500 to-amber-500',
      'from-red-500 to-pink-500'
    ];
    
    const index = (sender?.id || name).length % gradients.length;
    return gradients[index];
  };

  const renderMessageContent = () => {
    return renderMessage({
      message,
      sender,
      isFromCurrentUser,
      onLoad: onImageLoad
    });
  };

  const handleStartEdit = (message: Message) => {
    actions.startEditingMessage(message);
  };

  const handleDelete = (messageId: string) => {
    actions.deleteMessage(messageId);
  };

  return (
    <div
      className={cn(
        'flex gap-3 max-w-[70%] group',
        isGrouped ? 'mb-1' : 'mb-4',
        isFromCurrentUser ? 'ml-auto flex-row-reverse' : 'mr-auto',
        className
      )}
    >
      {/* Avatar space only for other users */}
      {!isFromCurrentUser && (
        // Other users: always show space, but only show avatar if showAvatar is true
        <div className="w-8 h-8 flex-shrink-0">
          {showAvatar && (
            <Avatar className="w-8 h-8 ring-2 ring-white/10 hover:ring-white/20 transition-all duration-300">
              {sender?.avatar ? (
                <AvatarImage src={sender.avatar} alt={sender.name || 'User'} />
              ) : (
                <AvatarFallback className={cn(
                  'text-xs text-black font-semibold bg-gradient-to-br shadow-lg',
                  getAvatarGradient(sender?.name || '')
                )}>
                  {sender ? getInitials(sender.name) : '?'}
                </AvatarFallback>
              )}
            </Avatar>
          )}
        </div>
      )}
      
      <div
        className={cn(
          'flex flex-col min-w-0',
          isFromCurrentUser ? 'items-end' : 'items-start'
        )}
      >
        {/* Message content container with actions */}
        <div className={cn(
          'relative flex items-center gap-2',
          isFromCurrentUser ? 'flex-row-reverse' : 'flex-row'
        )}>
          {/* Message content */}
          <div className="flex-shrink min-w-0">
            {renderMessageContent()}
          </div>
          
          {/* Message actions - visible on hover */}
          <div className="opacity-0 group-hover:opacity-100 transition-all duration-300 flex-shrink-0">
            <MessageActions
              message={message}
              currentUserId={state.currentUserId || ''}
              onStartEdit={handleStartEdit}
              onDelete={handleDelete}
            />
          </div>
        </div>

        {/* Message info (time and status) */}
        {showTime && (
          <div
            className={cn(
              'flex items-center gap-1 mt-1 text-xs text-black/50',
              isFromCurrentUser ? 'flex-row-reverse' : 'flex-row'
            )}
          >
            <span>{message.getFormattedTime()}</span>
            {isFromCurrentUser && (
              <span className="ml-1">{getStatusIcon()}</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}; 
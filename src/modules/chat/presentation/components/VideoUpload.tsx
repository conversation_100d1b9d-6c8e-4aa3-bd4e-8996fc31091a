import React, { useState, useRef } from 'react';
import { Button, Input, Label, Card, CardContent, CardHeader, CardTitle } from '@/modules/shared/presentation';
import { VideoIcon, Upload, X, Play } from 'lucide-react';
import { cn } from '@/modules/shared/infrastructure';
import { FileValidationRules } from '../../domain/valueobjects/FileValidation';
import { FormatService } from '@/modules/shared/domain/services/FormatService';

interface VideoUploadProps {
  onVideoUpload: (file: File, caption?: string) => Promise<void>;
  disabled?: boolean;
  className?: string;
}

export const VideoUpload: React.FC<VideoUploadProps> = ({
  onVideoUpload,
  disabled = false,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [caption, setCaption] = useState('');
  const [preview, setPreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Utiliser les règles de validation du domaine
  const validationRules = FileValidationRules.videoRules();

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Utiliser les règles de validation du domaine
    const validationResult = validationRules.validate(file);
    if (!validationResult.isValid) {
      alert(validationResult.errorMessage);
      return;
    }

    setSelectedFile(file);

    // Créer un aperçu vidéo
    const reader = new FileReader();
    reader.onload = (event: ProgressEvent<FileReader>) => {
      if (event.target?.result) {
        setPreview(event.target.result as string);
      }
    };
    reader.readAsDataURL(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      setIsUploading(true);
      await onVideoUpload(selectedFile, caption);
      
      // Reset form
      setSelectedFile(null);
      setCaption('');
      setPreview(null);
      setIsOpen(false);
      
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Erreur lors de l\'upload:', error);
      alert('Erreur lors de l\'upload de la vidéo');
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancel = () => {
    setSelectedFile(null);
    setCaption('');
    setPreview(null);
    setIsOpen(false);
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  if (!isOpen) {
    return (
      <Button
        variant="outline"
        size="sm"
        disabled={disabled}
        onClick={() => setIsOpen(true)}
        className={cn('gap-2', className)}
      >
        <VideoIcon className="h-4 w-4" />
        Vidéo
      </Button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Envoyer une vidéo</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* File input */}
          <div className="space-y-2">
            <Label htmlFor="video-input">Sélectionner une vidéo</Label>
            <Input
              ref={fileInputRef}
              id="video-input"
              type="file"
              accept={validationRules.allowedMimeTypes.join(',')}
              onChange={handleFileSelect}
              disabled={isUploading}
            />
          </div>

          {/* Preview */}
          {preview && selectedFile && (
            <div className="space-y-2">
              <Label>Aperçu</Label>
              <div className="relative border rounded-lg p-4 bg-muted/50">
                <div className="flex items-start gap-3">
                  <div className="relative w-20 h-20 bg-black rounded border overflow-hidden">
                    <video 
                      src={preview} 
                      className="w-full h-full object-cover"
                      muted
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                      <Play className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {selectedFile.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {FormatService.formatFileSize(selectedFile.size)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {selectedFile.type}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCancel}
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Caption */}
          {selectedFile && (
            <div className="space-y-2">
              <Label htmlFor="caption">Légende (optionnel)</Label>
              <textarea
                id="caption"
                placeholder="Ajouter une légende à votre vidéo..."
                value={caption}
                onChange={(e) => setCaption(e.target.value)}
                disabled={isUploading}
                maxLength={500}
                rows={3}
                className="w-full p-2 border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-muted-foreground">
                {caption.length}/500 caractères
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isUploading}
            >
              Annuler
            </Button>
            <Button
              onClick={handleUpload}
              disabled={!selectedFile || isUploading}
              className="gap-2"
            >
              {isUploading ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                  Envoi...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  Envoyer
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}; 
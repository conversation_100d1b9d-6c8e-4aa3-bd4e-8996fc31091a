import React, { useState } from 'react';
import { Edit, Trash2, MoreHorizontal } from 'lucide-react';
import { Button } from '@/modules/shared/presentation';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/modules/shared/presentation/components/ui/dropdown-menu';
import { Message } from '../../domain/entities/Message';

interface MessageActionsProps {
  message: Message;
  currentUserId: string;
  onStartEdit: (message: Message) => void;
  onDelete: (messageId: string) => void;
}

export const MessageActions: React.FC<MessageActionsProps> = ({
  message,
  currentUserId,
  onStartEdit,
  onDelete
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Vérifier les permissions
  const canEdit = message.canBeEdited() && message.senderId === currentUserId;
  const canDelete = message.canBeDeleted() && message.senderId === currentUserId;

  // Ne pas afficher les actions si aucune action n'est disponible
  if (!canEdit && !canDelete) {
    return null;
  }

  const handleEdit = () => {
    onStartEdit(message);
  };

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = () => {
    onDelete(message.id);
    setShowDeleteConfirm(false);
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
  };

  // Confirmation de suppression
  if (showDeleteConfirm) {
    return (
      <div className="flex items-center gap-1 bg-red-500/20 backdrop-blur-sm border border-red-400/30 rounded-lg px-3 py-2 shadow-lg">
        <span className="text-xs text-red-300">Supprimer ?</span>
        <Button
          size="sm"
          onClick={handleDeleteConfirm}
          className="h-6 px-2 text-xs bg-red-500/80 hover:bg-red-500 text-white border-red-400/30"
        >
          Oui
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={handleDeleteCancel}
          className="h-6 px-2 text-xs bg-white/10 hover:bg-white/20 text-white border-white/20"
        >
          Non
        </Button>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="w-8 h-8 p-0 bg-black/10 hover:bg-black/20 text-black border-black/10 backdrop-blur-sm transition-all duration-300"
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="end" 
        className="w-32 bg-white/10 backdrop-blur-xl border-white/20 shadow-2xl"
      >
        {canEdit && (
          <DropdownMenuItem 
            onClick={handleEdit} 
            className="text-xs text-black hover:bg-black/20 focus:bg-black/20"
          >
            <Edit className="mr-2 h-3 w-3" />
            Edit
          </DropdownMenuItem>
        )}
        {canDelete && (
          <DropdownMenuItem 
            onClick={handleDeleteClick} 
            className="text-xs text-red-300 hover:bg-red-500/20 focus:bg-red-500/20"
          >
            <Trash2 className="mr-2 h-3 w-3" />
            Delete
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}; 
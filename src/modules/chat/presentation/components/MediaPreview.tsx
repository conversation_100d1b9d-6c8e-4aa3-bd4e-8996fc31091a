import React from 'react';
import { Button } from '@/modules/shared/presentation';
import { X, Play } from 'lucide-react';
import { PendingMedia } from '../types/MessageTypes';

interface MediaPreviewProps {
  pendingMediaList: PendingMedia[];
  onRemoveMedia: (index: number) => void;
  disabled?: boolean;
}

export const MediaPreview: React.FC<MediaPreviewProps> = ({
  pendingMediaList,
  onRemoveMedia,
  disabled = false
}) => {
  if (pendingMediaList.length === 0) {
    return null;
  }

  return (
    <div className="p-3 border-t border-white/10 bg-white/5 backdrop-blur-sm">
      <div className="space-y-2">
        <p className="text-xs font-medium text-white/70">
          {pendingMediaList.length} média(s) sélectionné(s)
        </p>
        <div className="flex flex-wrap gap-2">
          {pendingMediaList.map((media, index) => (
            <div key={index} className="relative group">
              <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden shadow-lg">
                {media.type === 'image' ? (
                  <img
                    src={media.preview}
                    alt="Aperçu"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="relative w-full h-full bg-black/20">
                    <video
                      src={media.preview}
                      className="w-full h-full object-cover"
                      muted
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30 backdrop-blur-sm">
                      <Play className="h-3 w-3 text-white drop-shadow-lg" />
                    </div>
                  </div>
                )}
                
                <Button
                  size="sm"
                  onClick={() => onRemoveMedia(index)}
                  disabled={disabled}
                  className="absolute -top-1 -right-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-all duration-300 text-xs bg-red-500/80 hover:bg-red-500 text-white border-red-400/30 shadow-lg backdrop-blur-sm"
                >
                  <X className="h-2.5 w-2.5" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}; 
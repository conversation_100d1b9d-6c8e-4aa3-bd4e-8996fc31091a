import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Button, Input } from '@/modules/shared/presentation';
import { useChat } from '../context/ChatContext';
import { UserSearchResult } from '../context/types/ChatTypes';

interface UserSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectUser: (user: UserSearchResult) => Promise<void>;
}

export const UserSearchModal: React.FC<UserSearchModalProps> = ({
  isOpen,
  onClose,
  onSelectUser,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isSelecting, setIsSelecting] = useState(false);
  const [users, setUsers] = useState<UserSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { state, actions } = useChat();

  // Fonction pour obtenir le statut en ligne en temps réel
  const getUserOnlineStatus = (userId: string): boolean => {
    const chatUser = state.users[userId];
    return chatUser?.isOnline ?? false;
  };

  // Mémoriser les utilisateurs avec leur statut en temps réel
  const usersWithRealTimeStatus = useMemo(() => {
    return users.map(user => ({
      user,
      isOnlineRealTime: getUserOnlineStatus(user.id)
    }));
  }, [users, state.users]);

  // Fonction de recherche d'utilisateurs mémorisée pour éviter les re-créations
  const searchUsers = useCallback(async (term: string) => {
    if (term.length < 2) {
      setUsers([]);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      const results = await actions.searchUsers(term);
      setUsers(results);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la recherche');
      setUsers([]);
    } finally {
      setIsLoading(false);
    }
  }, [actions.searchUsers]);

  // Fonction pour vider les résultats
  const clearResults = useCallback(() => {
    setUsers([]);
    setError(null);
  }, []);

  // Synchroniser les utilisateurs trouvés avec le ChatContext
  // Utilisation d'un Set pour éviter les synchronisations répétées
  const syncedUserIds = useMemo(() => new Set<string>(), []);
  
  useEffect(() => {
    if (users.length > 0) {
      users.forEach(user => {
        // Éviter de synchroniser le même utilisateur plusieurs fois
        if (!syncedUserIds.has(user.id)) {
          syncedUserIds.add(user.id);
          actions.syncUser(
            user.id,
            user.username,
            user.avatar,
            user.isOnline
          );
        }
      });
    }
  }, [users, actions.syncUser, syncedUserIds]);

  // Effet pour déclencher la recherche avec debounce
  useEffect(() => {
    if (searchTerm.length < 2) {
      clearResults();
      return;
    }

    const debounceTimeout = setTimeout(() => {
      searchUsers(searchTerm);
    }, 300);

    return () => clearTimeout(debounceTimeout);
  }, [searchTerm, searchUsers, clearResults]);

  // Nettoyer les états quand le modal se ferme
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
      setUsers([]);
      setError(null);
      syncedUserIds.clear();
    }
  }, [isOpen, syncedUserIds]);

  const handleSelectUser = async (user: UserSearchResult) => {
    setIsSelecting(true);
    try {
      await onSelectUser(user);
      onClose();
      setSearchTerm('');
      clearResults();
    } catch (error) {
      console.error('Erreur lors de la sélection de l\'utilisateur:', error);
    } finally {
      setIsSelecting(false);
    }
  };

  const handleClose = () => {
    onClose();
    setSearchTerm('');
    clearResults();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      {/* Modal glassmorphique premium */}
      <div className="relative w-full max-w-md max-h-[80vh] group">
        {/* Effet de brillance d'arrière-plan */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
        
        <div className="relative bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl overflow-hidden flex flex-col">
          {/* Reflet en haut */}
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent"></div>
          
          {/* Header */}
          <div className="p-6 pb-4">
            <div className="flex items-center justify-between mb-1">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-white via-pink-100 to-purple-200 bg-clip-text text-transparent">
                New conversation
              </h2>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={handleClose}
                className="w-8 h-8 p-0 bg-white/5 border border-white/20 text-white/80 hover:text-white hover:bg-white/10 hover:border-white/30 rounded-full transition-all duration-300"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </Button>
            </div>
            <p className="text-white/60 text-sm">Search for users to chat with</p>
          </div>
          
          {/* Contenu */}
          <div className="flex-1 flex flex-col px-6 pb-6 min-h-0">
            {/* Champ de recherche glassmorphique */}
            <div className="mb-6">
              <div className="relative">
                <Input
                  type="text"
                  placeholder="Search users by username or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-blue-400/50 focus:ring-blue-400/20 pr-10"
                  autoFocus
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <svg className="w-4 h-4 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Zone de résultats avec scroll personnalisé */}
            <div className="flex-1 overflow-y-auto min-h-0 -mx-2 px-2">
              {searchTerm.length < 2 ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 mx-auto mb-4 bg-white/5 rounded-2xl flex items-center justify-center">
                    <svg className="w-8 h-8 text-white/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <p className="text-white/60 text-sm">Type at least 2 characters to search...</p>
                </div>
              ) : isLoading ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 mx-auto mb-4 bg-white/5 rounded-2xl flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-2 border-white/30 border-t-white"></div>
                  </div>
                  <p className="text-white/60 text-sm">Searching...</p>
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 mx-auto mb-4 bg-red-500/10 rounded-2xl flex items-center justify-center border border-red-400/30">
                    <svg className="w-8 h-8 text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <p className="text-red-300 text-sm mb-4">Error: {error}</p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => searchUsers(searchTerm)}
                    className="bg-white/5 border-white/20 text-white/80 hover:text-white hover:bg-white/10 hover:border-white/30"
                  >
                    Try again
                  </Button>
                </div>
              ) : users.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 mx-auto mb-4 bg-white/5 rounded-2xl flex items-center justify-center">
                    <svg className="w-8 h-8 text-white/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <p className="text-white/60 text-sm">No users found</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {usersWithRealTimeStatus.map((userWithStatus) => {
                    // Générer un gradient unique basé sur l'ID utilisateur
                    const gradientIndex = parseInt(userWithStatus.user.id.slice(-1), 16) % 6;
                    const gradients = [
                      'from-blue-500 to-purple-600',
                      'from-purple-500 to-pink-600', 
                      'from-pink-500 to-rose-600',
                      'from-green-500 to-teal-600',
                      'from-orange-500 to-red-600',
                      'from-indigo-500 to-blue-600'
                    ];
                    
                    return (
                      <div
                        key={`${userWithStatus.user.id}-${userWithStatus.isOnlineRealTime}`}
                        className="group relative"
                      >
                        {/* Effet de brillance au hover */}
                        <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300 blur-sm"></div>
                        
                        <div
                          className="relative flex items-center justify-between p-4 rounded-2xl bg-white/5 border border-white/10 hover:bg-white/10 hover:border-white/20 cursor-pointer transition-all duration-300 backdrop-blur-sm"
                          onClick={() => handleSelectUser(userWithStatus.user)}
                        >
                          <div className="flex items-center space-x-4">
                            {/* Avatar avec gradient et statut en ligne */}
                            <div className="relative">
                              <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${gradients[gradientIndex]} flex items-center justify-center shadow-lg border border-white/20`}>
                                <span className="text-white font-bold text-lg drop-shadow-sm">
                                  {userWithStatus.user.username[0]?.toUpperCase() || '?'}
                                </span>
                                {/* Reflet */}
                                <div className="absolute inset-1 bg-gradient-to-br from-white/30 to-transparent rounded-lg pointer-events-none"></div>
                              </div>
                              
                              {/* Indicateur de statut en ligne */}
                              {userWithStatus.isOnlineRealTime && (
                                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white/20 animate-pulse shadow-lg"></div>
                              )}
                            </div>
                            
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <p className="font-semibold text-white">{userWithStatus.user.username}</p>
                                {userWithStatus.isOnlineRealTime && (
                                  <span className="px-2 py-0.5 text-xs bg-green-500/20 text-green-300 rounded-full border border-green-400/30">
                                    Onligne
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          <Button
                            size="sm"
                            disabled={isSelecting}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSelectUser(userWithStatus.user);
                            }}
                            className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-blue-500/25 transition-all duration-300 disabled:opacity-50"
                          >
                            {isSelecting ? (
                              <div className="flex items-center gap-2">
                                <div className="animate-spin rounded-full h-3 w-3 border-2 border-white/30 border-t-white"></div>
                                Creating...
                              </div>
                            ) : (
                              'Chat'
                            )}
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
            
            {/* Footer */}
            <div className="mt-6 pt-4 border-t border-white/10">
              <Button 
                variant="outline" 
                onClick={handleClose} 
                className="w-full bg-white/5 border-white/20 text-white/80 hover:text-white hover:bg-white/10 hover:border-white/30 transition-all duration-300"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 
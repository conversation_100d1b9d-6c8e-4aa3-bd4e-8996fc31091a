import React, { useState, useRef } from 'react';
import { cn } from '@/modules/shared/infrastructure';
import { FormatService } from '@/modules/shared/domain/services/FormatService';
import { VideoViewer } from './VideoViewer';

export interface VideoMessageContent {
  url: string;
  originalName: string;
  size: number;
  caption?: string;
  thumbnail?: string;
  duration?: number;
  mimeType?: string;
}

interface VideoMessageProps {
  videoData: VideoMessageContent;
  isFromCurrentUser: boolean;
  className?: string;
  onVideoLoad?: () => void;
}

export const VideoMessage: React.FC<VideoMessageProps> = ({
  videoData,
  isFromCurrentUser,
  className,
  onVideoLoad
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handleVideoLoad = () => {
    setIsLoading(false);
    if (onVideoLoad) {
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          onVideoLoad();
        });
      });
    }
  };

  const handleVideoError = () => {
    setIsLoading(false);
    setHasError(true);
    if (onVideoLoad) {
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          onVideoLoad();
        });
      });
    }
  };

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const openViewer = () => {
    if (!hasError) {
      setIsViewerOpen(true);
    }
  };

  const closeViewer = () => {
    setIsViewerOpen(false);
  };

  const handleVideoClick = (e: React.MouseEvent) => {
    // Si on clique avec le bouton droit ou Ctrl+click, on ouvre le visualiseur
    if (e.ctrlKey || e.metaKey || e.button === 2) {
      e.preventDefault();
      openViewer();
    } else {
      // Sinon, on toggle play/pause
      togglePlay();
    }
  };

  const formattedDuration = videoData.duration ? FormatService.formatDuration(videoData.duration) : '';

  return (
    <>
      <div className={cn('max-w-sm', className)}>
        {/* Video container */}
        <div 
          className={cn(
            'relative rounded-lg overflow-hidden border',
            isFromCurrentUser ? 'bg-primary/10' : 'bg-muted'
          )}
        >
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-muted z-10">
              <div className="animate-spin h-6 w-6 border-2 border-current border-t-transparent rounded-full" />
            </div>
          )}
          
          {hasError ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <div className="w-12 h-12 mb-2 rounded-full bg-destructive/10 flex items-center justify-center">
                <svg 
                  className="w-6 h-6 text-destructive" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
                  />
                </svg>
              </div>
              <p className="text-sm text-muted-foreground mb-1">
                Unable to load video
              </p>
              <p className="text-xs text-muted-foreground">
                Vidéo non disponible
              </p>
            </div>
          ) : (
            <div className="relative">
              <video
                ref={videoRef}
                src={videoData.url}
                poster={videoData.thumbnail}
                onLoadedData={handleVideoLoad}
                onError={handleVideoError}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onEnded={() => setIsPlaying(false)}
                className={cn(
                  'w-full h-auto max-h-64 object-cover cursor-pointer',
                  isLoading && 'opacity-0'
                )}
                onClick={handleVideoClick}
                onContextMenu={(e) => {
                  e.preventDefault();
                  openViewer();
                }}
                controls={false}
              />
              
              {/* Play/Pause overlay */}
              {!isLoading && !hasError && (
                <div 
                  className="absolute inset-0 flex items-center justify-center cursor-pointer opacity-0 hover:opacity-100 transition-opacity bg-black/20"
                  onClick={handleVideoClick}
                >
                  <div className="w-12 h-12 rounded-full bg-black/50 flex items-center justify-center">
                    {isPlaying ? (
                      <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                      </svg>
                    ) : (
                      <svg className="w-6 h-6 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    )}
                  </div>
                </div>
              )}

              {/* Duration badge */}
              {videoData.duration && (
                <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  {formattedDuration}
                </div>
              )}
              
              {/* Fullscreen button */}
              <div className="absolute top-2 right-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    openViewer();
                  }}
                  className="w-8 h-8 rounded-full bg-black/50 hover:bg-black/70 flex items-center justify-center transition-colors"
                  title="Ouvrir en plein écran"
                >
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                  </svg>
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Caption */}
        {videoData.caption && (
          <div className="mt-2">
            <p className="text-sm whitespace-pre-wrap break-words">
              {videoData.caption}
            </p>
          </div>
        )}
      </div>

      {/* Video Viewer */}
      <VideoViewer
        isOpen={isViewerOpen}
        onClose={closeViewer}
        videoData={videoData}
      />
    </>
  );
}; 
import React, { useState, useRef, useEffect } from 'react';
import { Button, Input } from '@/modules/shared/presentation';
import { ImageIcon, VideoIcon, SendIcon } from 'lucide-react';
import { EmojiPicker } from './EmojiPicker';
import { MediaPreview } from './MediaPreview';
import { cn } from '@/modules/shared/infrastructure';
import { PendingMedia } from '../types/MessageTypes';
import { useMediaUpload } from '../hooks/useMediaUpload';
import { useTypingIndicator } from '../hooks/useTypingIndicator';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';

interface MessageSendInputProps {
  onSendMessage: (content: string, mediaList?: PendingMedia[]) => Promise<void>;
  onStartTyping?: (chatId: string, receiverId: string) => void;
  onStopTyping?: (chatId: string, receiverId: string) => void;
  onFocus?: () => void;
  chatId?: string;
  receiverId?: string;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

export const MessageSendInput: React.FC<MessageSendInputProps> = ({
  onSendMessage,
  onStartTyping,
  onStopTyping,
  onFocus,
  chatId,
  receiverId,
  disabled = false,
  placeholder = "Type your message...",
  className
}) => {
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    pendingMediaList,
    imageInputRef,
    videoInputRef,
    imageValidationRules,
    videoValidationRules,
    handleImageSelect,
    handleVideoSelect,
    handleImageChange,
    handleVideoChange,
    removeMedia,
    clearMediaList
  } = useMediaUpload();

  const { handleMessageChange: handleTypingChange, cleanup } = useTypingIndicator({
    chatId,
    receiverId,
    onStartTyping,
    onStopTyping
  });

  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const handleInputFocus = () => {
    if (onFocus) {
      onFocus();
    }
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMessage = e.target.value;
    setMessage(newMessage);
    handleTypingChange(newMessage);
  };

  const handleFocus = () => {
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 0);
  };

  const handleSend = async () => {
    const trimmedMessage = message.trim();
    if ((!trimmedMessage && pendingMediaList.length === 0) || isSending) return;

    try {
      setIsSending(true);
      
      const mediaListToSend = pendingMediaList.length > 0 ? 
        pendingMediaList.map(media => ({
          ...media,
          caption: media.caption || trimmedMessage
        })) : undefined;

      await onSendMessage(trimmedMessage, mediaListToSend);
      setMessage('');
      clearMediaList();
      
      handleFocus();
    } catch (error) {
      console.error('Échec de l\'envoi du message:', error);
    } finally {
      setIsSending(false);
    }
  };

  const { handleKeyPress } = useKeyboardShortcuts({
    onSend: handleSend,
    disabled: disabled || isSending
  });

  const handleEmojiSelect = (emoji: string) => {
    const input = inputRef.current;
    if (!input) return;

    const start = input.selectionStart || 0;
    const end = input.selectionEnd || 0;
    const newMessage = message.slice(0, start) + emoji + message.slice(end);
    
    setMessage(newMessage);
    handleTypingChange(newMessage);
      
    setTimeout(() => {
      input.focus();
      const newCursorPosition = start + emoji.length;
      input.setSelectionRange(newCursorPosition, newCursorPosition);
    }, 0);
  };

  const getPlaceholder = () => {
    if (pendingMediaList.length > 0) {
      return "Ajouter un message à vos médias...";
    }
    return placeholder;
  };

  const isDisabled = disabled || isSending || (!message.trim() && pendingMediaList.length === 0);

  return (
    <div className={cn('bg-black/30 backdrop-blur-xl border-t border-black/10', className)}>
      {/* Inputs cachés pour les fichiers */}
      <input
        ref={imageInputRef}
        type="file"
        accept={imageValidationRules.allowedMimeTypes.join(',')}
        onChange={handleImageChange}
        className="hidden"
        multiple
      />
      <input
        ref={videoInputRef}
        type="file"
        accept={videoValidationRules.allowedMimeTypes.join(',')}
        onChange={handleVideoChange}
        className="hidden"
        multiple
      />

      {/* Prévisualisation des médias */}
      <MediaPreview
        pendingMediaList={pendingMediaList}
        onRemoveMedia={removeMedia}
        disabled={isSending}
      />

      {/* Zone d'input */}
      <div className="flex gap-3 p-4">
        <Button
          variant="outline"
          size="sm"
          disabled={disabled || isSending}
          onClick={handleImageSelect}
          className="bg-white/10 hover:bg-white/20 text-white border-white/20 backdrop-blur-sm transition-all duration-300"
        >
          <ImageIcon className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          disabled={disabled || isSending}
          onClick={handleVideoSelect}
          className="bg-white/10 hover:bg-white/20 text-white border-white/20 backdrop-blur-sm transition-all duration-300"
        >
          <VideoIcon className="h-4 w-4" />
        </Button>
        
        <EmojiPicker 
          onEmojiSelect={handleEmojiSelect}
          disabled={disabled || isSending}
        />
        
        <Input
          ref={inputRef}
          value={message}
          onChange={handleMessageChange}
          onKeyPress={handleKeyPress}
          onFocus={handleInputFocus}
          placeholder={getPlaceholder()}
          disabled={disabled || isSending}
          className="flex-1 bg-black/10 border-black/20 text-white placeholder:text-white/50 backdrop-blur-sm focus:bg-black/15 focus:border-black/30 transition-all duration-300"
        />
        
        <Button
          onClick={handleSend}
          disabled={isDisabled}
          size="sm"
          className={cn(
            'transition-all duration-300 backdrop-blur-sm',
            isDisabled 
              ? 'bg-white/10 text-white/50 border-white/10' 
              : 'bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white border-blue-400/50 shadow-lg hover:shadow-blue-500/25'
          )}
        >
          <SendIcon className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}; 
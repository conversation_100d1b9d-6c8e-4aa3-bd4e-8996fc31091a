import React from 'react';
import { cn } from '@/modules/shared/infrastructure';

interface TypingIndicatorProps {
  typingUsers: string[];
  userNames: Record<string, string>;
  className?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  typingUsers,
  userNames,
  className
}) => {
  if (typingUsers.length === 0) {
    return null;
  }

  const getTypingText = () => {
    const names = typingUsers.map(userId => userNames[userId] || 'Utilisateur');
    
    if (names.length === 1) {
      return `${names[0]} est en train d'écrire...`;
    } else if (names.length === 2) {
      return `${names[0]} et ${names[1]} sont en train d'écrire...`;
    } else {
      return `${names[0]} et ${names.length - 1} autres sont en train d'écrire...`;
    }
  };

  const customBounceKeyframes = `
    @keyframes custom-high-bounce {
      0%, 100% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
      }
      50% {
        transform: translateY(-12px);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
      }
    }
  `;

  return (
    <div className={cn('flex items-center gap-2 px-4 py-2 text-sm text-muted-foreground', className)}>
      <style dangerouslySetInnerHTML={{ __html: customBounceKeyframes }} />
      <div className="flex gap-1">
        <div 
          className="w-2 h-2 bg-primary rounded-full" 
          style={{ 
            animation: 'custom-high-bounce 1.4s infinite',
            animationDelay: '0ms' 
          }} 
        />
        <div 
          className="w-2 h-2 bg-primary rounded-full" 
          style={{ 
            animation: 'custom-high-bounce 1.4s infinite',
            animationDelay: '160ms' 
          }} 
        />
        <div 
          className="w-2 h-2 bg-primary rounded-full" 
          style={{ 
            animation: 'custom-high-bounce 1.4s infinite',
            animationDelay: '320ms' 
          }} 
        />
      </div>
      <span>{getTypingText()}</span>
    </div>
  );
}; 
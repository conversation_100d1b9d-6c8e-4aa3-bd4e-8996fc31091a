import React, { useState, useRef } from 'react';
import { Modal } from '@/modules/shared/presentation/components/ui/modal';
import { VideoMessageContent } from './VideoMessage';
import { MediaViewerHeader } from './MediaViewerHeader';

interface VideoViewerProps {
  isOpen: boolean;
  onClose: () => void;
  videoData: VideoMessageContent;
}

export const VideoViewer: React.FC<VideoViewerProps> = ({
  isOpen,
  onClose,
  videoData
}) => {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handleVideoPlay = () => {
    setIsVideoPlaying(true);
  };

  const handleVideoPause = () => {
    setIsVideoPlaying(false);
  };

  const toggleVideoPlay = () => {
    if (videoRef.current) {
      if (isVideoPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
    }
  };

  const downloadVideo = () => {
    const link = document.createElement('a');
    link.href = videoData.url;
    link.download = videoData.originalName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const openInNewTab = () => {
    window.open(videoData.url, '_blank');
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="w-auto h-auto">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-2xl overflow-hidden">
        {/* Header */}
        <MediaViewerHeader
          onDownload={downloadVideo}
          onOpenInNewTab={openInNewTab}
          onClose={onClose}
        />

        {/* Contenu vidéo */}
        <div>
          <div className="relative">
            <video
              ref={videoRef}
              src={videoData.url}
              poster={videoData.thumbnail}
              onPlay={handleVideoPlay}
              onPause={handleVideoPause}
              onEnded={() => setIsVideoPlaying(false)}
              className="max-w-full max-h-[70vh] object-contain mx-auto rounded"
              controls
            />
            
            {/* Overlay de contrôle personnalisé */}
            <div 
              className="absolute inset-0 flex items-center justify-center cursor-pointer opacity-0 hover:opacity-100 transition-opacity bg-black/20 rounded"
              onClick={toggleVideoPlay}
            >
              <div className="w-16 h-16 rounded-full bg-black/50 flex items-center justify-center">
                {isVideoPlaying ? (
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                  </svg>
                ) : (
                  <svg className="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                )}
              </div>
            </div>
          </div>

          {/* Caption */}
          {videoData.caption && (
            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {videoData.caption}
              </p>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
}; 
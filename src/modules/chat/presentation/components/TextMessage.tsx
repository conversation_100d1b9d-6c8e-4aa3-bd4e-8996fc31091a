import React from 'react';
import { Message } from '../../domain/entities/Message';
import { cn } from '@/modules/shared/infrastructure';

interface TextMessageProps {
  message: Message;
  isFromCurrentUser: boolean;
  className?: string;
}

export const TextMessage: React.FC<TextMessageProps> = ({
  message,
  isFromCurrentUser,
  className
}) => {
  return (
    <div
      className={cn(
        'px-4 py-3 rounded-2xl max-w-full break-words backdrop-blur-xl border shadow-lg transition-all duration-300',
        isFromCurrentUser
          ? 'bg-gradient-to-br from-pink-500/80 to-purple-600/80 text-white border-white/20 rounded-br-sm shadow-blue-500/20'
          : 'bg-white text-black border-white/10 rounded-bl-sm hover:bg-white/15',
        className
      )}
    >
      <p className="text-sm whitespace-pre-wrap leading-relaxed">
        {message.content.getValue()}
      </p>
    </div>
  );
}; 
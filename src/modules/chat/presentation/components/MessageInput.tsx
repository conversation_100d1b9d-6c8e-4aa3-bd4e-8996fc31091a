import React from 'react';
import { MessageEditInput } from './MessageEditInput';
import { MessageSendInput } from './MessageSendInput';
import { PendingMedia } from '../types/MessageTypes';
import { Message } from '../../domain/entities/Message';

interface MessageInputProps {
  onSendMessage: (content: string, mediaList?: PendingMedia[]) => Promise<void>;
  onStartTyping?: (chatId: string, receiverId: string) => void;
  onStopTyping?: (chatId: string, receiverId: string) => void;
  onFocus?: () => void;
  chatId?: string;
  receiverId?: string;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  editingMessage?: Message | null;
  onSaveEdit?: (newContent: string) => Promise<void>;
  onCancelEdit?: () => void;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  onStartTyping,
  onStopTyping,
  onFocus,
  chatId,
  receiverId,
  disabled = false,
  placeholder = "Type your message...",
  className,
  editingMessage,
  onSaveEdit,
  onCancelEdit
}) => {
  const isEditMode = editingMessage !== null && editingMessage !== undefined;

  if (isEditMode && editingMessage && onSaveEdit && onCancelEdit) {
    return (
      <MessageEditInput
        editingMessage={editingMessage}
        onSaveEdit={onSaveEdit}
        onCancelEdit={onCancelEdit}
        onStartTyping={onStartTyping}
        onStopTyping={onStopTyping}
        chatId={chatId}
        receiverId={receiverId}
        disabled={disabled}
        className={className}
      />
    );
  }

  return (
    <MessageSendInput
      onSendMessage={onSendMessage}
      onStartTyping={onStartTyping}
      onStopTyping={onStopTyping}
      onFocus={onFocus}
      chatId={chatId}
      receiverId={receiverId}
      disabled={disabled}
      placeholder={placeholder}
      className={className}
    />
  );
}; 
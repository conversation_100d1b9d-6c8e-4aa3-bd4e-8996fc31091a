import React, { createContext, useContext, useReducer, useMemo, useEffect } from 'react';
import { ChatService } from './services/ChatService';
import { chatReducer } from './reducers/chatReducer';
import { useChatActions } from './hooks/useChatActions';
import { useChatWebSocket } from './hooks/useChatWebSocket';
import {
  ChatContextType,
  ChatProviderProps,
  createInitialChatState, UserSearchResult
} from './types/ChatTypes';
import { CreateChatUseCase } from '../../application/usecases/CreateChatUseCase';
import { HttpChatRepository } from '../../infrastructure/repositories/HttpChatRepository';
import { MessageType } from '../../domain/valueobjects/MessageType';

// Contexte
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Provider refactorisé avec injection de dépendances
export const ChatProvider: React.FC<ChatProviderProps> = ({ children, auth, userSearch }) => {
  const [state, dispatch] = useReducer(chatReducer, createInitialChatState(auth, userSearch));
  
  // Instance du service de chat (singleton)
  const chatService = useMemo(() => new ChatService(), []);
  
  // Use cases pour les actions avancées
  const chatRepository = useMemo(() => new HttpChatRepository(), []);
  const createChatUseCase = useMemo(() => new CreateChatUseCase(chatRepository), [chatRepository]);
  
  // Hooks personnalisés pour les actions et WebSocket
  const chatActions = useChatActions({ state, dispatch, chatService });
  const { connectWebSocket, disconnectWebSocket } = useChatWebSocket({ state, dispatch, chatService });

  // Mettre à jour les dépendances dans l'état quand elles changent
  useEffect(() => {
    dispatch({ type: 'SET_AUTH', payload: auth });
  }, [auth]);

  useEffect(() => {
    dispatch({ type: 'SET_USER_SEARCH', payload: userSearch });
  }, [userSearch]);

  // Fonctions stables pour éviter les re-créations
  const stableSearchUsers = useMemo(() => 
    (term: string) => state.userSearch.searchUsers(term), 
    [state.userSearch.searchUsers]
  );

  // Actions qui utilisent les dépendances injectées
  const enhancedActions = useMemo(() => ({
    ...chatActions,
    connectWebSocket,
    disconnectWebSocket,
    
    // Action pour rechercher des utilisateurs
    searchUsers: stableSearchUsers,

    // Action pour marquer tous les messages non lus d'un chat comme lus immédiatement
    markAllUnreadAsReadLocally: (chatId: string, currentUserId: string) => {
      const messages = state.messages[chatId] || [];
      const unreadMessages = messages.filter(msg => 
        msg.receiverId === currentUserId && !msg.status.isRead()
      );
      
      unreadMessages.forEach(message => {
        dispatch({
          type: 'UPDATE_MESSAGE_STATUS',
          payload: {
            messageId: message.id,
            status: 'read',
            readAt: new Date()
          }
        });
      });
    },

    // Actions qui utilisent l'utilisateur authentifié automatiquement
    sendMessageAsCurrentUser: async (content: string, receiverId: string, type?: MessageType) => {
      if (!state.auth.currentUser?.id) {
        throw new Error('User not authenticated');
      }
      return chatActions.sendMessage(content, receiverId, type, state.auth.currentUser.id);
    },

    loadChatsForCurrentUser: async () => {
      if (!state.auth.currentUser?.id) {
        throw new Error('User not authenticated');
      }
      return chatActions.loadChats(state.auth.currentUser.id);
    },

    loadChatHistoryForCurrentUser: async (otherUserId: string) => {
      if (!state.auth.currentUser?.id) {
        throw new Error('User not authenticated');
      }
      return chatActions.loadChatHistory(state.auth.currentUser.id, otherUserId);
    },

    // Fonction pour créer/récupérer un chat avec un autre utilisateur
    selectUserForChat: async (selectedUser: UserSearchResult) => {
      if (!state.auth.currentUser?.id) {
        throw new Error('User not authenticated');
      }

      // Synchroniser l'utilisateur sélectionné avec le ChatContext
      chatActions.syncUser(
        selectedUser.id,
        selectedUser.username,
        selectedUser.avatar,
        selectedUser.isOnline,
        selectedUser.blockStatus
      );

      // Créer ou récupérer le chat
      const chat = await createChatUseCase.execute([state.auth.currentUser.id, selectedUser.id]);
      await chatActions.selectChat(chat);
      await chatActions.loadChats(state.auth.currentUser.id);
      return chat;
    }
  }), [chatActions, connectWebSocket, disconnectWebSocket, stableSearchUsers, createChatUseCase]);

  const contextValue = useMemo(() => ({
    state,
    actions: enhancedActions
  }), [state, enhancedActions]);

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};

// Hook pour utiliser le contexte
export const useChat = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat doit être utilisé dans un ChatProvider');
  }
  return context;
};

// Exporter les types pour la compatibilité
export type { ChatUser, ChatState, ChatAction, ChatContextType, ChatProviderProps, AuthDependency, UserSearchDependency, UserSearchResult } from './types/ChatTypes'; 
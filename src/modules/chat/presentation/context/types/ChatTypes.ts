import { Message } from '../../../domain/entities/Message';
import { Chat } from '../../../domain/entities/Chat';
import { MessageType } from '../../../domain/valueobjects/MessageType';
import { UserBlockStatus } from '@/modules/users/domain/entities/User';

// Types d'utilisateur du chat
export interface ChatUser {
  id: string;
  name: string;
  avatar?: string;
  isOnline?: boolean;
  email?: string;
  blockedStatus?: UserBlockStatus;
}

// Types pour l'injection de dépendances
export interface AuthDependency {
  currentUser: {
    id: string;
    email: string;
    username: string;
    blockStatus?: UserBlockStatus;
  } | null;
  isAuthenticated: boolean;
}

export interface UserSearchDependency {
  searchUsers: (term: string) => Promise<UserSearchResult[]>;
}

export interface UserSearchResult {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  isOnline?: boolean;
  blockStatus?: UserBlockStatus;
}

// Props pour le ChatProvider
export interface ChatProviderProps {
  children: React.ReactNode;
  auth: AuthDependency;
  userSearch: UserSearchDependency;
}

// État global du chat
export interface ChatState {
  chats: Chat[];
  activeChat: Chat | null;
  messages: Record<string, Message[]>; // chatId -> messages
  users: Record<string, ChatUser>; // userId -> user
  typingUsers: Record<string, Set<string>>; // chatId -> Set of userIds who are typing
  isLoading: boolean;
  error: string | null;
  isConnected: boolean;
  currentUserId: string | null;
  editingMessage: Message | null; // Message en cours d'édition
  // Ajout des dépendances injectées dans l'état
  auth: AuthDependency;
  userSearch: UserSearchDependency;
}

// Actions du reducer
export type ChatAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CONNECTED'; payload: boolean }
  | { type: 'SET_CHATS'; payload: Chat[] }
  | { type: 'SET_ACTIVE_CHAT'; payload: Chat | null }
  | { type: 'ADD_MESSAGE'; payload: { chatId: string; message: Message } }
  | { type: 'SET_MESSAGES'; payload: { chatId: string; messages: Message[] } }
  | { type: 'UPDATE_MESSAGE'; payload: { chatId: string; message: Message } }
  | { type: 'DELETE_MESSAGE'; payload: { chatId: string; messageId: string } }
  | { type: 'SET_EDITING_MESSAGE'; payload: Message | null }
  | { type: 'SET_USER_STATUS'; payload: { userId: string; isOnline: boolean } }
  | { type: 'SYNC_USER'; payload: { userId: string; name: string; avatar?: string; isOnline?: boolean, blockStatus?: UserBlockStatus } }
  | { type: 'ADD_CHAT'; payload: Chat }
  | { type: 'SET_CURRENT_USER_ID'; payload: string | null }
  | { type: 'UPDATE_MESSAGE_STATUS'; payload: { messageId: string; status: string; readAt?: Date } }
  | { type: 'SET_USERS'; payload: Record<string, ChatUser> }
  | { type: 'ADD_USER'; payload: ChatUser }
  | { type: 'SET_USER_TYPING'; payload: { chatId: string; userId: string; isTyping: boolean } }
  | { type: 'CLEAR_USER_TYPING_ALL'; payload: { userId: string } }
  | { type: 'SET_AUTH'; payload: AuthDependency }
  | { type: 'SET_USER_SEARCH'; payload: UserSearchDependency };

// Interface du contexte
export interface ChatContextType {
  state: ChatState;
  actions: {
    sendMessage: (content: string, receiverId: string, type?: MessageType, senderId?: string) => Promise<void>;
    uploadImage: (file: File, receiverId: string, caption?: string) => Promise<void>;
    uploadVideo: (file: File, receiverId: string, caption?: string) => Promise<void>;
    loadChats: (userId?: string) => Promise<void>;
    selectChat: (chat: Chat) => Promise<void>;
    loadChatHistory: (userId: string, otherUserId: string) => Promise<void>;
    markMessageAsRead: (messageId: string) => Promise<void>;
    markAllUnreadAsReadLocally: (chatId: string, currentUserId: string) => void;
    connectWebSocket: (token: string) => Promise<void>;
    disconnectWebSocket: () => void;
    setCurrentUserId: (userId: string) => void;
    syncUser: (userId: string, name: string, avatar?: string, isOnline?: boolean, blockStatus?: UserBlockStatus) => void;
    startTyping: (chatId: string, receiverId: string) => void;
    stopTyping: (chatId: string, receiverId: string) => void;
    // Nouvelles actions pour la recherche d'utilisateurs
    searchUsers: (term: string) => Promise<UserSearchResult[]>;
    // Actions qui utilisent l'utilisateur authentifié automatiquement
    sendMessageAsCurrentUser: (content: string, receiverId: string, type?: MessageType) => Promise<void>;
    loadChatsForCurrentUser: () => Promise<void>;
    loadChatHistoryForCurrentUser: (otherUserId: string) => Promise<void>;
    selectUserForChat: (selectedUser: UserSearchResult) => Promise<Chat>;
    // Nouvelles actions pour la modification et suppression des messages
    startEditingMessage: (message: Message) => void;
    cancelEditingMessage: () => void;
    saveEditedMessage: (newContent: string) => Promise<void>;
    deleteMessage: (messageId: string) => Promise<void>;
  };
}

// État initial mis à jour
export const createInitialChatState = (auth: AuthDependency, userSearch: UserSearchDependency): ChatState => ({
  chats: [],
  activeChat: null,
  messages: {},
  users: {},
  typingUsers: {},
  isLoading: false,
  error: null,
  isConnected: false,
  currentUserId: auth.currentUser?.id || null,
  editingMessage: null,
  auth,
  userSearch
});

// État initial par défaut (pour compatibilité)
export const initialChatState: ChatState = {
  chats: [],
  activeChat: null,
  messages: {},
  users: {},
  typingUsers: {},
  isLoading: false,
  error: null,
  isConnected: false,
  currentUserId: null,
  editingMessage: null,
  auth: { currentUser: null, isAuthenticated: false },
  userSearch: { searchUsers: async () => [] }
}; 
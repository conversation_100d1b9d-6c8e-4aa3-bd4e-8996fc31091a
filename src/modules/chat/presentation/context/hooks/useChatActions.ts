import { useCallback } from 'react';
import { MessageType } from '../../../domain/valueobjects/MessageType';
import { Chat } from '../../../domain/entities/Chat';
import { Message } from '../../../domain/entities/Message';
import { ChatService } from '../services/ChatService';
import { ChatState, ChatAction } from '../types/ChatTypes';
import { useImageUpload } from '../../hooks/useImageUpload';
import { useVideoUpload } from '../../hooks/useVideoUpload';
import { chatWebSocketService } from '../../../infrastructure/services/ChatWebSocketService';
import { UserBlockStatus } from '@/modules/users/domain/entities/User';

interface UseChatActionsProps {
  state: ChatState;
  dispatch: React.Dispatch<ChatAction>;
  chatService: ChatService;
}

export const useChatActions = ({ state, dispatch, chatService }: UseChatActionsProps) => {
  const { uploadImage: uploadImageHook } = useImageUpload();
  const { uploadVideo: uploadVideoHook } = useVideoUpload();
  
  const setCurrentUserId = useCallback((userId: string) => {
    dispatch({ type: 'SET_CURRENT_USER_ID', payload: userId });
  }, [dispatch]);

  const sendMessage = useCallback(async (
    content: string, 
    receiverId: string, 
    type: MessageType = MessageType.text(), 
    senderId?: string
  ) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const currentUserId = senderId || state.currentUserId;
      if (!currentUserId) {
        throw new Error('Utilisateur non authentifié');
      }
      
      await chatService.sendMessage({
        content,
        senderId: currentUserId,
        receiverId,
        type
      });

      dispatch({ type: 'SET_ERROR', payload: null });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Échec de l\'envoi du message' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [state.currentUserId, dispatch, chatService]);

  const uploadImage = useCallback(async (
    file: File,
    receiverId: string,
    caption?: string
  ) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const result = await uploadImageHook(file, receiverId, caption);
      
      // L'image a été uploadée avec succès, le message sera reçu via WebSocket
      console.log('Image uploadée avec succès:', result);
      
      dispatch({ type: 'SET_ERROR', payload: null });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Échec de l\'upload de l\'image' });
      throw error; // Re-throw pour que le composant puisse gérer l'erreur
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [uploadImageHook, dispatch]);

  const uploadVideo = useCallback(async (
    file: File,
    receiverId: string,
    caption?: string
  ) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const result = await uploadVideoHook(file, receiverId, caption);
      
      // La vidéo a été uploadée avec succès, le message sera reçu via WebSocket
      console.log('Vidéo uploadée avec succès:', result);
      
      dispatch({ type: 'SET_ERROR', payload: null });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Échec de l\'upload de la vidéo' });
      throw error; // Re-throw pour que le composant puisse gérer l'erreur
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [uploadVideoHook, dispatch]);

  const loadChats = useCallback(async (userId?: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const currentUserId = userId || state.currentUserId;
      if (!currentUserId) {
        throw new Error('Utilisateur non authentifié');
      }
      
      const chats = await chatService.loadUserChats(currentUserId);
      
      // Synchroniser tous les participants des chats
      chats.forEach(chat => {
        chat.getParticipants().forEach(participant => {
          dispatch({ 
            type: 'SYNC_USER', 
            payload: { 
              userId: participant.getId(), 
              name: participant.getName(),
              avatar: participant.getAvatar(),
              isOnline: participant.isUserOnline(),
              blockStatus: participant.getBlockStatus()
            } 
          });
        });
      });
      
      dispatch({ type: 'SET_CHATS', payload: chats });
      dispatch({ type: 'SET_ERROR', payload: null });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Échec du chargement des chats' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [state.currentUserId, dispatch, chatService]);

  const selectChat = useCallback(async (chat: Chat) => {
    try {
      // Synchroniser les participants du chat avec le state users
      chat.getParticipants().forEach(participant => {
        dispatch({ 
          type: 'SYNC_USER', 
          payload: { 
            userId: participant.getId(), 
            name: participant.getName(),
            avatar: participant.getAvatar(),
            isOnline: participant.isUserOnline(),
            // blockStatus: participant.getBlockStatus()
          } 
        });
      });
      
      dispatch({ type: 'SET_ACTIVE_CHAT', payload: chat });
      dispatch({ type: 'SET_ERROR', payload: null });
      
      let messagesToCheck: Message[] = [];
      
      // Charger l'historique du chat s'il n'est pas déjà chargé
      if (!state.messages[chat.id] || state.messages[chat.id].length === 0) {
        dispatch({ type: 'SET_LOADING', payload: true });
        
        const currentUserId = state.currentUserId;
        if (currentUserId) {
          const otherParticipant = chat.getOtherParticipant(currentUserId);
          if (otherParticipant) {
            const messages = await chatService.loadChatHistory({
              userId: currentUserId,
              otherUserId: otherParticipant.getId(),
              limit: 50
            });
            
            dispatch({ type: 'SET_MESSAGES', payload: { chatId: chat.id, messages } });
            messagesToCheck = messages;
          }
        }
      } else {
        messagesToCheck = state.messages[chat.id];
      }
      
      // Marquer tous les messages non lus comme lus pour l'utilisateur actuel
      const currentUserId = state.currentUserId;
      if (currentUserId && messagesToCheck.length > 0) {
        const unreadMessages = messagesToCheck.filter(msg => 
          msg.receiverId === currentUserId && !msg.status.isRead()
        );
        
        for (const message of unreadMessages) {
          try {
            await chatService.markMessageAsRead(message.id);
          } catch (error) {
            console.error(`Échec du marquage du message ${message.id} comme lu:`, error);
          }
        }
      }
      
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      console.error('Erreur lors de la sélection du chat:', error);
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Échec de la sélection du chat' });
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [state.messages, state.currentUserId, dispatch, chatService]);

  const loadChatHistory = useCallback(async (userId: string, otherUserId: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const messages = await chatService.loadChatHistory({
        userId,
        otherUserId,
        limit: 50
      });

      const chatId = chatService.getChatIdByParticipants(state.chats, userId, otherUserId);
      if (chatId) {
        dispatch({ type: 'SET_MESSAGES', payload: { chatId, messages } });
      }

      dispatch({ type: 'SET_ERROR', payload: null });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Échec du chargement de l\'historique du chat' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [state.chats, dispatch, chatService]);

  const markMessageAsRead = useCallback(async (messageId: string) => {
    try {
      await chatService.markMessageAsRead(messageId);
    } catch (error) {
      console.error('Échec du marquage du message comme lu:', error);
    }
  }, [chatService]);

  const syncUser = useCallback((userId: string, name: string, avatar?: string, isOnline?: boolean, blockStatus?:UserBlockStatus) => {
    dispatch({ type: 'SYNC_USER', payload: { userId, name, avatar, isOnline, blockStatus } });
  }, [dispatch]);

  const startTyping = useCallback((chatId: string, receiverId: string) => {
    if (chatWebSocketService.isConnected()) {
      chatWebSocketService.startTyping({ chatId, receiverId });
    }
  }, []);

  const stopTyping = useCallback((chatId: string, receiverId: string) => {
    if (chatWebSocketService.isConnected()) {
      chatWebSocketService.stopTyping({ chatId, receiverId });
    }
  }, []);

  // Nouvelles actions pour l'édition de messages
  const startEditingMessage = useCallback((message: Message) => {
    // Vérifier que l'utilisateur peut modifier ce message
    if (!message.canBeEdited() || message.senderId !== state.currentUserId) {
      dispatch({ type: 'SET_ERROR', payload: 'Vous ne pouvez pas modifier ce message' });
      return;
    }
    
    dispatch({ type: 'SET_EDITING_MESSAGE', payload: message });
  }, [state.currentUserId, dispatch]);

  const cancelEditingMessage = useCallback(() => {
    dispatch({ type: 'SET_EDITING_MESSAGE', payload: null });
  }, [dispatch]);

  const saveEditedMessage = useCallback(async (newContent: string) => {
    if (!state.editingMessage) {
      return;
    }

    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Trouver le chat qui contient ce message
      let chatId: string | null = null;
      for (const [cId, messages] of Object.entries(state.messages)) {
        if (messages.find(msg => msg.id === state.editingMessage!.id)) {
          chatId = cId;
          break;
        }
      }
      
      if (!chatId) {
        throw new Error('Chat non trouvé pour ce message');
      }
      
      // Appeler l'API pour mettre à jour le message sur le serveur
      const updatedMessage = await chatService.updateMessage(state.editingMessage.id, newContent);
      
      // Mettre à jour dans le state local avec la réponse du serveur
      dispatch({ 
        type: 'UPDATE_MESSAGE', 
        payload: { chatId, message: updatedMessage } 
      });
      
      // Effacer le mode édition
      dispatch({ type: 'SET_EDITING_MESSAGE', payload: null });
      
      dispatch({ type: 'SET_ERROR', payload: null });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Échec de la modification du message' });
      // En cas d'erreur, on peut remettre le message original ou laisser l'utilisateur réessayer
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [state.editingMessage, state.messages, dispatch, chatService]);

  const deleteMessage = useCallback(async (messageId: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Trouver le message à supprimer
      let messageToDelete: Message | null = null;
      let chatId: string | null = null;
      
      for (const [cId, messages] of Object.entries(state.messages)) {
        const foundMessage = messages.find(msg => msg.id === messageId);
        if (foundMessage) {
          messageToDelete = foundMessage;
          chatId = cId;
          break;
        }
      }
      
      if (!messageToDelete || !chatId) {
        throw new Error('Message non trouvé');
      }
      
      // Vérifier si l'utilisateur peut supprimer ce message
      if (!messageToDelete.canBeDeleted() || messageToDelete.senderId !== state.currentUserId) {
        throw new Error('Vous ne pouvez pas supprimer ce message');
      }
      
      // Appeler l'API pour supprimer le message sur le serveur
      await chatService.deleteMessage(messageId);
      
      // Supprimer du state local après confirmation du serveur
      dispatch({ 
        type: 'DELETE_MESSAGE', 
        payload: { chatId, messageId } 
      });
      
      dispatch({ type: 'SET_ERROR', payload: null });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Échec de la suppression du message' });
      // En cas d'erreur, le message reste dans l'interface
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [state.messages, state.currentUserId, dispatch, chatService]);

  return {
    setCurrentUserId,
    sendMessage,
    uploadImage,
    uploadVideo,
    loadChats,
    selectChat,
    loadChatHistory,
    markMessageAsRead,
    syncUser,
    startTyping,
    stopTyping,
    startEditingMessage,
    cancelEditingMessage,
    saveEditedMessage,
    deleteMessage
  };
}; 
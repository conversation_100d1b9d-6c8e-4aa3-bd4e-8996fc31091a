import { useCallback, useRef, useEffect } from 'react';
import { chatWebSocketService } from '../../../infrastructure/services/ChatWebSocketService';
import type { ChatWebSocketEventHandlers } from '../../../infrastructure/services/ChatWebSocketService';
import type { MessageReceivedData, UserStatusData, ChatError } from '../../../infrastructure/api/types/ChatTypes';
import { ChatService } from '../services/ChatService';
import { ChatState, ChatAction } from '../types/ChatTypes';

interface UseChatWebSocketProps {
  state: ChatState;
  dispatch: React.Dispatch<ChatAction>;
  chatService: ChatService;
}

export const useChatWebSocket = ({ state, dispatch, chatService }: UseChatWebSocketProps) => {
  // Utiliser une ref pour avoir accès aux valeurs les plus récentes du state
  const stateRef = useRef(state);
  
  // Mettre à jour la ref à chaque changement du state
  useEffect(() => {
    stateRef.current = state;
  }, [state]);
  
  const connectWebSocket = useCallback(async (token: string) => {
    try {
      const handlers: ChatWebSocketEventHandlers = {
        onConnect: () => {
          dispatch({ type: 'SET_CONNECTED', payload: true });
        },
        onDisconnect: () => {
          dispatch({ type: 'SET_CONNECTED', payload: false });
        },
        onMessageReceived: (data: MessageReceivedData) => {
          const message = chatService.convertDtoToMessage(data.message, data.chatId);
          dispatch({ 
            type: 'ADD_MESSAGE', 
            payload: { 
              chatId: data.chatId, 
              message: message
            } 
          });
          
          // Utiliser stateRef.current pour accéder aux valeurs les plus récentes
          const currentState = stateRef.current;
          const currentUserId = currentState.currentUserId;
          const isCurrentlyViewingThisChat = currentState.activeChat?.id === data.chatId;
          const isMessageForCurrentUser = message.receiverId === currentUserId;
          
          if (isCurrentlyViewingThisChat && isMessageForCurrentUser && currentUserId) {
            // Marquer le message comme lu puisque nous regardons activement la conversation
            chatService.markMessageAsRead(message.id).catch(error => {
              console.error(`Échec du marquage automatique du message ${message.id} comme lu:`, error);
            });
          }
        },
        onMessageSent: (data: MessageReceivedData) => {
          const message = chatService.convertDtoToMessage(data.message, data.chatId);
          dispatch({ 
            type: 'ADD_MESSAGE', 
            payload: { 
              chatId: data.chatId, 
              message: message
            } 
          });
        },
        onMessageStatusUpdated: (data: { messageId: string; status: string; readAt?: Date }) => {
          dispatch({
            type: 'UPDATE_MESSAGE_STATUS',
            payload: {
              messageId: data.messageId,
              status: data.status,
              readAt: data.readAt
            }
          });
        },
        onMessageUpdated: (data: { messageId: string; content: string; updatedAt: Date; chatId: string }) => {
          // Trouver le message dans le state et le mettre à jour
          const currentState = stateRef.current;
          const messages = currentState.messages[data.chatId];
          if (messages) {
            const messageToUpdate = messages.find(msg => msg.id === data.messageId);
            if (messageToUpdate) {
              const updatedMessage = messageToUpdate.withUpdatedContent(data.content);
              dispatch({
                type: 'UPDATE_MESSAGE',
                payload: { chatId: data.chatId, message: updatedMessage }
              });
            }
          }
        },
        onMessageDeleted: (data: { messageId: string; chatId: string }) => {
          dispatch({
            type: 'DELETE_MESSAGE',
            payload: { chatId: data.chatId, messageId: data.messageId }
          });
        },
        onUserStatus: (data: UserStatusData) => {
          dispatch({ 
            type: 'SET_USER_STATUS', 
            payload: { userId: data.userId, isOnline: data.isOnline } 
          });
        },
        onUserTyping: (data: { userId: string; chatId: string }) => {
          dispatch({
            type: 'SET_USER_TYPING',
            payload: { chatId: data.chatId, userId: data.userId, isTyping: true }
          });
        },
        onUserStoppedTyping: (data: { userId: string; chatId: string }) => {
          dispatch({
            type: 'SET_USER_TYPING',
            payload: { chatId: data.chatId, userId: data.userId, isTyping: false }
          });
        },
        onUserStoppedTypingAll: (data: { userId: string }) => {
          dispatch({
            type: 'CLEAR_USER_TYPING_ALL',
            payload: { userId: data.userId }
          });
        },
        onError: (error: ChatError) => {
          dispatch({ type: 'SET_ERROR', payload: error.message });
        }
      };

      await chatWebSocketService.connect(token, handlers);
      
      // S'abonner aux nouveaux messages
      chatService.subscribeToNewMessages((message) => {
        const currentState = stateRef.current;
        const chatId = chatService.getChatIdFromMessage(message, currentState.chats);
        if (chatId) {
          dispatch({ type: 'ADD_MESSAGE', payload: { chatId, message } });
        }
      });

    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Échec de la connexion' });
    }
  }, [dispatch, chatService]); // Supprimé les dépendances du state pour éviter les recreations inutiles

  const disconnectWebSocket = useCallback(() => {
    chatWebSocketService.disconnect();
    chatService.unsubscribeFromNewMessages();
    dispatch({ type: 'SET_CONNECTED', payload: false });
  }, [dispatch, chatService]);

  return {
    connectWebSocket,
    disconnectWebSocket
  };
}; 
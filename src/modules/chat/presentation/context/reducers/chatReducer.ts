import { ChatState, ChatAction } from '../types/ChatTypes';

// Reducer principal
export function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_CONNECTED':
      return { ...state, isConnected: action.payload };
    
    case 'SET_CHATS':
      return { ...state, chats: action.payload };
    
    case 'ADD_CHAT':
      return { ...state, chats: [...state.chats, action.payload] };
    
    case 'SET_ACTIVE_CHAT':
      return { ...state, activeChat: action.payload };
    
    case 'SET_MESSAGES':
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.payload.chatId]: action.payload.messages
        }
      };
    
    case 'ADD_MESSAGE':
      const existingMessages = state.messages[action.payload.chatId] || [];
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.payload.chatId]: [...existingMessages, action.payload.message]
        }
      };
    
    case 'UPDATE_MESSAGE':
      const chatMessages = state.messages[action.payload.chatId] || [];
      const updatedMessages = chatMessages.map(msg =>
        msg.id === action.payload.message.id ? action.payload.message : msg
      );
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.payload.chatId]: updatedMessages
        }
      };
    
    case 'DELETE_MESSAGE':
      const messagesAfterDelete = state.messages[action.payload.chatId] || [];
      const filteredMessages = messagesAfterDelete.filter(msg => 
        msg.id !== action.payload.messageId
      );
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.payload.chatId]: filteredMessages
        }
      };
    
    case 'SET_EDITING_MESSAGE':
      return { ...state, editingMessage: action.payload };
    
    case 'SET_USER_STATUS':
      return {
        ...state,
        users: {
          ...state.users,
          [action.payload.userId]: {
            ...state.users[action.payload.userId],
            id: action.payload.userId,
            name: state.users[action.payload.userId]?.name || 'Utilisateur Inconnu',
            isOnline: action.payload.isOnline
          }
        }
      };
    
    case 'SET_USER_TYPING':
      const { chatId, userId, isTyping } = action.payload;
      const currentTypingUsers = state.typingUsers[chatId] || new Set();
      const newTypingUsers = new Set(currentTypingUsers);
      
      if (isTyping) {
        newTypingUsers.add(userId);
      } else {
        newTypingUsers.delete(userId);
      }
      
      return {
        ...state,
        typingUsers: {
          ...state.typingUsers,
          [chatId]: newTypingUsers
        }
      };
    
    case 'CLEAR_USER_TYPING_ALL':
      const userIdToClear = action.payload.userId;
      const updatedTypingUsers = { ...state.typingUsers };
      
      // Parcourir tous les chats et supprimer l'utilisateur des indicateurs de frappe
      Object.keys(updatedTypingUsers).forEach(chatId => {
        const typingUsersInChat = new Set(updatedTypingUsers[chatId]);
        typingUsersInChat.delete(userIdToClear);
        updatedTypingUsers[chatId] = typingUsersInChat;
      });
      
      return {
        ...state,
        typingUsers: updatedTypingUsers
      };
    
    case 'SYNC_USER':
      return {
        ...state,
        users: {
          ...state.users,
          [action.payload.userId]: {
            id: action.payload.userId,
            name: action.payload.name,
            avatar: action.payload.avatar,
            isOnline: action.payload.isOnline || false
          }
        }
      };
    
    case 'SET_CURRENT_USER_ID':
      return { ...state, currentUserId: action.payload };
    
    case 'UPDATE_MESSAGE_STATUS':
      const updatedMessagesForStatus = Object.keys(state.messages).reduce((acc, chatId) => {
        acc[chatId] = state.messages[chatId].map(msg => {
          if (msg.id === action.payload.messageId) {
            // Mettre à jour le statut du message avec les méthodes du domaine
            switch (action.payload.status) {
              case 'read':
                return msg.markAsRead();
              case 'delivered':
                return msg.markAsDelivered();
              default:
                return msg;
            }
          }
          return msg;
        });
        return acc;
      }, {} as Record<string, any[]>);
      
      return {
        ...state,
        messages: updatedMessagesForStatus
      };
    
    case 'SET_AUTH':
      return {
        ...state,
        auth: action.payload,
        currentUserId: action.payload.currentUser?.id || null
      };
    
    case 'SET_USER_SEARCH':
      return {
        ...state,
        userSearch: action.payload
      };
    
    default:
      return state;
  }
} 
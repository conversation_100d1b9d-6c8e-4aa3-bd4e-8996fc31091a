import { Message } from '../../../domain/entities/Message';
import { Chat } from '../../../domain/entities/Chat';
import { MessageType } from '../../../domain/valueobjects/MessageType';
import { SendMessageUseCase } from '../../../application/usecases/SendMessageUseCase';
import { GetChatHistoryUseCase } from '../../../application/usecases/GetChatHistoryUseCase';
import { MarkMessageAsReadUseCase } from '../../../application/usecases/MarkMessageAsReadUseCase';
import { CreateChatUseCase } from '../../../application/usecases/CreateChatUseCase';
import { HttpChatRepository } from '../../../infrastructure/repositories/HttpChatRepository';

export class ChatService {
  private chatRepository: HttpChatRepository;
  private sendMessageUseCase: SendMessageUseCase;
  private getChatHistoryUseCase: GetChatHistoryUseCase;
  private markMessageAsReadUseCase: MarkMessageAsReadUseCase;
  private createChatUseCase: CreateChatUseCase;

  constructor() {
    this.chatRepository = new HttpChatRepository();
    this.sendMessageUseCase = new SendMessageUseCase(this.chatRepository);
    this.getChatHistoryUseCase = new GetChatHistoryUseCase(this.chatRepository);
    this.markMessageAsReadUseCase = new MarkMessageAsReadUseCase(this.chatRepository);
    this.createChatUseCase = new CreateChatUseCase(this.chatRepository);
  }

  // Envoyer un message
  async sendMessage(params: {
    content: string;
    senderId: string;
    receiverId: string;
    type: MessageType;
  }): Promise<Message> {
    return this.sendMessageUseCase.execute(params);
  }

  // Charger les chats d'un utilisateur
  async loadUserChats(userId: string): Promise<Chat[]> {
    return this.chatRepository.getUserChats(userId);
  }

  // Charger l'historique d'un chat
  async loadChatHistory(params: {
    userId: string;
    otherUserId: string;
    limit: number;
  }): Promise<Message[]> {
    return this.getChatHistoryUseCase.execute(params);
  }

  // Marquer un message comme lu
  async markMessageAsRead(messageId: string): Promise<void> {
    return this.markMessageAsReadUseCase.execute(messageId);
  }

  // Mettre à jour un message
  async updateMessage(messageId: string, content: string): Promise<Message> {
    return this.chatRepository.updateMessage(messageId, content);
  }

  // Supprimer un message
  async deleteMessage(messageId: string): Promise<void> {
    return this.chatRepository.deleteMessage(messageId);
  }

  // Créer un nouveau chat
  async createChat(participantIds: string[]): Promise<Chat> {
    return this.createChatUseCase.execute(participantIds);
  }

  // Converter DTO vers Message
  convertDtoToMessage(dto: any, chatId: string): Message {
    return this.chatRepository.convertDtoToMessage(dto, chatId);
  }

  // S'abonner aux nouveaux messages
  subscribeToNewMessages(callback: (message: Message) => void): void {
    this.chatRepository.subscribeToNewMessages(callback);
  }

  // Se désabonner des nouveaux messages
  unsubscribeFromNewMessages(): void {
    this.chatRepository.unsubscribeFromNewMessages();
  }

  // Fonctions utilitaires
  getChatIdByParticipants(chats: Chat[], userId1: string, userId2: string): string | null {
    const chat = chats.find(c => 
      c.participantIds.includes(userId1) && c.participantIds.includes(userId2)
    );
    return chat?.id || null;
  }

  getChatIdFromMessage(message: Message, chats: Chat[]): string | null {
    return message.chatId || this.getChatIdByParticipants(chats, message.senderId, message.receiverId);
  }
} 
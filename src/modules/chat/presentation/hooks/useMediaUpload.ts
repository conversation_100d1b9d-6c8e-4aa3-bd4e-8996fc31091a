import { useState, useRef } from 'react';
import { FileValidationRules } from '../../domain/valueobjects/FileValidation';
import { PendingMedia } from '../types/MessageTypes';

export const useMediaUpload = () => {
  const [pendingMediaList, setPendingMediaList] = useState<PendingMedia[]>([]);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  const imageValidationRules = FileValidationRules.imageRules();
  const videoValidationRules = FileValidationRules.videoRules();

  const addMediaToList = (media: PendingMedia) => {
    setPendingMediaList(prev => [...prev, media]);
  };

  const removeMedia = (index: number) => {
    setPendingMediaList(prev => prev.filter((_, i) => i !== index));
  };

  const clearMediaList = () => {
    setPendingMediaList([]);
  };

  const handleImageSelect = () => {
    if (imageInputRef.current) {
      imageInputRef.current.click();
    }
  };

  const handleVideoSelect = () => {
    if (videoInputRef.current) {
      videoInputRef.current.click();
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    files.forEach(file => {
      const validationResult = imageValidationRules.validate(file);
      if (!validationResult.isValid) {
        alert(validationResult.errorMessage);
        return;
      }

      const reader = new FileReader();
      reader.onload = (event: ProgressEvent<FileReader>) => {
        if (event.target?.result) {
          addMediaToList({
            file,
            type: 'image',
            preview: event.target.result as string
          });
        }
      };
      reader.readAsDataURL(file);
    });

    e.target.value = '';
  };

  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    files.forEach(file => {
      const validationResult = videoValidationRules.validate(file);
      if (!validationResult.isValid) {
        alert(validationResult.errorMessage);
        return;
      }

      const reader = new FileReader();
      reader.onload = (event: ProgressEvent<FileReader>) => {
        if (event.target?.result) {
          addMediaToList({
            file,
            type: 'video',
            preview: event.target.result as string
          });
        }
      };
      reader.readAsDataURL(file);
    });

    e.target.value = '';
  };

  return {
    pendingMediaList,
    imageInputRef,
    videoInputRef,
    imageValidationRules,
    videoValidationRules,
    handleImageSelect,
    handleVideoSelect,
    handleImageChange,
    handleVideoChange,
    removeMedia,
    clearMediaList
  };
}; 
import { useState, useRef, useEffect, useCallback } from 'react';
import { Message } from '../../domain/entities/Message';
import { MessageTypeEnum } from '../../domain/valueobjects/MessageType';
import { MessageContentAdapter } from '../../infrastructure/adapters/MessageContentAdapter';

interface UseMessageEditProps {
  editingMessage: Message;
  onSaveEdit: (newContent: string) => Promise<void>;
  onMessageChange?: (message: string) => void;
}

export const useMessageEdit = ({ editingMessage, onSaveEdit, onMessageChange }: UseMessageEditProps) => {
  const [message, setMessage] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const getEditingMediaData = useCallback(() => {
    if (editingMessage.type.getValue() === MessageTypeEnum.TEXT) {
      return null;
    }

    try {
      const parsedContent = MessageContentAdapter.parseMessageContent(editingMessage);
      return parsedContent.data;
    } catch (error) {
      try {
        return JSON.parse(editingMessage.content.getValue());
      } catch (directError) {
        return null;
      }
    }
  }, [editingMessage]);

  // Initialiser le contenu lors du montage
  useEffect(() => {
    const initializeMessage = () => {
      if (editingMessage.type.getValue() === MessageTypeEnum.IMAGE || 
          editingMessage.type.getValue() === MessageTypeEnum.VIDEO) {
        try {
          const mediaData = getEditingMediaData();
          setMessage(mediaData?.caption || '');
        } catch (error) {
          console.warn('Impossible de parser le contenu média');
          setMessage('');
        }
      } else {
        setMessage(editingMessage.content.getValue());
      }
    };

    initializeMessage();
    
    // Focus sur l'input
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);
      }
    }, 0);
  }, [editingMessage, getEditingMediaData]);

  const getMessageTypeLabel = () => {
    switch (editingMessage.type.getValue()) {
      case MessageTypeEnum.IMAGE:
        return 'image';
      case MessageTypeEnum.VIDEO:
        return 'vidéo';
      case MessageTypeEnum.TEXT:
      default:
        return 'message';
    }
  };

  const getPlaceholder = () => {
    const messageType = getMessageTypeLabel();
    if (messageType === 'image' || messageType === 'vidéo') {
      return `Modifiez la légende de votre ${messageType}...`;
    }
    return "Modifiez votre message...";
  };

  const handleSave = async () => {
    const trimmedMessage = message.trim();
    if (!trimmedMessage || isSaving) return;

    try {
      setIsSaving(true);
      await onSaveEdit(trimmedMessage);
    } catch (error) {
      console.error('Échec de la modification du message:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    const input = inputRef.current;
    if (!input) return;

    const start = input.selectionStart || 0;
    const end = input.selectionEnd || 0;
    const newMessage = message.slice(0, start) + emoji + message.slice(end);
    
    setMessage(newMessage);
    if (onMessageChange) {
      onMessageChange(newMessage);
    }
      
    setTimeout(() => {
      input.focus();
      const newCursorPosition = start + emoji.length;
      input.setSelectionRange(newCursorPosition, newCursorPosition);
    }, 0);
  };

  return {
    message,
    setMessage,
    isSaving,
    inputRef,
    mediaData: getEditingMediaData(),
    messageTypeLabel: getMessageTypeLabel(),
    placeholder: getPlaceholder(),
    handleSave,
    handleEmojiSelect
  };
}; 
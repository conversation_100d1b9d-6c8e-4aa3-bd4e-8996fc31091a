import { useState, useRef, useCallback } from 'react';

interface UseTypingIndicatorProps {
  chatId?: string;
  receiverId?: string;
  onStartTyping?: (chatId: string, receiverId: string) => void;
  onStopTyping?: (chatId: string, receiverId: string) => void;
}

export const useTypingIndicator = ({
  chatId,
  receiverId,
  onStartTyping,
  onStopTyping
}: UseTypingIndicatorProps) => {
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleStartTyping = useCallback(() => {
    if (!chatId || !receiverId || !onStartTyping || isTyping) return;
    
    setIsTyping(true);
    onStartTyping(chatId, receiverId);
  }, [chatId, receiverId, onStartTyping, isTyping]);

  const handleStopTyping = useCallback(() => {
    if (!chatId || !receiverId || !onStopTyping || !isTyping) return;
    
    setIsTyping(false);
    onStopTyping(chatId, receiverId);
  }, [chatId, receiverId, onStopTyping, isTyping]);

  const resetTypingTimeout = useCallback(() => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    typingTimeoutRef.current = setTimeout(() => {
      handleStopTyping();
    }, 2000);
  }, [handleStopTyping]);

  const handleMessageChange = useCallback((message: string) => {
    if (message.trim() && !isTyping) {
      handleStartTyping();
    }

    if (message.trim()) {
      resetTypingTimeout();
    } else {
      handleStopTyping();
    }
  }, [isTyping, handleStartTyping, handleStopTyping, resetTypingTimeout]);

  const cleanup = useCallback(() => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    handleStopTyping();
  }, [handleStopTyping]);

  return {
    isTyping,
    handleMessageChange,
    handleStopTyping,
    cleanup
  };
}; 
import { KeyboardEvent } from 'react';

interface UseKeyboardShortcutsProps {
  onSave?: () => void;
  onCancel?: () => void;
  onSend?: () => void;
  disabled?: boolean;
}

export const useKeyboardShortcuts = ({
  onSave,
  onCancel,
  onSend,
  disabled = false
}: UseKeyboardShortcutsProps) => {
  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (onSave) {
        onSave();
      } else if (onSend) {
        onSend();
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      if (onCancel) {
        onCancel();
      }
    }
  };

  return { handleKeyPress };
}; 
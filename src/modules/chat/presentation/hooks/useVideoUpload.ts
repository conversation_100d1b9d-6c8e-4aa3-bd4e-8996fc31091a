import { useState } from 'react';
import { UploadVideoUseCase } from '../../application/usecases/UploadVideoUseCase';
import { HttpChatRepository } from '../../infrastructure/repositories/HttpChatRepository';
import { VideoUploadResponseDto } from '../../infrastructure/api/types/ChatTypes';

const httpChatRepository = new HttpChatRepository();
const uploadVideoUseCase = new UploadVideoUseCase(httpChatRepository);

export const useVideoUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadVideo = async (
    file: File, 
    receiverId: string, 
    caption?: string
  ): Promise<VideoUploadResponseDto> => {
    try {
      setIsUploading(true);
      setError(null);

      const result = await uploadVideoUseCase.execute({
        file,
        receiverId,
        caption
      });

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de l\'upload de la vidéo';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    uploadVideo,
    isUploading,
    error,
    clearError
  };
}; 
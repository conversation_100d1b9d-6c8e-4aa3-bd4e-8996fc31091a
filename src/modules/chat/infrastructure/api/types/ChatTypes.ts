export interface MessageResponseDto {
  id: string;
  content: string;
  senderId: string;
  receiverId: string;
  type: MessageTypeEnum;
  status: MessageStatusEnum;
  sentAt: Date;
  readAt?: Date;
}

export interface ChatResponseDto {
  id: string;
  participantIds: string[];
  lastMessage?: MessageResponseDto;
  createdAt: Date;
  updatedAt: Date;
}

export interface SendMessageDto {
  content: string;
  receiverId: string;
  type?: MessageTypeEnum;
}

export interface CreateChatDto {
  participantId: string;
}

export interface GetChatHistoryDto {
  limit?: number;
  before?: Date;
}

// Types pour l'upload d'images
export interface UploadImageDto {
  receiverId: string;
  caption?: string;
}

export interface ImageUploadResponseDto {
  success: boolean;
  message: string;
  imageUrl: string;
  messageId: string;
}

export interface ImageMetadataDto {
  originalName: string;
  filename: string;
  mimetype: string;
  size: number;
  url: string;
}

// Structure du contenu d'un message image
export interface ImageMessageContent {
  type: 'image';
  url: string;
  originalName: string;
  filename: string;
  mimetype: string;
  size: number;
  caption: string;
}

// Types pour l'upload de vidéos
export interface UploadVideoDto {
  receiverId: string;
  caption?: string;
}

export interface VideoUploadResponseDto {
  success: boolean;
  message: string;
  videoUrl: string;
  messageId: string;
}

export interface VideoMetadataDto {
  originalName: string;
  filename: string;
  mimetype: string;
  size: number;
  url: string;
}

// Structure du contenu d'un message vidéo
export interface VideoMessageContent {
  type: 'video';
  url: string;
  originalName: string;
  filename: string;
  mimetype: string;
  size: number;
  caption: string;
}

export enum MessageTypeEnum {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  FILE = 'file',
  SYSTEM = 'system'
}

export enum MessageStatusEnum {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read'
}

// WebSocket event types
export interface JoinChatData {
  chatId: string;
}

export interface JoinedChatResponse {
  chatId: string;
  messages: MessageResponseDto[];
}

export interface SendMessageData {
  content: string;
  receiverId: string;
  chatId?: string;
  type?: MessageTypeEnum;
}

export interface MessageReceivedData {
  message: MessageResponseDto;
  chatId: string;
}

export interface MarkMessageReadData {
  messageId: string;
}

export interface UserStatusData {
  userId: string;
  isOnline: boolean;
}

// Typing indicator types
export interface TypingStartData {
  chatId: string;
  receiverId: string;
}

export interface TypingStopData {
  chatId: string;
  receiverId: string;
}

export interface UserTypingData {
  userId: string;
  chatId: string;
}

export interface UserStoppedTypingData {
  userId: string;
  chatId: string;
}

export interface UserStoppedTypingAllData {
  userId: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface ChatError {
  message: string;
  code?: string;
  details?: any;
} 
import { ApiService } from '../../../shared/infrastructure/api/ApiService';
import {
  MessageResponseDto,
  ChatResponseDto,
  SendMessageDto,
  GetChatHistoryDto,
  CreateChatDto,
  ImageUploadResponseDto,
  VideoUploadResponseDto
} from './types/ChatTypes';
import { ErrorMappingService } from '../../../shared/infrastructure/errors/ErrorMappingService';
import { AxiosApi } from '../../../shared/infrastructure/api/AxiosApi';

export class ChatApiClient {
  private baseUrl: string;
  private api: ApiService;

  constructor(api: ApiService) {
    this.baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    this.api = api;
  }

  private async handleResponse<T>(response: T): Promise<T> {
    return response;
  }

  public async uploadImage(
    file: File, 
    receiverId: string, 
    caption?: string
  ): Promise<ImageUploadResponseDto> {
    try {
      const formData = new FormData();
      formData.append('image', file);
      formData.append('receiverId', receiverId);
      if (caption) {
        formData.append('caption', caption);
      }
      console.log('formData', formData);
      const response = await this.api.post<ImageUploadResponseDto>(
        `/api/chat/images/upload`,
        formData
      );
      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error uploading image:', error);
      throw this.handleError(error);
    }
  }

  public async uploadVideo(
    file: File, 
    receiverId: string, 
    caption?: string
  ): Promise<VideoUploadResponseDto> {
    try {
      const formData = new FormData();
      formData.append('video', file);
      formData.append('receiverId', receiverId);
      if (caption) {
        formData.append('caption', caption);
      }
      const response = await this.api.post<VideoUploadResponseDto>(
        `/api/chat/videos/upload`,
        formData
      );
      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error uploading video:', error);
      throw this.handleError(error);
    }
  }

  public async sendMessage(messageData: SendMessageDto): Promise<MessageResponseDto> {
    try {
      const response = await this.api.post<MessageResponseDto>(
        `/api/chat/messages`,
        messageData
      );
      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error sending message:', error);
      throw this.handleError(error);
    }
  }

  public async createChat(chatData: CreateChatDto): Promise<ChatResponseDto> {
    try {
      const response = await this.api.post<ChatResponseDto>(
        `/api/chat/chats`,
        chatData
      );
      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error creating chat:', error);
      throw this.handleError(error);
    }
  }

  public async getUserChats(): Promise<ChatResponseDto[]> {
    try {
      const response = await this.api.get<ChatResponseDto[]>(
        `/api/chat/chats`
      );
      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching user chats:', error);
      throw this.handleError(error);
    }
  }

  public async getChatHistory(
    chatId: string, 
    options: GetChatHistoryDto = {}
  ): Promise<MessageResponseDto[]> {
    try {
      const params = this.buildQueryParams(options);
      const url = this.buildApiUrl(`/api/chat/chats/${chatId}/messages`, params);
      const response = await this.api.get<MessageResponseDto[]>(
        url
      );
      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching chat history:', error);
      throw this.handleError(error);
    }
  }

  public async markMessageAsRead(messageId: string): Promise<{ success: boolean }> {
    try {
      const response = await this.api.patch<{ success: boolean }>(
        `/api/chat/messages/${messageId}/read`,
        {}
      );
      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error marking message as read:', error);
      throw this.handleError(error);
    }
  }

  public async updateMessage(messageId: string, content: string): Promise<MessageResponseDto> {
    try {
      const response = await this.api.patch<MessageResponseDto>(
        `/api/chat/messages/${messageId}`,
        { content }
      );
      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error updating message:', error);
      throw this.handleError(error);
    }
  }

  public async deleteMessage(messageId: string): Promise<void> {
    try {
      await this.api.delete(`/api/chat/messages/${messageId}`);
    } catch (error) {
      console.error('Error deleting message:', error);
      throw this.handleError(error);
    }
  }

  private buildQueryParams(options: GetChatHistoryDto): URLSearchParams {
    const params = new URLSearchParams();
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.before) params.append('before', options.before.toISOString());
    return params;
  }

  private buildApiUrl(endpoint: string, params?: URLSearchParams): string {
    if (!params) return endpoint;
    return `${endpoint}?${params.toString()}`;
  }

  private handleError(error: any): Error {
    if (error.response) {
      const mapped = ErrorMappingService.mapHttpErrorToApplicationError(
        error.response.status,
        error.response.data?.message
      );
      return new Error(mapped.message);
    }
    return new Error('Erreur réseau ou inconnue');
  }
}

export const chatApiClient = new ChatApiClient(new AxiosApi());
export default chatApiClient; 
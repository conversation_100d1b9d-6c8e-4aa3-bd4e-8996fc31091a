import { multiWebSocketService, WebSocketEventHandlers } from '../../../shared/infrastructure/services/MultiWebSocketService';
import {
  JoinChatData,
  JoinedChatResponse,
  SendMessageData,
  MessageReceivedData,
  MarkMessageReadData,
  UserStatusData,
  ChatError,
  TypingStartData,
  TypingStopData,
  UserTypingData,
  UserStoppedTypingData,
  UserStoppedTypingAllData
} from '../api/types/ChatTypes';

export interface ChatWebSocketEventHandlers {
  onConnect?: () => void;
  onDisconnect?: () => void;
  onJoinedChat?: (data: JoinedChatResponse) => void;
  onMessageReceived?: (data: MessageReceivedData) => void;
  onMessageSent?: (data: MessageReceivedData) => void;
  onMessageRead?: (data: { messageId: string; readAt: Date }) => void;
  onMessageStatusUpdated?: (data: { messageId: string; status: string; readAt?: Date }) => void;
  onMessageUpdated?: (data: { messageId: string; content: string; updatedAt: Date; chatId: string }) => void;
  onMessageDeleted?: (data: { messageId: string; chatId: string }) => void;
  onUserStatus?: (data: UserStatusData) => void;
  onUserTyping?: (data: UserTypingData) => void;
  onUserStoppedTyping?: (data: UserStoppedTypingData) => void;
  onUserStoppedTypingAll?: (data: UserStoppedTypingAllData) => void;
  onError?: (error: ChatError) => void;
}

class ChatWebSocketService {
  private readonly namespace = '/chat';

  public async connect(token: string, handlers: ChatWebSocketEventHandlers = {}): Promise<void> {
    const config = {
      url: process.env.REACT_APP_WS_URL || 'http://localhost:3001',
      namespace: '/chat',
      auth: {
        token: token
      },
      transports: ['websocket']
    };

    // Map chat-specific handlers to generic handlers
    const genericHandlers: WebSocketEventHandlers = {
      onConnect: () => {
        handlers.onConnect?.();
      },
      onDisconnect: () => {
        handlers.onDisconnect?.();
      },
      onError: (error) => {
        handlers.onError?.(error);
      },
      // Chat-specific events
      joined_chat: (data: JoinedChatResponse) => {
        handlers.onJoinedChat?.(data);
      },
      new_message: (data: MessageReceivedData) => {
        handlers.onMessageReceived?.(data);
      },
      message_sent: (data: MessageReceivedData) => {
        handlers.onMessageSent?.(data);
      },
      message_read: (data: { messageId: string; readAt: Date }) => {
        handlers.onMessageRead?.(data);
      },
      message_status_updated: (data: { messageId: string; status: string; readAt?: Date }) => {
        handlers.onMessageStatusUpdated?.(data);
      },
      message_updated: (data: { messageId: string; content: string; updatedAt: Date; chatId: string }) => {
        handlers.onMessageUpdated?.(data);
      },
      message_deleted: (data: { messageId: string; chatId: string }) => {
        handlers.onMessageDeleted?.(data);
      },
      user_status: (data: UserStatusData) => {
        handlers.onUserStatus?.(data);
      },
      user_typing: (data: UserTypingData) => {
        handlers.onUserTyping?.(data);
      },
      user_stopped_typing: (data: UserStoppedTypingData) => {
        handlers.onUserStoppedTyping?.(data);
      },
      user_stopped_typing_all: (data: UserStoppedTypingAllData) => {
        handlers.onUserStoppedTypingAll?.(data);
      }
    };

    await multiWebSocketService.connect(this.namespace, config, genericHandlers);
  }

  public disconnect(): void {
    multiWebSocketService.disconnect(this.namespace);
  }

  public isConnected(): boolean {
    return multiWebSocketService.isConnected(this.namespace);
  }

  public joinChat(chatId: string): void {
    if (!this.isConnected()) {
      return;
    }

    const data: JoinChatData = { chatId };
    multiWebSocketService.emit(this.namespace, 'join_chat', data);
  }

  public sendMessage(data: SendMessageData): void {
    if (!this.isConnected()) {
      return;
    }

    multiWebSocketService.emit(this.namespace, 'send_message', data);
  }

  public markMessageAsRead(data: MarkMessageReadData): void {
    if (!this.isConnected()) {
      return;
    }

    multiWebSocketService.emit(this.namespace, 'mark_message_read', data);
  }

  public startTyping(data: TypingStartData): void {
    if (!this.isConnected()) {
      return;
    }

    multiWebSocketService.emit(this.namespace, 'typing_start', data);
  }

  public stopTyping(data: TypingStopData): void {
    if (!this.isConnected()) {
      return;
    }

    multiWebSocketService.emit(this.namespace, 'typing_stop', data);
  }
}

// Export singleton instance
export const chatWebSocketService = new ChatWebSocketService();
export default chatWebSocketService; 
import { Message } from '../../domain/entities/Message';
import { MessageTypeEnum } from '../../domain/valueobjects/MessageType';
import { VideoMessageContent } from '../../presentation/components/VideoMessage';
import { ImageMessageContent } from '../../infrastructure/api/types/ChatTypes';

export interface ParsedMessageContent {
  type: 'text' | 'image' | 'video' | 'audio' | 'file' | 'system';
  data: any;
}

export class MessageContentAdapter {
  static parseMessageContent(message: Message): ParsedMessageContent {
    const messageType = message.type.getValue();
    const rawContent = message.content.getValue();

    switch (messageType) {
      case MessageTypeEnum.TEXT:
      case MessageTypeEnum.SYSTEM:
        return {
          type: 'text',
          data: rawContent
        };

      case MessageTypeEnum.IMAGE:
        try {
          const imageData: ImageMessageContent = JSON.parse(rawContent);
          return {
            type: 'image',
            data: imageData
          };
        } catch (error) {
          console.error('Error parsing image content:', error);
          throw new Error('Invalid image message content');
        }

      case MessageTypeEnum.VIDEO:
        try {
          const videoData: VideoMessageContent = JSON.parse(rawContent);
          return {
            type: 'video',
            data: videoData
          };
        } catch (error) {
          console.error('Error parsing video content:', error);
          throw new Error('Invalid video message content');
        }

      case MessageTypeEnum.FILE:
        try {
          const fileData = JSON.parse(rawContent);
          return {
            type: 'file',
            data: fileData
          };
        } catch (error) {
          console.error('Error parsing file content:', error);
          throw new Error('Invalid file message content');
        }

      default:
        // Gestion des types étendus (comme AUDIO)
        try {
          const parsedData = JSON.parse(rawContent);
          return {
            type: String(messageType).toLowerCase() as any,
            data: parsedData
          };
        } catch (error) {
          console.warn('Unknown message type or invalid content:', messageType);
          return {
            type: 'text',
            data: rawContent
          };
        }
    }
  }

  static serializeMessageContent(type: MessageTypeEnum, data: any): string {
    switch (type) {
      case MessageTypeEnum.TEXT:
      case MessageTypeEnum.SYSTEM:
        return typeof data === 'string' ? data : JSON.stringify(data);
      
      default:
        return JSON.stringify(data);
    }
  }
} 
import { IChatRepository } from '../../domain/repositories/IChatRepository';
import { Message } from '../../domain/entities/Message';
import { Chat } from '../../domain/entities/Chat';
import { MessageContent } from '../../domain/valueobjects/MessageContent';
import { MessageType, MessageTypeEnum } from '../../domain/valueobjects/MessageType';
import { MessageStatus, MessageStatusEnum } from '../../domain/valueobjects/MessageStatus';
import { ParticipantFactory, UserProfileData } from '../../domain/factories/ParticipantFactory';
import { chatApiClient } from '../api/ChatApiClient';
import { chatWebSocketService } from '../services/ChatWebSocketService';
import { userApiRepository } from '../../../users/infrastructure/api/UserApiRepository';
import {
  MessageResponseDto,
  ChatResponseDto, MessageTypeEnum as ApiMessageTypeEnum,
  MessageStatusEnum as ApiMessageStatusEnum,
  ImageUploadResponseDto,
  VideoUploadResponseDto
} from '../api/types/ChatTypes';

export class HttpChatRepository implements IChatRepository {
  private messageSubscriptionCallback: ((message: Message) => void) | null = null;

  async sendMessage(message: Message): Promise<Message> {
    const sendMessageData = {
      content: message.content.getValue(),
      receiverId: message.receiverId,
      type: this.mapDomainTypeToApi(message.type.getValue())
    };

    // Send via WebSocket instead of HTTP API
    chatWebSocketService.sendMessage(sendMessageData);
    
    // Return the original message since WebSocket doesn't return a response
    // The message will be handled via WebSocket events
    return message;
  }

  async getChatHistory(
    userId: string, 
    otherUserId: string, 
    limit?: number, 
    before?: Date
  ): Promise<Message[]> {
    // First, get or create the chat
    const chat = await this.getChat(userId, otherUserId);
    if (!chat) {
      return [];
    }

    const messages = await chatApiClient.getChatHistory(chat.id, { limit, before });
    return messages.map(dto => this.mapDtoToMessage(dto, chat.id));
  }

  async markMessageAsRead(messageId: string): Promise<void> {
    await chatApiClient.markMessageAsRead(messageId);
    // WebSocket call now uses object parameter
    chatWebSocketService.markMessageAsRead({ messageId });
  }

  subscribeToNewMessages(callback: (message: Message) => void): void {
    this.messageSubscriptionCallback = callback;
  }

  unsubscribeFromNewMessages(): void {
    this.messageSubscriptionCallback = null;
  }

  async createChat(participants: string[]): Promise<Chat> {
    try {      
      // Le premier participant est l'utilisateur connecté, le second est le destinataire
      const participantId = participants[1];
      const createChatDto = { participantId };
      
      const chatDto = await chatApiClient.createChat(createChatDto);
      return await this.mapDtoToChat(chatDto);
    } catch (error) {
      console.error('Error creating chat:', error);
      throw new Error('Failed to create chat');
    }
  }

  async getChat(userId: string, otherUserId: string): Promise<Chat | null> {
    const chats = await this.getUserChats(userId);
    return chats.find(chat => 
      chat.participantIds.includes(otherUserId)
    ) || null;
  }

  async getUserChats(userId: string): Promise<Chat[]> {
    const chatDtos = await chatApiClient.getUserChats();
    const chatsWithParticipants = await Promise.all(
      chatDtos.map(dto => this.mapDtoToChat(dto))
    );
    return chatsWithParticipants;
  }

  async uploadImage(file: File, receiverId: string, caption?: string): Promise<ImageUploadResponseDto> {
    return await chatApiClient.uploadImage(file, receiverId, caption);
  }

  async uploadVideo(file: File, receiverId: string, caption?: string): Promise<VideoUploadResponseDto> {
    return await chatApiClient.uploadVideo(file, receiverId, caption);
  }

  async updateMessage(messageId: string, content: string): Promise<Message> {
    const dto = await chatApiClient.updateMessage(messageId, content);
    return this.mapDtoToMessage(dto);
  }

  async deleteMessage(messageId: string): Promise<void> {
    await chatApiClient.deleteMessage(messageId);
  }

  // Public method to convert DTO to Message (for WebSocket usage)
  public convertDtoToMessage(dto: MessageResponseDto, chatId?: string): Message {
    return this.mapDtoToMessage(dto, chatId);
  }

  private mapDtoToMessage(dto: MessageResponseDto, chatId?: string): Message {
    return new Message(
      dto.id,
      new MessageContent(dto.content),
      dto.senderId,
      dto.receiverId,
      chatId || '', // Use provided chatId or empty string
      new MessageType(this.mapApiTypeToDomain(dto.type)),
      new MessageStatus(this.mapApiStatusToDomain(dto.status)),
      new Date(dto.sentAt),
      dto.readAt ? new Date(dto.readAt) : undefined
    );
  }

  private async mapDtoToChat(dto: ChatResponseDto): Promise<Chat> {
    const lastMessage = dto.lastMessage ? this.mapDtoToMessage(dto.lastMessage, dto.id) : undefined;
    
    const userProfilesMap = new Map<string, UserProfileData>();
    
    const userPromises = dto.participantIds.map(participantId =>
      userApiRepository.getUserById(participantId)
        .then(user => {
          userProfilesMap.set(participantId, {
            id: user.id,
            username: user.username,
            email: user.email,
            avatar: user.avatar,
            isOnline: user.isOnline,
            blockStatus: user.blockStatus
          });
        })
        .catch(error => {
          console.warn(`Could not fetch user profile for ${participantId}`);
        })
    );

    await Promise.all(userPromises);
    
    const participantProfiles = ParticipantFactory.createParticipantsFromIds(
      dto.participantIds, 
      userProfilesMap
    );
    
    return new Chat(
      dto.id,
      participantProfiles,
      new Date(dto.createdAt),
      new Date(dto.updatedAt),
      lastMessage
    );
  }

  // Pure technical mapping - no business logic
  private mapDomainTypeToApi(type: MessageTypeEnum): ApiMessageTypeEnum {
    const typeMap: Record<MessageTypeEnum, ApiMessageTypeEnum> = {
      [MessageTypeEnum.TEXT]: ApiMessageTypeEnum.TEXT,
      [MessageTypeEnum.IMAGE]: ApiMessageTypeEnum.IMAGE,
      [MessageTypeEnum.VIDEO]: ApiMessageTypeEnum.VIDEO,
      [MessageTypeEnum.FILE]: ApiMessageTypeEnum.FILE,
      [MessageTypeEnum.SYSTEM]: ApiMessageTypeEnum.SYSTEM
    };
    
    return typeMap[type] || ApiMessageTypeEnum.TEXT;
  }

  private mapApiTypeToDomain(type: ApiMessageTypeEnum): MessageTypeEnum {
    const typeMap: Record<ApiMessageTypeEnum, MessageTypeEnum> = {
      [ApiMessageTypeEnum.TEXT]: MessageTypeEnum.TEXT,
      [ApiMessageTypeEnum.IMAGE]: MessageTypeEnum.IMAGE,
      [ApiMessageTypeEnum.VIDEO]: MessageTypeEnum.VIDEO,
      [ApiMessageTypeEnum.FILE]: MessageTypeEnum.FILE,
      [ApiMessageTypeEnum.SYSTEM]: MessageTypeEnum.SYSTEM
    };
    
    return typeMap[type] || MessageTypeEnum.TEXT;
  }

  private mapApiStatusToDomain(status: ApiMessageStatusEnum): MessageStatusEnum {
    const statusMap: Record<ApiMessageStatusEnum, MessageStatusEnum> = {
      [ApiMessageStatusEnum.SENT]: MessageStatusEnum.SENT,
      [ApiMessageStatusEnum.DELIVERED]: MessageStatusEnum.DELIVERED,
      [ApiMessageStatusEnum.READ]: MessageStatusEnum.READ
    };
    
    return statusMap[status] || MessageStatusEnum.SENT;
  }
} 
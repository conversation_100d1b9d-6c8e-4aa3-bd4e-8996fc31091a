export enum MessageStatusEnum {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read'
}

export class MessageStatus {
  private readonly value: MessageStatusEnum;

  constructor(value: MessageStatusEnum) {
    this.value = value;
  }

  static sent(): MessageStatus {
    return new MessageStatus(MessageStatusEnum.SENT);
  }

  static delivered(): MessageStatus {
    return new MessageStatus(MessageStatusEnum.DELIVERED);
  }

  static read(): MessageStatus {
    return new MessageStatus(MessageStatusEnum.READ);
  }

  getValue(): MessageStatusEnum {
    return this.value;
  }

  getDisplayText(): string {
    switch (this.value) {
      case MessageStatusEnum.SENT: return 'Sent';
      case MessageStatusEnum.DELIVERED: return 'Delivered';
      case MessageStatusEnum.READ: return 'Read';
    }
  }

  getIcon(): string {
    switch (this.value) {
      case MessageStatusEnum.SENT: return '✓';
      case MessageStatusEnum.DELIVERED: return '✓✓';
      case MessageStatusEnum.READ: return '✓✓';
    }
  }

  shouldShowTimestamp(): boolean {
    return this.value === MessageStatusEnum.READ;
  }

  isSent(): boolean {
    return this.value === MessageStatusEnum.SENT;
  }

  isDelivered(): boolean {
    return this.value === MessageStatusEnum.DELIVERED;
  }

  isRead(): boolean {
    return this.value === MessageStatusEnum.READ;
  }

  equals(other: MessageStatus): boolean {
    return this.value === other.value;
  }

  toString(): string {
    return this.value;
  }
} 
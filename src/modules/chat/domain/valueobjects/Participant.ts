import { UserBlockStatus } from "@/modules/users/domain/entities/User";

export class Participant {
  constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly email: string,
    public readonly avatar?: string,
    public readonly isOnline?: boolean,
    public readonly blockStatus?: UserBlockStatus
  ) {
    if (!id.trim()) {
      throw new Error('Participant ID cannot be empty');
    }
    if (!name.trim()) {
      throw new Error('Participant name cannot be empty');
    }
    if (!email.trim()) {
      throw new Error('Participant email cannot be empty');
    }
  }

  public getId(): string {
    return this.id;
  }

  public getName(): string {
    return this.name;
  }

  public getEmail(): string {
    return this.email;
  }

  public getAvatar(): string | undefined {
    return this.avatar;
  }

  public isUserOnline(): boolean {
    return this.isOnline || false;
  }

  public getBlockStatus(): UserBlockStatus | undefined {
    return this.blockStatus;
  }

  public getDisplayInfo(): string {
    return `${this.name} (${this.email})`;
  }

  public equals(other: Participant): boolean {
    return this.id === other.id;
  }

  public static create(data: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    isOnline?: boolean;
    blockStatus?: UserBlockStatus;
  }): Participant {
    return new Participant(
      data.id,
      data.name,
      data.email,
      data.avatar,
      data.isOnline,
      data.blockStatus
    );
  }
} 
import { InvalidMessageException } from '../exceptions/InvalidMessageException';

export class MessageContent {
  private readonly value: string;

  constructor(content: string) {
    this.validate(content);
    this.value = content.trim();
  }

  private validate(content: string): void {
    if (!content || content.trim().length === 0) {
      throw new InvalidMessageException('Message content cannot be empty');
    }

    if (content.trim().length > 5000) {
      throw new InvalidMessageException('Message content cannot exceed 5000 characters');
    }
  }

  getValue(): string {
    return this.value;
  }

  getLength(): number {
    return this.value.length;
  }

  isEmpty(): boolean {
    return this.value.length === 0;
  }

  contains(searchTerm: string): boolean {
    return this.value.toLowerCase().includes(searchTerm.toLowerCase());
  }

  startsWith(prefix: string): boolean {
    return this.value.startsWith(prefix);
  }

  endsWith(suffix: string): boolean {
    return this.value.endsWith(suffix);
  }

  equals(other: MessageContent): boolean {
    return this.value === other.value;
  }

  toString(): string {
    return this.value;
  }

  // Méthodes utilitaires pour le formatage
  getPreview(maxLength: number = 100): string {
    if (this.value.length <= maxLength) {
      return this.value;
    }
    return this.value.substring(0, maxLength) + '...';
  }

  getWordCount(): number {
    return this.value.split(/\s+/).filter(word => word.length > 0).length;
  }

  hasUrl(): boolean {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return urlRegex.test(this.value);
  }

  extractUrls(): string[] {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return this.value.match(urlRegex) || [];
  }

  containsMention(userId: string): boolean {
    return this.value.includes(`@${userId}`);
  }
} 
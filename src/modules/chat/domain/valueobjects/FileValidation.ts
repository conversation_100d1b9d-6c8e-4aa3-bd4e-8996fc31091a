export class FileValidationResult {
  constructor(
    public readonly isValid: boolean,
    public readonly errorMessage?: string
  ) {}

  static valid(): FileValidationResult {
    return new FileValidationResult(true);
  }

  static invalid(message: string): FileValidationResult {
    return new FileValidationResult(false, message);
  }
}

export class FileValidationRules {
  constructor(
    public readonly maxSizeBytes: number,
    public readonly allowedMimeTypes: string[]
  ) {}

  static videoRules(): FileValidationRules {
    return new FileValidationRules(
      100 * 1024 * 1024, // 100MB
      ['video/mp4', 'video/webm', 'video/quicktime', 'video/x-msvideo']
    );
  }

  static audioRules(): FileValidationRules {
    return new FileValidationRules(
      50 * 1024 * 1024, // 50MB
      ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/webm']
    );
  }

  static imageRules(): FileValidationRules {
    return new FileValidationRules(
      10 * 1024 * 1024, // 10MB
      ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    );
  }

  validate(file: File): FileValidationResult {
    // Vérification du type MIME
    if (!this.allowedMimeTypes.some(type => file.type.startsWith(type.split('/')[0] + '/'))) {
      return FileValidationResult.invalid(
        `File type not allowed. Allowed types: ${this.allowedMimeTypes.join(', ')}`
      );
    }

    // Vérification de la taille
    if (file.size > this.maxSizeBytes) {
      const maxSizeMB = Math.round(this.maxSizeBytes / (1024 * 1024));
      return FileValidationResult.invalid(
        `File too large. Maximum size: ${maxSizeMB}MB`
      );
    }

    return FileValidationResult.valid();
  }
} 
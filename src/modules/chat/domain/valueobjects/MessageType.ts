export enum MessageTypeEnum {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  SYSTEM = 'system',
  VIDEO = 'video'
}

export class MessageType {
  private readonly value: MessageTypeEnum;

  constructor(value: MessageTypeEnum) {
    this.value = value;
  }

  static text(): MessageType {
    return new MessageType(MessageTypeEnum.TEXT);
  }

  static image(): MessageType {
    return new MessageType(MessageTypeEnum.IMAGE);
  }

  static file(): MessageType {
    return new MessageType(MessageTypeEnum.FILE);
  }

  static system(): MessageType {
    return new MessageType(MessageTypeEnum.SYSTEM);
  }

  static video(): MessageType {
    return new MessageType(MessageTypeEnum.VIDEO);
  }

  getValue(): MessageTypeEnum {
    return this.value;
  }

  isText(): boolean {
    return this.value === MessageTypeEnum.TEXT;
  }

  isImage(): boolean {
    return this.value === MessageTypeEnum.IMAGE;
  }

  isFile(): boolean {
    return this.value === MessageTypeEnum.FILE;
  }

  isSystem(): boolean {
    return this.value === MessageTypeEnum.SYSTEM;
  }

  isVideo(): boolean {
    return this.value === MessageTypeEnum.VIDEO;
  }

  equals(other: MessageType): boolean {
    return this.value === other.value;
  }

  toString(): string {
    return this.value;
  }
} 
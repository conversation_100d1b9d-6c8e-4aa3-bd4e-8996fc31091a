export class ChatValidationResult {
  constructor(
    public readonly isValid: boolean,
    public readonly errorMessage?: string
  ) {}

  static valid(): ChatValidationResult {
    return new ChatValidationResult(true);
  }

  static invalid(message: string): ChatValidationResult {
    return new ChatValidationResult(false, message);
  }
}

export class ChatValidationService {
  static validateChatParticipants(participants: string[]): ChatValidationResult {
    if (!participants || participants.length === 0) {
      return ChatValidationResult.invalid('At least one participant is required');
    }

    if (participants.length < 2) {
      return ChatValidationResult.invalid('A chat must have at least 2 participants');
    }

    if (participants.length > 2) {
      return ChatValidationResult.invalid('Direct chats can only have 2 participants');
    }

    // Vérifier qu'il n'y a pas de doublons
    const uniqueParticipants = new Set(participants);
    if (uniqueParticipants.size !== participants.length) {
      return ChatValidationResult.invalid('Duplicate participants are not allowed');
    }

    return ChatValidationResult.valid();
  }

  static validateChatId(chatId: string): ChatValidationResult {
    if (!chatId || chatId.trim().length === 0) {
      return ChatValidationResult.invalid('Chat ID is required');
    }

    // Validation du format UUID (exemple)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(chatId)) {
      return ChatValidationResult.invalid('Invalid chat ID format');
    }

    return ChatValidationResult.valid();
  }

  static validateMessageContent(content: string, maxLength: number = 10000): ChatValidationResult {
    if (!content || content.trim().length === 0) {
      return ChatValidationResult.invalid('Message content cannot be empty');
    }

    if (content.length > maxLength) {
      return ChatValidationResult.invalid(`Message too long. Maximum ${maxLength} characters allowed`);
    }

    return ChatValidationResult.valid();
  }
} 
import { UserBlockStatus } from '@/modules/users/domain/entities/User';
import { Participant } from '../valueobjects/Participant';

export interface UserProfileData {
  id: string;
  username?: string;
  name?: string;
  email?: string;
  avatar?: string;
  isOnline?: boolean;
  blockStatus?: UserBlockStatus; // Exemple de statut de blocage
}

export class ParticipantFactory {
  static createFromUserProfile(userData: UserProfileData): Participant {
    return Participant.create({
      id: userData.id,
      name: userData.username || userData.name || `User ${userData.id}`,
      email: userData.email || '',
      avatar: userData.avatar,
      isOnline: userData.isOnline || false,
      blockStatus: userData.blockStatus
    });
  }

  static createUnknownParticipant(userId: string): Participant {
    // Règle métier : Comment gérer un participant inconnu
    return Participant.create({
      id: userId,
      name: 'Unknown User',
      email: '<EMAIL>',
      isOnline: false
    });
  }

  static createSystemParticipant(): Participant {
    // Règle métier : Participant système pour les messages automatiques
    return Participant.create({
      id: 'system',
      name: 'System',
      email: '<EMAIL>',
      isOnline: true,
      blockStatus: UserBlockStatus.NONE
    });
  }

  static createParticipantsFromIds(
    userIds: string[], 
    userProfilesMap: Map<string, UserProfileData>
  ): Participant[] {
    return userIds.map(userId => {
      const userProfile = userProfilesMap.get(userId);
      
      if (userProfile) {
        return this.createFromUserProfile(userProfile);
      }
      
      // Règle métier : Fallback pour utilisateurs inconnus
      return this.createUnknownParticipant(userId);
    });
  }

  static validateParticipantData(userData: UserProfileData): boolean {
    // Règles métier de validation
    if (!userData.id || userData.id.trim().length === 0) {
      return false;
    }

    // Validation de l'email si fourni
    if (userData.email && userData.email.length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        return false;
      }
    }

    return true;
  }
} 
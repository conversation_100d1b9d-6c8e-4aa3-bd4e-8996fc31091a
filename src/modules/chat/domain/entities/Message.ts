import { MessageContent } from '../valueobjects/MessageContent';
import { MessageType, MessageTypeEnum } from '../valueobjects/MessageType';
import { MessageStatus } from '../valueobjects/MessageStatus';

export class Message {
  constructor(
    public readonly id: string,
    public readonly content: MessageContent,
    public readonly senderId: string,
    public readonly receiverId: string,
    public readonly chatId: string,
    public readonly type: MessageType,
    public readonly status: MessageStatus,
    public readonly createdAt: Date,
    public readonly updatedAt?: Date
  ) {}

  public markAsRead(): Message {
    return new Message(
      this.id,
      this.content,
      this.senderId,
      this.receiverId,
      this.chatId,
      this.type,
      MessageStatus.read(),
      this.createdAt,
      new Date()
    );
  }

  public markAsDelivered(): Message {
    return new Message(
      this.id,
      this.content,
      this.senderId,
      this.receiverId,
      this.chatId,
      this.type,
      MessageStatus.delivered(),
      this.createdAt,
      new Date()
    );
  }

  public isFromUser(userId: string): boolean {
    return this.senderId === userId;
  }

  public isToUser(userId: string): boolean {
    return this.receiverId === userId;
  }

  public isSentAfter(date: Date): boolean {
    return this.createdAt > date;
  }

  getFormattedTime(): string {
    return this.createdAt.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }

  canBeEdited(): boolean {
    // Permettre l'édition seulement si le message a du contenu texte
    if (this.type.getValue() === MessageTypeEnum.TEXT) {
      return this.content.getValue().trim().length > 0;
    }
    
    if (this.type.getValue() === MessageTypeEnum.IMAGE || 
        this.type.getValue() === MessageTypeEnum.VIDEO) {
      // Pour les médias, vérifier s'il y a une caption non vide
      try {
        const parsedContent = JSON.parse(this.content.getValue());
        return parsedContent.caption && parsedContent.caption.trim().length > 0;
      } catch (error) {
        // Si on ne peut pas parser le JSON, pas d'édition possible
        return false;
      }
    }
    
    // Autres types de messages : pas d'édition
    return false;
  }

  canBeDeleted(): boolean {
    return true;
  }

  withUpdatedContent(newContent: string): Message {
    return new Message(
      this.id,
      new MessageContent(newContent),
      this.senderId,
      this.receiverId,
      this.chatId,
      this.type,
      this.status,
      this.createdAt,
      new Date()
    );
  }

  // Getters
  getId(): string { 
    return this.id; 
  }
  
  getContent(): MessageContent { 
    return this.content; 
  }
  
  getSenderId(): string { 
    return this.senderId; 
  }
  
  getReceiverId(): string { 
    return this.receiverId; 
  }
  
  getStatus(): MessageStatus { 
    return this.status; 
  }
  
  getType(): MessageType { 
    return this.type; 
  }
  
  getTimestamp(): Date { 
    return this.createdAt; 
  }

  public static create(params: {
    content: string;
    senderId: string;
    receiverId: string;
    type: MessageType;
  }): Message {
    return new Message(
      Math.random().toString(36).substring(2) + Date.now().toString(36),
      new MessageContent(params.content),
      params.senderId,
      params.receiverId,
      '', // chatId will be set later
      params.type,
      MessageStatus.sent(),
      new Date()
    );
  }
} 
import { Message } from "./Message";
import { Participant } from "../valueobjects/Participant";
import { UserBlockStatus } from "@/modules/users/domain/entities/User";

export class Chat {
  constructor(
    public readonly id: string,
    public readonly participants: Participant[],
    public readonly createdAt: Date,
    public readonly updatedAt: Date,
    public readonly lastMessage?: Message
  ) {}

  static create(participants: Participant[]): Chat {
    if (participants.length !== 2) {
      throw new Error("Chat must have exactly 2 participants");
    }

    return new Chat(
      Math.random().toString(36).substring(2) + Date.now().toString(36),
      participants,
      new Date(),
      new Date(),
      undefined
    );
  }

  public hasParticipant(userId: string): boolean {
    return this.participants.some(p => p.id === userId);
  }

  public getOtherParticipant(userId: string): Participant | null {
    const other = this.participants.find(p => p.id !== userId);
    return other || null;
  }

  public getAllOtherParticipants(userId: string): Participant[] {
    return this.participants.filter(p => p.id !== userId);
  }

  public isGroup(): boolean {
    return this.participants.length > 2;
  }

  public getParticipantCount(): number {
    return this.participants.length;
  }

  public withLastMessage(message: Message): Chat {
    return new Chat(
      this.id,
      this.participants,
      this.createdAt,
      new Date(),
      message
    );
  }

  public hasUnreadMessages(userId: string): boolean {
    if (!this.lastMessage) return false;
    return this.lastMessage.receiverId === userId && !this.lastMessage.status.isRead();
  }

  public isActive(): boolean {
    // Un chat est considéré comme actif s'il a eu une activité dans les 30 derniers jours
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    return this.updatedAt > thirtyDaysAgo;
  }

  // Version simple et expressive pour les chats one-to-one
  public getDisplayName(currentUserId: string): string {
    const otherParticipant = this.getOtherParticipant(currentUserId);
    return otherParticipant ? otherParticipant.getName() : 'Utilisateur inconnu';
  }

  public getBlockedStatus(currentUserId: string): UserBlockStatus | undefined {
    const otherParticipant = this.getOtherParticipant(currentUserId);
    if (!otherParticipant) return UserBlockStatus.NONE;
    return otherParticipant.getBlockStatus();
  } 

  // Getters
  getId(): string { 
    return this.id; 
  }
  
  getParticipants(): Participant[] { 
    return [...this.participants]; 
  }

  // Backward compatibility - retourne les IDs des participants
  get participantIds(): string[] {
    return this.participants.map(p => p.id);
  }
  
  getLastMessage(): Message | undefined { 
    return this.lastMessage; 
  }
  
  getCreatedAt(): Date { 
    return this.createdAt; 
  }
  
  getUpdatedAt(): Date { 
    return this.updatedAt; 
  }
} 
import { Message } from "../entities/Message";
import { Chat } from "../entities/Chat";
import { ImageUploadResponseDto, VideoUploadResponseDto } from "../../infrastructure/api/types/ChatTypes";

export interface IChatRepository {
  sendMessage(message: Message): Promise<Message>;
  getChatHistory(userId: string, otherUserId: string, limit?: number, before?: Date): Promise<Message[]>;
  markMessageAsRead(messageId: string): Promise<void>;
  subscribeToNewMessages(callback: (message: Message) => void): void;
  unsubscribeFromNewMessages(): void;
  createChat(participants: string[]): Promise<Chat>;
  getChat(userId: string, otherUserId: string): Promise<Chat | null>;
  getUserChats(userId: string): Promise<Chat[]>;
  uploadImage(file: File, receiverId: string, caption?: string): Promise<ImageUploadResponseDto>;
  uploadVideo(file: File, receiverId: string, caption?: string): Promise<VideoUploadResponseDto>;
  updateMessage(messageId: string, content: string): Promise<Message>;
  deleteMessage(messageId: string): Promise<void>;
} 
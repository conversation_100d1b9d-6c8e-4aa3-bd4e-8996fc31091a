import { IChatRepository } from "../../domain/repositories/IChatRepository";
import { ImageUploadResponseDto } from "../../infrastructure/api/types/ChatTypes";

export class UploadImageUseCase {
  constructor(private chatRepository: IChatRepository) {}

  async execute(params: {
    file: File;
    receiverId: string;
    caption?: string;
  }): Promise<ImageUploadResponseDto> {
    return this.chatRepository.uploadImage(
      params.file,
      params.receiverId,
      params.caption
    );
  }
} 
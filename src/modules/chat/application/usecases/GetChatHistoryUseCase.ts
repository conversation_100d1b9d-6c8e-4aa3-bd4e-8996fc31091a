import { IChatRepository } from "../../domain/repositories/IChatRepository";
import { Message } from "../../domain/entities/Message";

export class GetChatHistoryUseCase {
  constructor(private chatRepository: IChatRepository) {}

  async execute(params: {
    userId: string;
    otherUserId: string;
    limit?: number;
    before?: Date;
  }): Promise<Message[]> {
    return this.chatRepository.getChatHistory(
      params.userId,
      params.otherUserId,
      params.limit,
      params.before
    );
  }
} 
import { IChatRepository } from "../../domain/repositories/IChatRepository";
import { Message } from "../../domain/entities/Message";
import { MessageType } from "../../domain/valueobjects/MessageType";

export class SendMessageUseCase {
  constructor(private chatRepository: IChatRepository) {}

  async execute(params: {
    content: string;
    senderId: string;
    receiverId: string;
    type: MessageType;
  }): Promise<Message> {
    const message = Message.create({
      content: params.content,
      senderId: params.senderId,
      receiverId: params.receiverId,
      type: params.type
    });

    return this.chatRepository.sendMessage(message);
  }
} 
import { IChatRepository } from "../../domain/repositories/IChatRepository";
import { VideoUploadResponseDto } from "../../infrastructure/api/types/ChatTypes";

export class UploadVideoUseCase {
  constructor(private chatRepository: IChatRepository) {}

  async execute(params: {
    file: File;
    receiverId: string;
    caption?: string;
  }): Promise<VideoUploadResponseDto> {
    return this.chatRepository.uploadVideo(
      params.file,
      params.receiverId,
      params.caption
    );
  }
} 
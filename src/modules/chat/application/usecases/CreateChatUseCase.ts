import { IChatRepository } from "../../domain/repositories/IChatRepository";
import { Chat } from "../../domain/entities/Chat";

export class CreateChatUseCase {
  constructor(private chatRepository: IChatRepository) {}

  async execute(participants: string[]): Promise<Chat> {
    // Check if chat already exists
    if (participants.length === 2) {
      const existingChat = await this.chatRepository.getChat(participants[0], participants[1]);
      if (existingChat) {
        return existingChat;
      }
    }

    // Create new chat
    return this.chatRepository.createChat(participants);
  }
} 
import { VideoMessageContent } from '../../presentation/components/VideoMessage';
import { AudioMessageContent } from '../../presentation/components/AudioMessage';

export interface MediaProcessingService {
  processVideo(file: File): Promise<VideoMessageContent>;
  processAudio(file: File): Promise<AudioMessageContent>;
  generateVideoThumbnail(file: File): Promise<string>;
  getMediaDuration(file: File): Promise<number>;
}

export class BrowserMediaProcessingService implements MediaProcessingService {
  async processVideo(file: File): Promise<VideoMessageContent> {
    const [thumbnail, duration] = await Promise.all([
      this.generateVideoThumbnail(file),
      this.getMediaDuration(file)
    ]);

    // En production, uploadez vers votre serveur et obtenez l'URL réelle
    const url = URL.createObjectURL(file);

    return {
      url,
      originalName: file.name,
      size: file.size,
      thumbnail,
      duration,
      mimeType: file.type
    };
  }

  async processAudio(file: File): Promise<AudioMessageContent> {
    const duration = await this.getMediaDuration(file);

    // En production, uploadez vers votre serveur et obtenez l'URL réelle
    const url = URL.createObjectURL(file);

    return {
      url,
      originalName: file.name,
      size: file.size,
      duration,
      mimeType: file.type
    };
  }

  async generateVideoThumbnail(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');

      if (!context) {
        reject(new Error('Unable to create canvas context'));
        return;
      }

      video.addEventListener('loadedmetadata', () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        // Prendre une image au milieu ou à 1 seconde
        video.currentTime = Math.min(1, video.duration / 2);
      });

      video.addEventListener('seeked', () => {
        try {
          context.drawImage(video, 0, 0, canvas.width, canvas.height);
          const thumbnail = canvas.toDataURL('image/jpeg', 0.8);
          resolve(thumbnail);
        } catch (error) {
          reject(error);
        }
      });

      video.addEventListener('error', (error) => {
        reject(new Error(`Video processing error: ${error}`));
      });

      video.src = URL.createObjectURL(file);
    });
  }

  async getMediaDuration(file: File): Promise<number> {
    return new Promise((resolve, reject) => {
      const element = file.type.startsWith('video/') 
        ? document.createElement('video')
        : document.createElement('audio');
      
      element.addEventListener('loadedmetadata', () => {
        resolve(element.duration);
      });

      element.addEventListener('error', (error) => {
        reject(new Error(`Media duration extraction error: ${error}`));
      });

      element.src = URL.createObjectURL(file);
    });
  }
} 
import { Chat } from '../../domain/entities/Chat';
import { IChatRepository } from '../../domain/repositories/IChatRepository';
import { ChatValidationService } from '../../domain/services/ChatValidationService';

export interface CreateChatRequest {
  participants: string[];
  currentUserId: string;
}

export interface CreateChatResult {
  success: boolean;
  chat?: Chat;
  errorMessage?: string;
}

export class ChatApplicationService {
  constructor(
    private chatRepository: IChatRepository
  ) {}

  async createChat(request: CreateChatRequest): Promise<CreateChatResult> {
    // Validation métier via service de domaine
    const validationResult = ChatValidationService.validateChatParticipants(request.participants);
    if (!validationResult.isValid) {
      return {
        success: false,
        errorMessage: validationResult.errorMessage
      };
    }

    try {
      const chat = await this.chatRepository.createChat(request.participants);
      return {
        success: true,
        chat
      };
    } catch (error) {
      return {
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Failed to create chat'
      };
    }
  }

  async getChatBetweenUsers(userId: string, otherUserId: string): Promise<Chat | null> {
    // Validation des IDs
    const userValidation = ChatValidationService.validateChatId(userId);
    const otherUserValidation = ChatValidationService.validateChatId(otherUserId);
    
    if (!userValidation.isValid || !otherUserValidation.isValid) {
      throw new Error('Invalid user IDs provided');
    }

    return await this.chatRepository.getChat(userId, otherUserId);
  }

  async getUserChats(userId: string): Promise<Chat[]> {
    // Validation de l'ID utilisateur
    const validation = ChatValidationService.validateChatId(userId);
    if (!validation.isValid) {
      throw new Error(validation.errorMessage);
    }

    return await this.chatRepository.getUserChats(userId);
  }
} 
import { User } from "../../domain/entities/User";
import { UserRepository } from "../../domain/repositories/UserRepository";

export interface UpdateConnectedUserProfileUseCaseRequest {
  userId: string;
  profile: User;
}

export interface UpdateConnectedUserProfileUseCaseResponse {
  user?: User;
  success: boolean;
  message?: string;
}

export class UpdateConnectedUserProfileUseCase {
  constructor(private userRepository: UserRepository) {}

  async execute(
    request: UpdateConnectedUserProfileUseCaseRequest,
  ): Promise<UpdateConnectedUserProfileUseCaseResponse> {
    try {
      await this.userRepository.updateConnectedUserProfile(request.userId, request.profile);
      // Optionnel : récupérer l’utilisateur mis à jour si besoin
      // const user = await this.userRepository.findById(request.userId);
      return { success: true, message: "Profil mis à jour" };
    } catch (error: any) {
      return { success: false, message: error?.message || "Erreur inconnue" };
    }
  }
}
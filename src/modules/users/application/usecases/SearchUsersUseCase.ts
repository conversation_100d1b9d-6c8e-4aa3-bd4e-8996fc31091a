import { User } from "../../domain/entities/User";
import { UserRepository } from "../../domain/repositories/UserRepository";

export interface SearchUsersRequest {
  searchTerm: string;
}

export interface SearchUsersResponse {
  users: User[];
}

export class SearchUsersUseCase {
  constructor(private readonly userRepository: UserRepository) {}

  async execute(request: SearchUsersRequest): Promise<SearchUsersResponse> {
    if (!request.searchTerm || request.searchTerm.trim().length < 2) {
      return { users: [] };
    }

    const users = await this.userRepository.searchUsers(request.searchTerm.trim());
    
    return { users };
  }
} 
import { UserRepository } from "../../domain/repositories/UserRepository";
import { ProfilePhotos } from "../../domain/valueobjects/ProfilePhotos";
import { PersonalInfo } from "../../domain/valueobjects/PersonalInfo";
import { Appearance } from "../../domain/valueobjects/Appearance";

export interface UpdateOnboardingStepRequest {
  userId: string;
  step: number;
  data: any;
}

export interface UpdateOnboardingStepResponse {
  success: boolean;
  message?: string;
  nextStep?: number;
}

export class UpdateOnboardingStepUseCase {
  constructor(private userRepository: UserRepository) {}

  async execute(
    request: UpdateOnboardingStepRequest
  ): Promise<UpdateOnboardingStepResponse> {
    try {
      const { userId, step, data } = request;

      // Valider l'étape
      if (step < 1 || step > 3) {
        return {
          success: false,
          message: "Étape invalide. Les étapes valides sont 1, 2 et 3.",
        };
      }

      // Valider et convertir les données selon l'étape
      let validatedData: any;

      switch (step) {
        case 1: // Photos
          if (data instanceof ProfilePhotos) {
            validatedData = data;
          } else if (Array.isArray(data)) {
            validatedData = ProfilePhotos.fromArray(data);
          } else {
            return {
              success: false,
              message: "Données de photos invalides",
            };
          }
          break;

        case 2: // Informations personnelles
          if (data instanceof PersonalInfo) {
            validatedData = data;
          } else {
            try {
              validatedData = PersonalInfo.create(
                data.firstName,
                data.lastName,
                new Date(data.birthdate),
                data.gender,
                data.country,
                data.relationType
              );
            } catch (error) {
              return {
                success: false,
                message: `Informations personnelles invalides: ${
                  error instanceof Error ? error.message : "Erreur inconnue"
                }`,
              };
            }
          }
          break;

        case 3: // Apparence
          if (data instanceof Appearance) {
            validatedData = data;
          } else {
            try {
              validatedData = new Appearance(
                data.ethnicity,
                data.height,
                data.bodyType,
                data.hairColor,
                data.eyeColor,
                data.bodyColor,
                data.religion
              );
            } catch (error) {
              return {
                success: false,
                message: `Données d'apparence invalides: ${
                  error instanceof Error ? error.message : "Erreur inconnue"
                }`,
              };
            }
          }
          break;

        default:
          return {
            success: false,
            message: "Étape non supportée",
          };
      }

      // Mettre à jour l'étape dans le repository
      await this.userRepository.updateOnboardingStep(
        userId,
        step,
        validatedData
      );

      // Déterminer la prochaine étape
      const nextStep = step < 3 ? step + 1 : undefined;

      return {
        success: true,
        message: `Étape ${step} mise à jour avec succès`,
        nextStep,
      };
    } catch (error) {
      console.error(
        "Erreur lors de la mise à jour de l'étape d'onboarding:",
        error
      );
      return {
        success: false,
        message: `Erreur lors de la mise à jour: ${
          error instanceof Error ? error.message : "Erreur inconnue"
        }`,
      };
    }
  }
}

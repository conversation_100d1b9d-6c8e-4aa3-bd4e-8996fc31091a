import { PersonalInfo } from '../../domain/valueobjects/PersonalInfo';
import { Appearance } from '../../domain/valueobjects/Appearance';
import { OnboardingPhoto } from '../../domain/valueobjects/OnboardingPhoto';
import { UserRepository } from '../../domain/repositories/UserRepository';

export interface OnboardingData {
  photo?: OnboardingPhoto;             // Étape 1: UNE photo de profil seulement
  personalInfo?: PersonalInfo;         // Étape 2: Informations personnelles
  appearance?: Appearance;             // Étape 3: Apparence
}

export interface CompleteOnboardingRequest {
  userId: string;
  onboardingData: OnboardingData;
}

export interface CompleteOnboardingResponse {
  success: boolean;
  profileCompletionPercentage: number;
}

export class CompleteOnboardingUseCase {
  constructor(private userRepository: UserRepository) {}

  async execute(request: CompleteOnboardingRequest): Promise<CompleteOnboardingResponse> {
    try {
      this.validateOnboardingData(request.onboardingData);

      // Calculer le pourcentage de complétude
      const completionPercentage = this.calculateCompletion(request.onboardingData);

      // Si l'onboarding est complet, créer le profil utilisateur
      if (completionPercentage === 100) {
        const userProfile = this.createUserProfile(request.onboardingData);
        await this.userRepository.updateUserProfile(request.userId, userProfile);
      } else {
        // Sinon, sauvegarder les données d'onboarding partielles
        await this.savePartialOnboarding(request.userId, request.onboardingData);
      }

      return {
        success: true,
        profileCompletionPercentage: completionPercentage
      };
    } catch (error) {
      throw new Error(`Erreur lors de la finalisation de l'onboarding: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }

  private validateOnboardingData(data: OnboardingData): void {
    // Les validations sont maintenant gérées par les value objects eux-mêmes
    // Ce use case se contente de vérifier que les objets sont valides
    // Les erreurs de validation seront lancées par les constructeurs des value objects
  }

  private calculateCompletion(data: OnboardingData): number {
    let completedSteps = 0;
    const totalSteps = 3;

    // Étape 1: Photo (une seule)
    if (data.photo) {
      completedSteps++;
    }

    // Étape 2: Informations personnelles
    if (data.personalInfo) {
      completedSteps++;
    }

    // Étape 3: Apparence
    if (data.appearance) {
      completedSteps++;
    }

    return Math.round((completedSteps / totalSteps) * 100);
  }

  private createUserProfile(data: OnboardingData): any {
    if (!data.photo || !data.personalInfo || !data.appearance) {
      throw new Error('Toutes les étapes d\'onboarding doivent être complétées');
    }

    // Convertir OnboardingPhoto vers ProfilePhoto pour le profil final
    const profilePhoto = data.photo.toProfilePhoto();

    return {
      photos: [profilePhoto], // Convertir en tableau pour ProfilePhotos
      personalInfo: data.personalInfo,
      appearance: data.appearance,
      isProfileComplete: true
    };
  }

  private async savePartialOnboarding(userId: string, data: OnboardingData): Promise<void> {
    // Sauvegarder les données partielles pour reprendre plus tard
    // Cette méthode pourrait sauvegarder dans un cache ou une collection temporaire
    console.log(`Sauvegarde des données d'onboarding partielles pour l'utilisateur ${userId}`, data);
  }
} 
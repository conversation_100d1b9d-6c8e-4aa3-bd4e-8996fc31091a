import { apiService } from '@/modules/shared/infrastructure/api/api';

export interface LikeUserRequest {
  targetUserId: string;
}

export interface LikeUserResponse {
  success: boolean;
  message: string;
}

export interface GetLikedUsersResponse {
  success: boolean;
  data: Array<{
    id: string;
    username: string;
    email: string;
    avatar?: string;
    isOnline: boolean;
    profilePhoto?: string;
    firstName?: string;
    lastName?: string;
    birthdate?: Date;
    gender?: string;
    country?: string;
    ethnicity?: string;
    height?: number;
  }>;
  total: number;
  hasMore: boolean;
  pagination: {
    offset: number;
    limit: number;
  };
}

export class UserLikeApiService {
  static async likeUser(targetUserId: string): Promise<LikeUserResponse> {
    const response = await apiService.post<LikeUserResponse>('/api/users/like', {
      targetUserId,
    });
    return response;
  }

  static async dislikeUser(targetUserId: string): Promise<LikeUserResponse> {
    const response = await apiService.post<LikeUserResponse>('/api/users/dislike', {
      targetUserId,
    });
    return response;
  }

  static async removeUserInteraction(targetUserId: string): Promise<LikeUserResponse> {
    const response = await apiService.post<LikeUserResponse>('/api/users/remove-interaction', {
      targetUserId,
    });
    return response;
  }

  static async getLikedUsers(limit: number = 10, offset: number = 0): Promise<GetLikedUsersResponse> {
    const response = await apiService.get<GetLikedUsersResponse>(`/api/users/liked?limit=${limit}&offset=${offset}`);
    return response;
  }

  static async getDislikedUsers(limit: number = 10, offset: number = 0): Promise<GetLikedUsersResponse> {
    const response = await apiService.get<GetLikedUsersResponse>(`/api/users/disliked?limit=${limit}&offset=${offset}`);
    return response;
  }
} 
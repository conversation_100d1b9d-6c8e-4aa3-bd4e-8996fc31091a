import { apiService } from "@/modules/shared/infrastructure/api/api";
import { ApiResponse, UserSearchResponse } from "./types/ApiResponseTypes";
import { User, UserBlockStatus } from "../../domain/entities/User";

export class UserApiClient {
  constructor() {
    // Utilise maintenant le service API partagé qui gère déjà l'authentification
  }

  public async searchUsers(
    searchTerm: string
  ): Promise<ApiResponse<UserSearchResponse[]>> {
    try {
      return await apiService.get<ApiResponse<UserSearchResponse[]>>(
        `/api/users/search?q=${encodeURIComponent(searchTerm)}`
      );
    } catch (error) {
      console.error("Error searching users:", error);
      throw this.handleError(error);
    }
  }

  public async getUserSuggestions(
    limit: number = 5
  ): Promise<ApiResponse<UserSearchResponse[]>> {
    try {
      return await apiService.get<ApiResponse<UserSearchResponse[]>>(
        `/api/users/suggestions?limit=${limit}`
      );
    } catch (error) {
      console.error("Error getting user suggestions:", error);
      throw this.handleError(error);
    }
  }

  public async getUserById(
    userId: string
  ): Promise<ApiResponse<UserSearchResponse>> {
    try {
      return await apiService.get<ApiResponse<UserSearchResponse>>(
        `/api/users/${userId}`
      );
    } catch (error) {
      console.error(`Error fetching user ${userId}:`, error);
      throw this.handleError(error);
    }
  }

  public async updateUserProfile(
    userId: string,
    profileData: any
  ): Promise<ApiResponse<void>> {
    try {
      return await apiService.put<ApiResponse<void>>(
        `/api/users/${userId}/profile`,
        profileData
      );
    } catch (error) {
      console.error(`Error updating user profile for ${userId}:`, error);
      throw this.handleError(error);
    }
  }

  public async updateOnboardingStep(
    userId: string,
    step: number,
    data: any
  ): Promise<ApiResponse<void>> {
    try {
      return await apiService.put<ApiResponse<void>>(
        `/api/users/${userId}/onboarding/step/${step}`,
        data
      );
    } catch (error) {
      console.error(
        `Error updating onboarding step ${step} for user ${userId}:`,
        error
      );
      throw this.handleError(error);
    }
  }

  public async getUserProfile(userId: string): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<ApiResponse<any>>(
        `/api/users/${userId}/profile`
      );
    } catch (error) {
      console.error(`Error fetching user profile for ${userId}:`, error);
      throw this.handleError(error);
    }
  }

  public async getConnectedUserProfile(): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<ApiResponse<any>>(`/api/users/profile/me`);
    } catch (error) {
      console.error(`Error fetching connected user profile:`, error);
      throw this.handleError(error);
    }
  }

  public async getUserOnboardingData(): Promise<ApiResponse<any>> {
    try {
      return await apiService.get<ApiResponse<any>>("/api/users/onboarding");
    } catch (error) {
      console.error("Error fetching user onboarding data:", error);
      throw this.handleError(error);
    }
  }

  public async completeOnboarding(
    onboardingData: any
  ): Promise<ApiResponse<{ profileCompletionPercentage: number }>> {
    try {
      return await apiService.post<
        ApiResponse<{ profileCompletionPercentage: number }>
      >("/api/users/onboarding", onboardingData);
    } catch (error) {
      console.error("Error completing onboarding:", error);
      throw this.handleError(error);
    }
  }

  public async searchUsersWithFilters(params: {
    searchTerm: string;
    limit?: number;
    offset?: number;
    gender?: string;
    minAge?: number;
    maxAge?: number;
    country?: string;
    ethnicity?: string;
    minHeight?: number;
    maxHeight?: number;
    isOnline?: boolean;
    relationType?: string;
    hairColor?: string;
    eyeColor?: string;
    bodyType?: string;
    religion?: string;
    bodyColor?: string;
  }): Promise<
    ApiResponse<UserSearchResponse[]> & {
      hasMore: boolean;
      pagination: { offset: number; limit: number };
    }
  > {
    try {
      const queryParams = new URLSearchParams();

      if (params.searchTerm && params.searchTerm.trim().length > 0) {
        queryParams.append("q", params.searchTerm);
      }

      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.offset) queryParams.append("offset", params.offset.toString());
      if (params.gender) queryParams.append("gender", params.gender);
      if (params.minAge) queryParams.append("minAge", params.minAge.toString());
      if (params.maxAge) queryParams.append("maxAge", params.maxAge.toString());
      if (params.country) queryParams.append("country", params.country);
      if (params.ethnicity) queryParams.append("ethnicity", params.ethnicity);
      if (params.minHeight)
        queryParams.append("minHeight", params.minHeight.toString());
      if (params.maxHeight)
        queryParams.append("maxHeight", params.maxHeight.toString());
      if (params.isOnline !== undefined)
        queryParams.append("isOnline", params.isOnline.toString());
      if (params.relationType)
        queryParams.append("relationType", params.relationType);
      if (params.hairColor) queryParams.append("hairColor", params.hairColor);
      if (params.eyeColor) queryParams.append("eyeColor", params.eyeColor);
      if (params.bodyType) queryParams.append("bodyType", params.bodyType);
      if (params.religion) queryParams.append("religion", params.religion);
      if (params.bodyColor) queryParams.append("bodyColor", params.bodyColor);

      return await apiService.get<
        ApiResponse<UserSearchResponse[]> & {
          hasMore: boolean;
          pagination: { offset: number; limit: number };
        }
      >(`/api/users/search?${queryParams.toString()}`);
    } catch (error) {
      console.error("Error searching users with filters:", error);
      throw this.handleError(error);
    }
  }

  public async updateConnectedUserProfile(
    id: string,
    profileData: any
  ): Promise<ApiResponse<void>> {
    try {
      return await apiService.put<ApiResponse<void>>(
        `/api/users/profile/${id}`,
        profileData
      );
    } catch (error) {
      console.error(`Error updating connected user profile:`, error);
      throw this.handleError(error);
    }
  }

  public async reportUser(data: {
    reportedByID: string;
    reportedToID: string;
    reason: string;
  }): Promise<ApiResponse<void>> {
    try {
      return await apiService.post<ApiResponse<void>>(
        `/api/report_users`,
        data
      );
    } catch (error) {
      console.error("Error reporting user:", error);
      throw this.handleError(error);
    }
  }

  public async getReportUserByUserId(): Promise<ApiResponse<User[]>> {
    try {
      return await apiService.get<ApiResponse<any>>(`/api/report_users`);
    } catch (error) {
      console.error(`Error fetching report users:`, error);
      throw this.handleError(error);
    }
  }

  public async updateBlockStatus(
    userId: string,
    blockStatus: UserBlockStatus
  ): Promise<ApiResponse<void>> {
    try {
      return await apiService.put<ApiResponse<void>>(
        `/api/report_users/${userId}/block-status`,
        { blockStatus }
      );
    } catch (error) {
      console.error(`Error updating block status for user ${userId}:`, error);
      throw this.handleError(error);
    }
  }

  private handleError(error: any): Error {
    const message = error.response?.data?.message || error.message;
    const status = error.response?.status;

    switch (status) {
      case 401:
        return new Error("Unauthorized - Please login again");
      case 403:
        return new Error(
          "Forbidden - You do not have permission to perform this action"
        );
      case 404:
        return new Error("User not found");
      case 500:
        return new Error("Internal Server Error - Please try again later");
      default:
        return new Error(message || "An unexpected error occurred");
    }
  }
}

// Export singleton instance
export const userApiClient = new UserApiClient();
export default userApiClient;

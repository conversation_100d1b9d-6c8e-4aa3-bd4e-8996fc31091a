import { apiService } from '@/modules/shared/infrastructure/api/api';
import { PhotoUploadService, PhotoUploadResult } from '../../domain/services/PhotoUploadService';

export class ApiPhotoUploadService implements PhotoUploadService {
  constructor() {
    // Utilise maintenant le service API partagé qui gère déjà l'authentification
  }

  async uploadProfilePhoto(file: File): Promise<PhotoUploadResult> {
    const formData = new FormData();
    formData.append('image', file);

    try {
      // Utilise apiService qui gère automatiquement l'authentification
      const result = await apiService.post<{
        success: boolean;
        message?: string;
        data: {
          id?: string;
          url: string;
          path?: string;
          filename?: string;
        };
      }>('/api/users/profile-photo/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      if (!result.success) {
        throw new Error(result.message || 'Erreur lors de l\'upload');
      }

      return {
        id: result.data.id || `photo-${Date.now()}`,
        url: result.data.url,
        path: result.data.path || '',
        filename: result.data.filename || file.name
      };
    } catch (error) {
      console.error('Upload error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erreur lors de l\'upload de la photo');
    }
  }
} 
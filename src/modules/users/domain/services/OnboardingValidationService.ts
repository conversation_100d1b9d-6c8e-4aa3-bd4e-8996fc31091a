import { OnboardingPhoto } from '../valueobjects/OnboardingPhoto';
import { PersonalInfo } from '../valueobjects/PersonalInfo';
import { Appearance } from '../valueobjects/Appearance';

export class OnboardingValidationService {

  // Règle métier globale : profil complet (3 étapes)
  static isOnboardingComplete(data: {
    photo?: OnboardingPhoto;
    personalInfo?: PersonalInfo;
    appearance?: Appearance;
  }): boolean {
    return !!(
      data.photo &&
      data.personalInfo &&
      data.appearance
    );
  }

  // Calcul du score de complétude (règle métier cross-cutting)
  static calculateCompletionScore(data: {
    photo?: OnboardingPhoto;
    personalInfo?: PersonalInfo;
    appearance?: Appearance;
  }): number {
    let score = 0;
    const maxScore = 3; // 3 étapes

    if (data.photo) score++;
    if (data.personalInfo) score++;
    if (data.appearance) score++;

    return Math.round((score / maxScore) * 100);
  }

  // Méthode utilitaire pour calculer l'âge
  private static calculateAge(birthdate: Date): number {
    const today = new Date();
    let age = today.getFullYear() - birthdate.getFullYear();
    const monthDiff = today.getMonth() - birthdate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthdate.getDate())) {
      age--;
    }
    
    return age;
  }

  // Obtenir la prochaine étape à compléter (logique de navigation)
  static getNextStep(data: {
    photo?: OnboardingPhoto;
    personalInfo?: PersonalInfo;
    appearance?: Appearance;
  }): number | null {
    if (!data.photo) return 1;
    if (!data.personalInfo) return 2;
    if (!data.appearance) return 3;
    return null; // Onboarding complet
  }

  // Validation des étapes précédentes (règle métier de workflow)
  static canAccessStep(step: number, data: {
    photo?: OnboardingPhoto;
    personalInfo?: PersonalInfo;
    appearance?: Appearance;
  }): boolean {
    switch (step) {
      case 1:
        return true; // Première étape toujours accessible
      case 2:
        return !!data.photo; // Étape 2 si photo OK
      case 3:
        return !!(data.photo && data.personalInfo); // Étape 3 si photo + info OK
      default:
        return false;
    }
  }

  // Obtenir le statut détaillé de chaque étape
  static getStepsStatus(data: {
    photo?: OnboardingPhoto;
    personalInfo?: PersonalInfo;
    appearance?: Appearance;
  }) {
    return {
      step1: {
        completed: !!data.photo,
        canAccess: true,
        title: 'Photo de profil'
      },
      step2: {
        completed: !!data.personalInfo,
        canAccess: !!data.photo,
        title: 'Informations personnelles'
      },
      step3: {
        completed: !!data.appearance,
        canAccess: !!(data.photo && data.personalInfo),
        title: 'Apparence'
      }
    };
  }

  // Validation de cohérence globale (règles métier cross-cutting)
  static validateOnboardingCoherence(data: {
    photo?: OnboardingPhoto;
    personalInfo?: PersonalInfo;
    appearance?: Appearance;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Règle métier : profil cohérent pour un site de rencontre
    if (data.personalInfo && data.personalInfo.getAge() < 18) {
      errors.push('Vous devez être majeur pour vous inscrire');
    }

    // Règle métier : photo récente recommandée
    if (data.photo && !data.photo.isRecentlyUploaded(60)) {
      // Avertissement mais pas d'erreur bloquante
      console.warn('La photo semble avoir été uploadée il y a plus d\'une heure');
    }

    // Règle métier : profil complet si toutes les données sont présentes
    if (data.photo && data.personalInfo && data.appearance) {
      if (!this.isOnboardingComplete(data)) {
        errors.push('Le profil semble incomplet malgré la présence de toutes les données');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
} 
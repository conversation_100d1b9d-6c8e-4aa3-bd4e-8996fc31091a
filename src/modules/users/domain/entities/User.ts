import {
  BodyColor,
  BodyType,
  EyeColor,
  Religion,
} from "../valueobjects/Appearance";

export enum UserBlockStatus {
  BLOCKED = "blocked",
  PENDING = "pending",
  CANCELED = "canceled",
  NONE = "none",
}

export class User {
  constructor(
    public readonly id: string,
    public readonly username: string,
    public readonly email: string,
    public readonly avatar?: string,
    public readonly isOnline?: boolean,
    public readonly profilePhoto?: string,
    public readonly firstName?: string,
    public readonly lastName?: string,
    public readonly birthdate?: string,
    public readonly gender?: string,
    public readonly country?: string,
    public readonly ethnicity?: string,
    public readonly height?: number,
    public readonly blockStatus?: UserBlockStatus,
    public readonly roles?: string[],
    public readonly hairColor?: string,
    public readonly eyeColor?: string,
    public readonly bodyType?: string,
    public readonly bodyColor?: string,
    public readonly religion?: string,
    public readonly isOnboardingComplete?: boolean,
    public readonly profileCompletionPercentage?: number,
    public readonly interactionType?: "like" | "dislike" | "NONE"
  ) {}

  static create(data: {
    id: string;
    username: string;
    email: string;
    avatar?: string;
    isOnline?: boolean;
    profilePhoto?: string;
    firstName?: string;
    lastName?: string;
    birthdate?: string;
    gender?: string;
    country?: string;
    ethnicity?: string;
    height?: number;
    blockStatus?: UserBlockStatus;
    roles?: string[];
    hairColor?: string;
    eyeColor?: string;
    bodyType?: string;
    bodyColor?: string;
    religion?: string;
    isOnboardingComplete?: boolean;
    profileCompletionPercentage?: number;
    interactionType?: "like" | "dislike" | "NONE";
  }): User {
    return new User(
      data.id,
      data.username,
      data.email,
      data.avatar,
      data.isOnline,
      data.profilePhoto,
      data.firstName,
      data.lastName,
      data.birthdate,
      data.gender,
      data.country,
      data.ethnicity,
      data.height,
      data.blockStatus,
      data.roles,
      data.hairColor,
      data.eyeColor,
      data.bodyType,
      data.bodyColor,
      data.religion,
      data.isOnboardingComplete,
      data.profileCompletionPercentage,
      data.interactionType
    );
  }

  public withInteractionType(
    interactionType: "like" | "dislike" | "NONE"
  ): User {
    return new User(
      this.id,
      this.username,
      this.email,
      this.avatar,
      this.isOnline,
      this.profilePhoto,
      this.firstName,
      this.lastName,
      this.birthdate,
      this.gender,
      this.country,
      this.ethnicity,
      this.height,
      this.blockStatus,
      this.roles,
      this.hairColor,
      this.eyeColor,
      this.bodyType,
      this.bodyColor,
      this.religion,
      this.isOnboardingComplete,
      this.profileCompletionPercentage,
      interactionType
    );
  }

  public getDisplayName(): string {
    return this.username;
  }

  public getFullName(): string | null {
    if (this.firstName && this.lastName) {
      return `${this.firstName} ${this.lastName}`;
    }
    return null;
  }

  public getInitials(): string {
    return this.username.charAt(0).toUpperCase();
  }

  public isCurrentlyOnline(): boolean {
    return this.isOnline || false;
  }

  public getProfilePhotoUrl(): string | null {
    return this.profilePhoto || null;
  }

  public getAge(): number | null {
    if (!this.birthdate) return null;

    const today = new Date();
    const birthDate = new Date(this.birthdate);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  }

  public getGenderDisplay(): string | null {
    if (!this.gender) return null;

    const genderMap: { [key: string]: string } = {
      male: "Homme",
      female: "Femme",
      non_binary: "Non-binaire",
      other: "Autre",
    };

    return genderMap[this.gender] || this.gender;
  }

  public getEthnicityDisplay(): string | null {
    if (!this.ethnicity) return null;

    const ethnicityMap: { [key: string]: string } = {
      caucasian: "Caucasien",
      african: "Africain",
      asian: "Asiatique",
      hispanic: "Hispanique",
      middle_eastern: "Moyen-Orient",
      mixed: "Mixte",
      other: "Autre",
      prefer_not_to_say: "Préfère ne pas dire",
    };

    return ethnicityMap[this.ethnicity] || this.ethnicity;
  }

  public getHairColorDisplay(): string | null {
    if (!this.hairColor) return null;

    const hairColorMap: { [key: string]: string } = {
      black: "Black",
      blonde: "Blonde",
      brown: "Brown",
      red: "Red",
      gray: "Gray",
      white: "White",
    };

    return hairColorMap[this.hairColor] || this.hairColor;
  }

  public getEyeColorDisplay(): string | null {
    if (!this.eyeColor) return null;

    const eyeColorMap: { [key: string]: string } = {
      [EyeColor.BLUE]: "Blue",
      [EyeColor.BROWN]: "Brown",
      [EyeColor.GREEN]: "Green",
      [EyeColor.HAZEL]: "Hazel",
      [EyeColor.GRAY]: "Gray",
    };

    return eyeColorMap[this.eyeColor] || this.eyeColor;
  }

  public getBodyTypeDisplay(): string | null {
    if (!this.bodyType) return null;

    const bodyTypeMap: { [key: string]: string } = {
      [BodyType.SLIM]: "Slim",
      [BodyType.AVERAGE]: "Average",
      [BodyType.ATHLETIC]: "Athletic",
      [BodyType.CURVY]: "Curvy",
      [BodyType.PETITE]: "Small",
    };

    return bodyTypeMap[this.bodyType] || this.bodyType;
  }

  public getBodyColorDisplay(): string | null {
    if (!this.bodyColor) return null;

    const bodyColorMap: { [key: string]: string } = {
      [BodyColor.WHITE]: "White",
      [BodyColor.BLONDE]: "Blonde",
      [BodyColor.BLACK]: "Black",
      [BodyColor.BROWN]: "Brown",
      [BodyColor.YELLOW]: "Yellow",
      [BodyColor.MIXED]: "Mixed",
    };

    return bodyColorMap[this.bodyColor] || this.bodyColor;
  }

  public getReligionDisplay(): string | null {
    if (!this.religion) return null;

    const religionMap: { [key: string]: string } = {
      [Religion.CHRISTIANITY]: "Christianity",
      [Religion.ISLAM]: "Islam",
      [Religion.JUDAISM]: "Judaism",
      [Religion.HINDUISM]: "Hinduism",
      [Religion.BUDDHISM]: "Buddhism",
      [Religion.ATHEISM]: "Atheism",
    };

    return religionMap[this.religion] || this.religion;
  }

  public getHeightDisplay(): string | null {
    if (!this.height) return null;
    return `${this.height} cm`;
  }
}

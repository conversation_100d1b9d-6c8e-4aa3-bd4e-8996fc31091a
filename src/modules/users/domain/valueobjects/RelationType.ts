export class RelationType {
  private static readonly VALID_RELATION_TYPES = ['single', 'friends', 'in_a_relationship', 'married', 'divorced', 'widowed'];

  constructor(public readonly value: string | string[]) {
    // Toujours stocker comme un tableau
    this.value = Array.isArray(value) ? value : [value];
    this.validate();
  }

  private validate(): void {
    if (
      !this.value ||
      !Array.isArray(this.value) ||
      !this.value.every(val => RelationType.VALID_RELATION_TYPES.includes(val))
    ) {
      throw new Error(
        `Relation type "${this.value}" is not valid. Valid types are: ${RelationType.VALID_RELATION_TYPES.join(', ')}`
      );
    }
  }

  toString(): string {
  return (Array.isArray(this.value) ? this.value : [this.value]).join(', ');
}

  equals(other: RelationType): boolean {
    return Array.isArray(this.value) &&
      Array.isArray(other.value) &&
      this.value.length === other.value.length &&
      this.value.every((val, idx) => val === other.value[idx]);
  }
}
export class PersonName {
  constructor(public readonly value: string) {
    this.validate();
  }

  private validate(): void {
    if (!this.value?.trim()) {
      throw new Error('Le nom est requis');
    }

    if (this.value.trim().length < 2) {
      throw new Error('Le nom doit contenir au moins 2 caractères');
    }

    if (this.value.trim().length > 50) {
      throw new Error('Le nom ne peut pas dépasser 50 caractères');
    }

    // Règles métier pour les noms de personne
    const namePattern = /^[a-zA-ZÀ-ÿ\s\-'\.]+$/;
    if (!namePattern.test(this.value.trim())) {
      throw new Error('Le nom ne peut contenir que des lettres, espaces, tirets et apostrophes');
    }
  }

  toString(): string {
    return this.value.trim();
  }

  // Méthode utilitaire pour la capitalisation
  toCapitalized(): string {
    return this.value
      .trim()
      .split(' ')
      .map(word => {
        if (word.length === 0) return word;
        // G<PERSON>rer les mots avec tirets et apostrophes
        return word
          .split(/(-|')/)
          .map(part => {
            if (part === '-' || part === "'" || part.length === 0) return part;
            return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase();
          })
          .join('');
      })
      .join(' ');
  }

  equals(other: PersonName): boolean {
    return this.value.trim().toLowerCase() === other.value.trim().toLowerCase();
  }
} 
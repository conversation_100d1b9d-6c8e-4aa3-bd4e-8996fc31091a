import { PersonName } from './PersonName';
import { Birthdate } from './Birthdate';
import { Country, SupportedCountryCode } from './Country';
import { RelationType } from './RelationType';

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  NON_BINARY = 'non_binary',
  OTHER = 'other'
}

export class PersonalInfo {
  constructor(
    public readonly firstName: PersonName,
    public readonly lastName: PersonName,
    public readonly birthdate: Birthdate,
    public readonly gender: Gender,
    public readonly country: Country,
    public readonly relationType: RelationType
  ) {
    this.validate();
  }

  private validate(): void {
    // Les validations individuelles sont déjà faites dans chaque value object
    // Ici on ne valide que les règles cross-cutting
    
    if (!Object.values(Gender).includes(this.gender)) {
      throw new Error('Le genre spécifié n\'est pas valide');
    }

    // Règle métier : vérifier la cohérence entre l'âge et le pays (exemple)
    // On pourrait avoir des règles spécifiques selon le pays
  }

  // Méthodes déléguées aux value objects
  getAge(): number {
    return this.birthdate.getAge();
  }

  getDisplayAge(): string {
    return this.birthdate.getDisplayAge();
  }

  getFullName(): string {
    return `${this.firstName.toCapitalized()} ${this.lastName.toCapitalized()}`;
  }

  // Nouvelles méthodes utiles
  getDisplayName(): string {
    return this.getFullName();
  }

  getCountryName(): string {
    return this.country.getName();
  }

  isFromEurope(): boolean {
    return this.country.isEuropean();
  }

  isFrancophone(): boolean {
    return this.country.isFrancophone();
  }

  // Méthodes de création statiques pour faciliter l'usage
    static create(
    firstName: string,
    lastName: string,
    birthdate: Date,
    gender: Gender,
    countryCode: string,
    relationTypes: (RelationType | string)
  ): PersonalInfo {
    return new PersonalInfo(
      new PersonName(firstName),
      new PersonName(lastName),
      new Birthdate(birthdate),
      gender,
      new Country(countryCode as SupportedCountryCode),
      relationTypes.toString() as any
    );
  }

  // Convertir vers un format simple pour l'API
  toApiFormat() {
  return {
    firstName: this.firstName.toString(),
    lastName: this.lastName.toString(),
    birthdate: this.birthdate.value,
    gender: this.gender,
    country: this.country.getCode(),
    relationTypes: this.relationType // <-- correction ici
  };
}
} 
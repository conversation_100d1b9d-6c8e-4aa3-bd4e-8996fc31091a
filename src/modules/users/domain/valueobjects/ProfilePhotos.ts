import { ProfilePhoto } from './UserProfile';

export class ProfilePhotos {
  constructor(public readonly photos: ProfilePhoto[]) {
    this.validate();
  }

  private validate(): void {
    // Règle métier : au moins une photo pour un site de rencontre
    if (!this.photos || this.photos.length === 0) {
      throw new Error('Au moins une photo est requise');
    }

    // Règle métier : maximum 6 photos
    if (this.photos.length > 6) {
      throw new Error('Maximum 6 photos autorisées');
    }

    // Règle métier : une photo principale obligatoire
    const hasPrimaryPhoto = this.photos.some(photo => photo.isPrimary);
    if (!hasPrimaryPhoto) {
      throw new Error('Une photo principale doit être définie');
    }

    // Règle métier : une seule photo principale
    const primaryPhotos = this.photos.filter(photo => photo.isPrimary);
    if (primaryPhotos.length > 1) {
      throw new Error('Une seule photo peut être définie comme principale');
    }
  }

  getPrimaryPhoto(): ProfilePhoto | undefined {
    return this.photos.find(photo => photo.isPrimary);
  }

  getSecondaryPhotos(): ProfilePhoto[] {
    return this.photos.filter(photo => !photo.isPrimary);
  }

  count(): number {
    return this.photos.length;
  }

  isEmpty(): boolean {
    return this.photos.length === 0;
  }

  canAddMore(): boolean {
    return this.photos.length < 6;
  }

  // Méthodes pour construire immutablement
  static fromArray(photos: ProfilePhoto[]): ProfilePhotos {
    return new ProfilePhotos(photos);
  }

  addPhoto(photo: ProfilePhoto): ProfilePhotos {
    if (!this.canAddMore()) {
      throw new Error('Maximum 6 photos autorisées');
    }
    return new ProfilePhotos([...this.photos, photo]);
  }

  removePhoto(photoId: string): ProfilePhotos {
    const filteredPhotos = this.photos.filter(photo => photo.id !== photoId);
    if (filteredPhotos.length === this.photos.length) {
      throw new Error('Photo non trouvée');
    }
    return new ProfilePhotos(filteredPhotos);
  }

  setPrimaryPhoto(photoId: string): ProfilePhotos {
    const updatedPhotos = this.photos.map(photo => ({
      ...photo,
      isPrimary: photo.id === photoId
    }));
    
    const foundPhoto = updatedPhotos.find(photo => photo.id === photoId);
    if (!foundPhoto) {
      throw new Error('Photo non trouvée');
    }
    
    return new ProfilePhotos(updatedPhotos);
  }
} 
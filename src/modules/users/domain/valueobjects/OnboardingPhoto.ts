export class OnboardingPhoto {
  constructor(
    public readonly id: string,
    public readonly url: string,
    public readonly fileName?: string,
    public readonly uploadedAt: Date = new Date()
  ) {
    this.validate();
  }

  private validate(): void {
    if (!this.id?.trim()) {
      throw new Error('L\'identifiant de la photo est requis');
    }

    if (!this.url?.trim()) {
      throw new Error('L\'URL de la photo est requise');
    }

    // Validation basique de l'URL AVANT la validation du format
    let isValidUrl = true;
    try {
      new URL(this.url);
    } catch {
      isValidUrl = false;
    }

    // Si l'URL n'est pas valide, on vérifie quand même si c'est un problème de format
    // pour donner un message d'erreur plus précis
    if (!isValidUrl) {
      // Vérifier si c'est juste un problème de format
      const supportedFormats = ['.jpg', '.jpeg', '.png', '.webp'];
      const hasValidFormat = supportedFormats.some(format => 
        this.url.toLowerCase().includes(format) || 
        (this.fileName && this.fileName.toLowerCase().endsWith(format))
      );
      
      if (hasValidFormat) {
        // L'URL contient un bon format mais n'est pas valide
        throw new Error('L\'URL de la photo n\'est pas valide');
      } else {
        // L'URL n'est pas valide ET n'a pas le bon format
        throw new Error('L\'URL de la photo n\'est pas valide');
      }
    }

    // Règle métier : formats d'image supportés (seulement si l'URL est valide)
    const supportedFormats = ['.jpg', '.jpeg', '.png', '.webp'];
    const hasValidFormat = supportedFormats.some(format => 
      this.url.toLowerCase().includes(format) || 
      (this.fileName && this.fileName.toLowerCase().endsWith(format))
    );
    
    if (!hasValidFormat) {
      throw new Error('Format d\'image non supporté. Utilisez JPG, PNG ou WebP');
    }
  }

  // Méthodes utilitaires
  getFileExtension(): string {
    if (this.fileName) {
      const parts = this.fileName.split('.');
      return parts.length > 1 ? `.${parts[parts.length - 1].toLowerCase()}` : '';
    }
    
    // Extraire de l'URL si pas de fileName
    const urlParts = this.url.split('.');
    return urlParts.length > 1 ? `.${urlParts[urlParts.length - 1].toLowerCase()}` : '';
  }

  isRecentlyUploaded(minutesAgo: number = 5): boolean {
    const now = new Date();
    const diffInMinutes = (now.getTime() - this.uploadedAt.getTime()) / (1000 * 60);
    return diffInMinutes <= minutesAgo;
  }

  // Convertir vers ProfilePhoto pour utilisation après onboarding
  toProfilePhoto(): import('./UserProfile').ProfilePhoto {
    const { ProfilePhoto } = require('./UserProfile');
    return new ProfilePhoto(this.id, this.url, true, 0); // isPrimary=true, order=0
  }

  // Méthode de création statique
  static create(id: string, url: string, fileName?: string): OnboardingPhoto {
    return new OnboardingPhoto(id, url, fileName);
  }

  // Créer depuis un upload de fichier (simulation)
  static fromUpload(file: { id: string; url: string; name: string }): OnboardingPhoto {
    return new OnboardingPhoto(file.id, file.url, file.name);
  }

  // Convertir vers format API
  toApiFormat() {
    return {
      id: this.id,
      url: this.url,
      fileName: this.fileName,
      uploadedAt: this.uploadedAt.toISOString(),
      isPrimary: true, // Toujours principale pour l'onboarding
      order: 0
    };
  }

  equals(other: OnboardingPhoto): boolean {
    return this.id === other.id && this.url === other.url;
  }
} 
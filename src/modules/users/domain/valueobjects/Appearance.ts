export enum Ethnicity {
  CAUCASIAN = 'caucasian',
  AFRICAN = 'african',
  ASIAN = 'asian',
  HISPANIC = 'hispanic',
  MIDDLE_EASTERN = 'middle_eastern',
  MIXED = 'mixed',
  OTHER = 'other',
  PREFER_NOT_TO_SAY = 'prefer_not_to_say'
}

// const hairColors = ['black', 'blonde', 'brown', 'red', 'gray', 'white'];
// const eyeColors = ['blue', 'brown', 'green', 'hazel', 'gray'];
// const bodyTypes = ['slim', 'average', 'athletic', 'curvy', 'petite'];
// const bodyColors = ['white', 'black', 'brown', 'yellow', 'mixed'];
// const religions = ['christianity', 'islam', 'judaism', 'hinduism', 'buddhism', 'atheism'];

export enum HairColor {
  BLACK = 'black',
  BLONDE = 'blonde',
  BROWN = 'brown',
  RED = 'red',
  GRAY = 'gray',
  WHITE = 'white'
}

export enum EyeColor {
  BLUE = 'blue',
  BROWN = 'brown',
  GREEN = 'green',
  HAZEL = 'hazel',
  GRAY = 'gray'
}

export enum BodyType {
  SLIM = 'slim',
  AVERAGE = 'average',
  ATHLETIC = 'athletic',
  CURVY = 'curvy',
  PETITE = 'petite'
}

export enum BodyColor {
  WHITE = 'white',
  BLONDE = 'blonde',
  BLACK = 'black',
  BROWN = 'brown',
  YELLOW = 'yellow',
  MIXED = 'mixed'
}

export enum Religion {
  CHRISTIANITY = 'christianity',
  ISLAM = 'islam',
  JUDAISM = 'judaism',
  HINDUISM = 'hinduism',
  BUDDHISM = 'buddhism',
  ATHEISM = 'atheism'
}

export class Appearance {
  constructor(
    public readonly ethnicity: Ethnicity,
    public readonly height: number, // en centimètres
    public readonly hairColor: HairColor,
    public readonly eyeColor: EyeColor,
    public readonly bodyType: BodyType,
    public readonly bodyColor: BodyColor,
    public readonly religion: Religion
  ) {
    this.validate();
  }

  private validate(): void {
    if (!Object.values(Ethnicity).includes(this.ethnicity)) {
      throw new Error('L\'ethnicité spécifiée n\'est pas valide');
    }

    if (!this.height || this.height < 120 || this.height > 250) {
      throw new Error('La taille doit être entre 120 et 250 cm');
    }

    if (!Number.isInteger(this.height)) {
      throw new Error('La taille doit être un nombre entier en centimètres');
    }

    // if (!Object.values(HairColor).includes(this.hairColor)) {
    //   throw new Error('La couleur de cheveux spécifiée n\'est pas valide');
    // }

    // if (!Object.values(EyeColor).includes(this.eyeColor)) {
    //   throw new Error('La couleur des yeux spécifiée n\'est pas valide');
    // }

    // if (!Object.values(BodyType).includes(this.bodyType)) {
    //   throw new Error('Le type de corps spécifié n\'est pas valide');
    // }

    // if (!Object.values(BodyColor).includes(this.bodyColor)) {
    //   throw new Error('La couleur de peau spécifiée n\'est pas valide');
    // }

    // if (!Object.values(Religion).includes(this.religion)) {
    //   throw new Error('La religion spécifiée n\'est pas valide');
    // }
  }

  getHeightDisplay(): string {
    const meters = Math.floor(this.height / 100);
    const centimeters = this.height % 100;
    return `${meters}m${centimeters.toString().padStart(2, '0')}`;
  }

  getEthnicityDisplay(): string {
    const ethnicityLabels: Record<Ethnicity, string> = {
      [Ethnicity.CAUCASIAN]: 'Caucasien',
      [Ethnicity.AFRICAN]: 'Africain',
      [Ethnicity.ASIAN]: 'Asiatique',
      [Ethnicity.HISPANIC]: 'Hispanique',
      [Ethnicity.MIDDLE_EASTERN]: 'Moyen-Oriental',
      [Ethnicity.MIXED]: 'Métissé',
      [Ethnicity.OTHER]: 'Autre',
      [Ethnicity.PREFER_NOT_TO_SAY]: 'Préfère ne pas dire'
    };

    return ethnicityLabels[this.ethnicity];
  }

  getHairColorDisplay(): string {
    const hairColorLabels: Record<HairColor, string> = {
      [HairColor.BLACK]: 'Noir',
      [HairColor.BLONDE]: 'Blond',
      [HairColor.BROWN]: 'Brun',
      [HairColor.RED]: 'Roux',
      [HairColor.GRAY]: 'Gris',
      [HairColor.WHITE]: 'Blanc'
    };

    return hairColorLabels[this.hairColor];
  }

  getEyeColorDisplay(): string {
    const eyeColorLabels: Record<EyeColor, string> = {
      [EyeColor.BLUE]: 'Bleu',
      [EyeColor.BROWN]: 'Marron',
      [EyeColor.GREEN]: 'Vert',
      [EyeColor.HAZEL]: 'Miel',
      [EyeColor.GRAY]: 'Gris'
    };

    return eyeColorLabels[this.eyeColor];
  }

  getBodyTypeDisplay(): string {
    const bodyTypeLabels: Record<BodyType, string> = {
      [BodyType.SLIM]: 'Maigre',
      [BodyType.AVERAGE]: 'Moyen',
      [BodyType.ATHLETIC]: 'Athlétique',
      [BodyType.CURVY]: 'Carrure',
      [BodyType.PETITE]: 'Petite'
    };

    return bodyTypeLabels[this.bodyType];
  }

  getBodyColorDisplay(): string {
    const bodyColorLabels: Record<BodyColor, string> = {
      [BodyColor.WHITE]: 'Blanc',
      [BodyColor.BLONDE]: 'Blond',
      [BodyColor.BLACK]: 'Noir',
      [BodyColor.BROWN]: 'Brun',
      [BodyColor.YELLOW]: 'Jaune',
      [BodyColor.MIXED]: 'Métissé'
    };

    return bodyColorLabels[this.bodyColor];
  }

  getReligionDisplay(): string {
    const religionLabels: Record<Religion, string> = {
      [Religion.CHRISTIANITY]: 'Christianisme',
      [Religion.ISLAM]: 'Islam',
      [Religion.JUDAISM]: 'Judaïsme',
      [Religion.HINDUISM]: 'Hindouisme',
      [Religion.BUDDHISM]: 'Bouddhisme',
      [Religion.ATHEISM]: 'Athéisme'
    };

    return religionLabels[this.religion];
  }

  static getEthnicityOptions(): Array<{ value: Ethnicity; label: string }> {
    return [
      { value: Ethnicity.CAUCASIAN, label: 'Caucasian' },
      { value: Ethnicity.AFRICAN, label: 'African' },
      { value: Ethnicity.ASIAN, label: 'Asian' },
      { value: Ethnicity.HISPANIC, label: 'Hispanic' },
      { value: Ethnicity.MIDDLE_EASTERN, label: 'Middle Eastern' },
      { value: Ethnicity.MIXED, label: 'Mixed' },
      { value: Ethnicity.OTHER, label: 'Other' },
      { value: Ethnicity.PREFER_NOT_TO_SAY, label: 'Prefer not to say' }
    ];
  }

  static getHairColorOptions(): Array<{ value: HairColor; label: string }> {
    return [
      { value: HairColor.BLACK, label: 'Black' },
      { value: HairColor.BLONDE, label: 'Blonde' },
      { value: HairColor.BROWN, label: 'Brown' },
      { value: HairColor.RED, label: 'Red' },
      { value: HairColor.GRAY, label: 'Gray' },
      { value: HairColor.WHITE, label: 'White' }
    ];
  }

  static getEyeColorOptions(): Array<{ value: EyeColor; label: string }> {
    return [
      { value: EyeColor.BLUE, label: 'Blue' },
      { value: EyeColor.BROWN, label: 'Brown' },
      { value: EyeColor.GREEN, label: 'Green' },
      { value: EyeColor.HAZEL, label: 'Hazel' },
      { value: EyeColor.GRAY, label: 'Gray' }
    ];
  }

  static getBodyTypeOptions(): Array<{ value: BodyType; label: string }> {
    return [
      { value: BodyType.SLIM, label: 'Slim' },
      { value: BodyType.AVERAGE, label: 'Average' },
      { value: BodyType.ATHLETIC, label: 'Athletic' },
      { value: BodyType.CURVY, label: 'Curvy' },
      { value: BodyType.PETITE, label: 'Small' }
    ];
  }

  static getBodyColorOptions(): Array<{ value: BodyColor; label: string }> {
    return [
      { value: BodyColor.WHITE, label: 'White' },
      { value: BodyColor.BLONDE, label: 'Blonde' },
      { value: BodyColor.BLACK, label: 'Black' },
      { value: BodyColor.BROWN, label: 'Brown' },
      { value: BodyColor.YELLOW, label: 'Yellow' },
      { value: BodyColor.MIXED, label: 'Mixed' }
    ];
  }

  static getReligionOptions(): Array<{ value: Religion; label: string }> {
    return [
      { value: Religion.CHRISTIANITY, label: 'Christianity' },
      { value: Religion.ISLAM, label: 'Islam' },
      { value: Religion.JUDAISM, label: 'Judaism' },
      { value: Religion.HINDUISM, label: 'Hinduism' },
      { value: Religion.BUDDHISM, label: 'Buddhism' },
      { value: Religion.ATHEISM, label: 'Atheism' }
    ];
  }

  // Convertir vers un format simple pour l'API
  toApiFormat() {
    return {
      ethnicity: this.ethnicity,
      height: this.height
    };
  }
} 
// Liste des pays supportés par l'application
export const SUPPORTED_COUNTRIES = [
  "FR",
  "US",
  "GB",
  "DE",
  "ES",
  "IT",
  "CA",
  "AU",
  "JP",
  "BR",
  "MX",
  "AR",
  "CL",
  "CO",
  "PE",
  "VE",
  "EC",
  "BO",
  "PY",
  "UY",
  "BE",
  "NL",
  "AT",
  "CH",
  "PT",
  "GR",
  "PL",
  "CZ",
  "HU",
  "RO",
  "BG",
  "HR",
  "SI",
  "SK",
  "LT",
  "LV",
  "EE",
  "FI",
  "SE",
  "NO",
  "DK",
  "IS",
  "IE",
  "MT",
  "CY",
  "LU",
  "AD",
  "MC",
  "SM",
  "VA",
  'CN',
  'KR',
  'VN',
  'TH',
  'ZA',
  'IN',
  'SG',
  'MY',
  'ID',
  'PH',
  'NG',
  'EG',
  'SA',
  'AE',
  'IL',
  'TR',
  'RU',
  'UA',
  'KZ',
  'NZ'
] as const;

export type SupportedCountryCode = (typeof SUPPORTED_COUNTRIES)[number];

// Mapping des codes pays vers les noms français
export const COUNTRY_NAMES: Record<SupportedCountryCode, string> = {
  'FR': 'France',
  'US': 'United States',
  'GB': 'United Kingdom',
  'DE': 'Germany',
  'ES': 'Spain',
  'IT': 'Italy',
  'CA': 'Canada',
  'AU': 'Australia',
  'JP': 'Japan',
  'BR': 'Brazil',
  'MX': 'Mexico',
  'AR': 'Argentina',
  'CL': 'Chile',
  'CO': 'Colombia',
  'PE': 'Peru',
  'VE': 'Venezuela',
  'EC': 'Ecuador',
  'BO': 'Bolivia',
  'PY': 'Paraguay',
  'UY': 'Uruguay',
  'BE': 'Belgium',
  'NL': 'Netherlands',
  'AT': 'Austria',
  'CH': 'Switzerland',
  'PT': 'Portugal',
  'GR': 'Greece',
  'PL': 'Poland',
  'CZ': 'Czech Republic',
  'HU': 'Hungary',
  'RO': 'Romania',
  'BG': 'Bulgaria',
  'HR': 'Croatia',
  'SI': 'Slovenia',
  'SK': 'Slovakia',
  'LT': 'Lithuania',
  'LV': 'Latvia',
  'EE': 'Estonia',
  'FI': 'Finland',
  'SE': 'Sweden',
  'NO': 'Norway',
  'DK': 'Denmark',
  'IS': 'Iceland',
  'IE': 'Ireland',
  'MT': 'Malta',
  'CY': 'Cyprus',
  'LU': 'Luxembourg',
  'AD': 'Andorra',
  'MC': 'Monaco',
  'SM': 'San Marino',
  'VA': 'Vatican City',
  'CN': 'China',
  'KR': 'South Korea',
  'VN': 'Vietnam',
  'TH': 'Thailand',
  'ZA': 'South Africa',
  'IN': 'India',
  'SG': 'Singapore',
  'MY': 'Malaysia',
  'ID': 'Indonesia',
  'PH': 'Philippines',
  'NG': 'Nigeria',
  'EG': 'Egypt',
  'SA': 'Saudi Arabia',
  'AE': 'United Arab Emirates',
  'IL': 'Israel',
  'TR': 'Turkey',
  'RU': 'Russia',
  'UA': 'Ukraine',
  'KZ': 'Kazakhstan',
  'NZ': 'New Zealand'
};

export class Country {
  constructor(public readonly code: SupportedCountryCode) {
    this.validate();
  }

  private validate(): void {
    if (!this.code?.trim()) {
      throw new Error("Le code pays est requis");
    }

    if (!SUPPORTED_COUNTRIES.includes(this.code as SupportedCountryCode)) {
      throw new Error(`Le pays "${this.code}" n'est pas supporté actuellement`);
    }
  }

  getName(): string {
    return COUNTRY_NAMES[this.code];
  }

  getCode(): string {
    return this.code;
  }

  // Vérifier si c'est un pays européen
  isEuropean(): boolean {
    const europeanCountries: SupportedCountryCode[] = [
      "FR",
      "DE",
      "ES",
      "IT",
      "BE",
      "NL",
      "AT",
      "CH",
      "PT",
      "GR",
      "PL",
      "CZ",
      "HU",
      "RO",
      "BG",
      "HR",
      "SI",
      "SK",
      "LT",
      "LV",
      "EE",
      "FI",
      "SE",
      "NO",
      "DK",
      "IS",
      "IE",
      "MT",
      "CY",
      "LU",
      "AD",
      "MC",
      "SM",
      "VA",
    ];
    return europeanCountries.includes(this.code);
  }

  // Vérifier si c'est un pays francophone
  isFrancophone(): boolean {
    const francophones: SupportedCountryCode[] = [
      "FR",
      "BE",
      "CH",
      "CA",
      "MC",
      "LU",
    ];
    return francophones.includes(this.code);
  }

  equals(other: Country): boolean {
    return this.code === other.code;
  }

  toString(): string {
    return this.getName();
  }

  // Méthode statique pour obtenir tous les pays supportés
  static getAllSupported(): Array<{
    code: SupportedCountryCode;
    name: string;
  }> {
    return SUPPORTED_COUNTRIES.map((code) => ({
      code,
      name: COUNTRY_NAMES[code],
    }));
  }

  // Créer un pays à partir d'un nom
  static fromName(name: string): Country | null {
    const entry = Object.entries(COUNTRY_NAMES).find(
      ([_, countryName]) => countryName.toLowerCase() === name.toLowerCase()
    );

    if (entry) {
      return new Country(entry[0] as SupportedCountryCode);
    }

    return null;
  }
}

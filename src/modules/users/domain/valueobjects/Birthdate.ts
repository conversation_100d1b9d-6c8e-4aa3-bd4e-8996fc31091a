export class Birthdate {
  constructor(public readonly value: Date) {
    this.validate();
  }

  private validate(): void {
    if (!this.value || !(this.value instanceof Date)) {
      throw new Error('La date de naissance est requise');
    }

    if (isNaN(this.value.getTime())) {
      throw new Error('La date de naissance doit être une date valide');
    }

    // Règle métier : pas de date future
    const today = new Date();
    if (this.value > today) {
      throw new Error('La date de naissance ne peut pas être dans le futur');
    }

    // Règle métier : pas de date trop ancienne (125 ans max)
    const maxAge = new Date();
    maxAge.setFullYear(maxAge.getFullYear() - 125);
    if (this.value < maxAge) {
      throw new Error('La date de naissance semble trop ancienne');
    }

    const age = this.getAge();
    
    // Règle métier : âge minimum pour un site de rencontre
    if (age < 18) {
      throw new Error('Vous devez avoir au moins 18 ans pour vous inscrire');
    }

    // Règle métier : âge maximum raisonnable
    if (age > 100) {
      throw new Error('Veuillez vérifier votre date de naissance');
    }
  }

  getAge(): number {
    const today = new Date();
    let age = today.getFullYear() - this.value.getFullYear();
    const monthDiff = today.getMonth() - this.value.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < this.value.getDate())) {
      age--;
    }
    
    return age;
  }

  getDisplayAge(): string {
    return `${this.getAge()} ans`;
  }

  // Méthode pour obtenir l'âge à une date donnée
  getAgeAt(date: Date): number {
    let age = date.getFullYear() - this.value.getFullYear();
    const monthDiff = date.getMonth() - this.value.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && date.getDate() < this.value.getDate())) {
      age--;
    }
    
    return age;
  }

  // Vérifier si c'est l'anniversaire aujourd'hui
  isBirthdayToday(): boolean {
    const today = new Date();
    return this.value.getMonth() === today.getMonth() && 
           this.value.getDate() === today.getDate();
  }

  // Obtenir le prochain anniversaire
  getNextBirthday(): Date {
    const today = new Date();
    const nextBirthday = new Date(today.getFullYear(), this.value.getMonth(), this.value.getDate());
    
    if (nextBirthday < today) {
      nextBirthday.setFullYear(today.getFullYear() + 1);
    }
    
    return nextBirthday;
  }

  equals(other: Birthdate): boolean {
    return this.value.getTime() === other.value.getTime();
  }
} 
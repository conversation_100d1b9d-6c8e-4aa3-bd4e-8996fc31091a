import { PersonalInfo } from './PersonalInfo';
// DatingPreferences supprimé - plus utilisé dans le nouveau onboarding

export class ProfilePhoto {
  constructor(
    public readonly id: string,
    public readonly url: string,
    public readonly isPrimary: boolean,
    public readonly order: number
  ) {}
}

export class UserProfile {
  constructor(
    public readonly personalInfo: PersonalInfo,
    public readonly photos: ProfilePhoto[],
    // DatingPreferences supprimé de la nouvelle structure
    public readonly interests: string[],
    public readonly bio: string,
    public readonly isProfileComplete: boolean = false
  ) {
    this.validate();
  }

  private validate(): void {
    if (!this.personalInfo) {
      throw new Error('Les informations personnelles sont requises');
    }

    if (!this.photos || this.photos.length === 0) {
      throw new Error('Au moins une photo est requise');
    }

    const hasPrimaryPhoto = this.photos.some(photo => photo.isPrimary);
    if (!hasPrimaryPhoto) {
      throw new Error('Une photo principale doit être définie');
    }

    if (!this.interests || this.interests.length < 3) {
      throw new Error('Au moins 3 centres d\'intérêt sont requis');
    }

    if (!this.bio || this.bio.trim().length < 50) {
      throw new Error('La description doit contenir au moins 50 caractères');
    }
  }

  getPrimaryPhoto(): ProfilePhoto | undefined {
    return this.photos.find(photo => photo.isPrimary);
  }

  getSecondaryPhotos(): ProfilePhoto[] {
    return this.photos.filter(photo => !photo.isPrimary);
  }

  // Note: Cette classe est maintenant obsolète avec la nouvelle architecture
  // Elle est conservée pour la compatibilité mais devrait être supprimée à terme
  static createDeprecated(data: {
    personalInfo: PersonalInfo;
    photos: ProfilePhoto[];
    interests: string[];
    bio: string;
  }): UserProfile {
    console.warn('UserProfile.createDeprecated: Cette méthode est obsolète. Utilisez la nouvelle structure OnboardingData.');
    
    return new UserProfile(
      data.personalInfo,
      data.photos,
      data.interests,
      data.bio,
      true // Considéré comme complet si créé via cette méthode
    );
  }
} 
import { useState, useCallback } from 'react';
import { UserLikeApiService } from '../../infrastructure/api/UserLikeApiService';

export interface LikedUser {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  isOnline: boolean;
  profilePhoto?: string;
  firstName?: string;
  lastName?: string;
  birthdate?: Date;
  gender?: string;
  country?: string;
  ethnicity?: string;
  height?: number;
}

export const useUserLikes = () => {
  const [likedUsers, setLikedUsers] = useState<LikedUser[]>([]);
  const [dislikedUsers, setDislikedUsers] = useState<LikedUser[]>([]);
  const [isLiking, setIsLiking] = useState<string | null>(null);
  const [isDisliking, setIsDisliking] = useState<string | null>(null);
  const [isLoadingLiked, setIsLoadingLiked] = useState(false);
  const [isLoadingDisliked, setIsLoadingDisliked] = useState(false);
  const [errorLiked, setErrorLiked] = useState<string | null>(null);
  const [errorDisliked, setErrorDisliked] = useState<string | null>(null);
  const [loadingUsers, setLoadingUsers] = useState<Set<string>>(new Set());

  const likeUser = useCallback(async (targetUserId: string): Promise<boolean> => {
    setLoadingUsers(prev => new Set(prev).add(targetUserId));
    setIsLiking(targetUserId);
    setErrorLiked(null);

    try {
      const response = await UserLikeApiService.likeUser(targetUserId);
      
      if (response.success) {
        // Optionnel: mettre à jour la liste des utilisateurs likés
        return true;
      } else {
        setErrorLiked(response.message);
        return false;
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Erreur lors du like';
      setErrorLiked(message);
      return false;
    } finally {
      setIsLiking(null);
      setLoadingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(targetUserId);
        return newSet;
      });
    }
  }, []);

  const dislikeUser = useCallback(async (targetUserId: string): Promise<boolean> => {
    setLoadingUsers(prev => new Set(prev).add(targetUserId));
    setIsDisliking(targetUserId);
    setErrorDisliked(null);

    try {
      const response = await UserLikeApiService.dislikeUser(targetUserId);
      
      if (response.success) {
        // Optionnel: mettre à jour la liste des utilisateurs dislikés
        return true;
      } else {
        setErrorDisliked(response.message);
        return false;
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Erreur lors du dislike';
      setErrorDisliked(message);
      return false;
    } finally {
      setIsDisliking(null);
      setLoadingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(targetUserId);
        return newSet;
      });
    }
  }, []);

  const removeUserInteraction = useCallback(async (targetUserId: string) => {
    setLoadingUsers(prev => new Set(prev).add(targetUserId));
    try {
      await UserLikeApiService.removeUserInteraction(targetUserId);
      // Supprimer l'utilisateur des listes locales
      setLikedUsers(prev => prev.filter(user => user.id !== targetUserId));
      setDislikedUsers(prev => prev.filter(user => user.id !== targetUserId));
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'interaction:', error);
      throw error;
    } finally {
      setLoadingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(targetUserId);
        return newSet;
      });
    }
  }, []);

  const loadLikedUsers = useCallback(async (limit: number = 10, offset: number = 0): Promise<void> => {
    setIsLoadingLiked(true);
    setErrorLiked(null);

    try {
      const response = await UserLikeApiService.getLikedUsers(limit, offset);
      
      if (response.success) {
        if (offset === 0) {
          setLikedUsers(response.data);
        } else {
          setLikedUsers(prev => [...prev, ...response.data]);
        }
      } else {
        setErrorLiked('Erreur lors du chargement des utilisateurs likés');
      }
    } catch (error: any) {
      // 404 signifie simplement qu'aucun utilisateur liké n'a été trouvé
      if (error?.response?.status === 404) {
        if (offset === 0) {
          setLikedUsers([]);
        }
        // Pas d'erreur à afficher pour un 404
      } else {
        const message = error instanceof Error ? error.message : 'Erreur lors du chargement';
        setErrorLiked(message);
      }
    } finally {
      setIsLoadingLiked(false);
    }
  }, []);

  const loadDislikedUsers = useCallback(async (limit: number = 10, offset: number = 0): Promise<void> => {
    setIsLoadingDisliked(true);
    setErrorDisliked(null);

    try {
      const response = await UserLikeApiService.getDislikedUsers(limit, offset);
      
      if (response.success) {
        if (offset === 0) {
          setDislikedUsers(response.data);
        } else {
          setDislikedUsers(prev => [...prev, ...response.data]);
        }
      } else {
        setErrorDisliked('Erreur lors du chargement des utilisateurs dislikés');
      }
    } catch (error: any) {
      // 404 signifie simplement qu'aucun utilisateur disliké n'a été trouvé
      if (error?.response?.status === 404) {
        if (offset === 0) {
          setDislikedUsers([]);
        }
        // Pas d'erreur à afficher pour un 404
      } else {
        const message = error instanceof Error ? error.message : 'Erreur lors du chargement';
        setErrorDisliked(message);
      }
    } finally {
      setIsLoadingDisliked(false);
    }
  }, []);

  return {
    // Données
    likedUsers,
    dislikedUsers,
    
    // États de chargement
    isLiking,
    isDisliking,
    isLoadingLiked,
    isLoadingDisliked,
    
    // Erreurs
    errorLiked,
    errorDisliked,
    
    // Actions
    likeUser,
    dislikeUser,
    removeUserInteraction,
    loadLikedUsers,
    loadDislikedUsers,
    
    // État de chargement des utilisateurs
    loadingUsers,
  };
}; 
import { useState, useCallback } from 'react';
import { User } from '../../domain/entities/User';
import { userApiRepository } from '../../infrastructure/api/UserApiRepository';

export interface UseUserSuggestionsResult {
  users: User[];
  isLoading: boolean;
  error: string | null;
  loadSuggestions: (limit?: number) => Promise<void>;
  refreshSuggestions: () => Promise<void>;
}

export const useUserSuggestions = (initialLimit: number = 50): UseUserSuggestionsResult => {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentLimit, setCurrentLimit] = useState(initialLimit);

  const loadSuggestions = useCallback(async (limit?: number) => {
    const requestLimit = limit || currentLimit;
    setCurrentLimit(requestLimit);
    setIsLoading(true);
    setError(null);

    try {
      const suggestions = await userApiRepository.getUserSuggestions(requestLimit);
      setUsers(suggestions);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load user suggestions');
      setUsers([]);
    } finally {
      setIsLoading(false);
    }
  }, [currentLimit]);

  const refreshSuggestions = useCallback(async () => {
    await loadSuggestions(currentLimit);
  }, [loadSuggestions, currentLimit]);

  return {
    users,
    isLoading,
    error,
    loadSuggestions,
    refreshSuggestions,
  };
}; 
import { useState, useCallback, useMemo } from 'react';
import { User } from '../../domain/entities/User';
import { SearchUsersUseCase } from '../../application/usecases/SearchUsersUseCase';
import { userApiRepository } from '../../infrastructure/api/UserApiRepository';

export interface UseUserSearchResult {
  users: User[];
  isLoading: boolean;
  error: string | null;
  searchUsers: (searchTerm: string) => Promise<void>;
  clearResults: () => void;
  searchUsersUseCase: SearchUsersUseCase;
}

export const useUserSearch = (): UseUserSearchResult => {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize use case with singleton repository - stable across renders
  const searchUsersUseCase = useMemo(() => {
    return new SearchUsersUseCase(userApiRepository);
  }, []);

  const searchUsers = useCallback(async (searchTerm: string) => {
    if (!searchTerm || searchTerm.trim().length < 2) {
      setUsers([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await searchUsersUseCase.execute({ searchTerm });
      setUsers(response.users);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search users');
      setUsers([]);
    } finally {
      setIsLoading(false);
    }
  }, [searchUsersUseCase]);

  const clearResults = useCallback(() => {
    setUsers([]);
    setError(null);
  }, []);

  return {
    users,
    isLoading,
    error,
    searchUsers,
    clearResults,
    searchUsersUseCase
  };
}; 
import { useState, useCallback } from 'react';
import { User } from '../../domain/entities/User';
import { SearchUsersParams } from '../../domain/repositories/UserRepository';
import { userApiRepository } from '../../infrastructure/api/UserApiRepository';

export interface UseUserSearchWithFiltersResult {
  users: User[];
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  searchUsers: (params: SearchUsersParams, isLoadMore?: boolean) => Promise<void>;
  loadMore: () => Promise<void>;
  reset: () => void;
}

export const useUserSearchWithFilters = (): UseUserSearchWithFiltersResult => {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [currentParams, setCurrentParams] = useState<SearchUsersParams | null>(null);
  const [offset, setOffset] = useState(0);

  const searchUsers = useCallback(async (params: SearchUsersParams, isLoadMore = false) => {
    if (isLoadMore) {
      setIsLoadingMore(true);
    } else {
      setIsLoading(true);
      setOffset(0);
    }
    
    setError(null);

    try {
      const currentOffset = isLoadMore ? offset : 0;
      const searchParams = {
        ...params,
        searchTerm: params.searchTerm || '',
        limit: params.limit || 5,
        offset: currentOffset
      };

      const result = await userApiRepository.searchUsersWithFilters(searchParams);

      if (isLoadMore) {
        setUsers(prev => [...prev, ...result.users]);
      } else {
        setUsers(result.users);
        setCurrentParams(params);
      }
      
      setHasMore(result.hasMore);
      setOffset(currentOffset + result.users.length);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la recherche';
      setError(errorMessage);
      console.error('Failed to search users with filters:', err);
      
      if (!isLoadMore) {
        setUsers([]);
        setHasMore(false);
      }
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, [offset]);

  const loadMore = useCallback(async () => {
    if (hasMore && !isLoadingMore && currentParams) {
      await searchUsers(currentParams, true);
    }
  }, [searchUsers, hasMore, isLoadingMore, currentParams]);

  const reset = useCallback(() => {
    setUsers([]);
    setIsLoading(false);
    setIsLoadingMore(false);
    setError(null);
    setHasMore(false);
    setCurrentParams(null);
    setOffset(0);
  }, []);

  return {
    users,
    isLoading,
    isLoadingMore,
    error,
    hasMore,
    searchUsers,
    loadMore,
    reset,
  };
}; 
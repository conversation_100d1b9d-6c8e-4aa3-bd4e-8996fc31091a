import { useState, useEffect } from 'react';
import { OnboardingData } from '../../application/usecases/CompleteOnboardingUseCase';
import { PersonalInfo } from '../../domain/valueobjects/PersonalInfo';
import { Appearance } from '../../domain/valueobjects/Appearance';
import { OnboardingPhoto } from '../../domain/valueobjects/OnboardingPhoto';
import { OnboardingValidationService } from '../../domain/services/OnboardingValidationService';
import { userApiRepository } from '../../infrastructure/api/UserApiRepository';
import { useAuth } from '../../../auth/presentation/hooks/useAuth';

export interface OnboardingStep {
  step: number;
  isCompleted: boolean;
  isValid: boolean;
  title: string;
}

export function useOnboarding() {
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    photo: undefined,
    personalInfo: undefined,
    appearance: undefined
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [isOnboardingCompleteCache, setIsOnboardingCompleteCache] = useState<boolean | null>(null);
  
  const { user, isAuthenticated } = useAuth();

  // Charger les données d'onboarding existantes au montage
  useEffect(() => {
    if (isAuthenticated && user) {
      loadOnboardingData();
    }
  }, [isAuthenticated, user]);

  const loadOnboardingData = async () => {
    if (!user) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const data = await userApiRepository.getUserOnboardingData();
      if (data) {
        setOnboardingData(data);
      }
    } catch (error) {
      console.error('Failed to load onboarding data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load onboarding data');
    } finally {
      setLoading(false);
    }
  };

  const updateStep = (step: number, data: any) => {
    const updatedData = { ...onboardingData };
    
    switch (step) {
      case 1:
        updatedData.photo = data as OnboardingPhoto;
        break;
      case 2:
        updatedData.personalInfo = data as PersonalInfo;
        break;
      case 3:
        updatedData.appearance = data as Appearance;
        break;
    }

    setOnboardingData(updatedData);
  };

  const saveOnboarding = async (): Promise<{ success: boolean; profileCompletionPercentage: number }> => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setSaving(true);
    setError(null);

    try {
      const result = await userApiRepository.completeOnboarding(onboardingData);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save onboarding';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleStepComplete = async (step: number, data: any): Promise<void> => {
    // Mettre à jour les données localement
    updateStep(step, data);
    
    // Sauvegarder automatiquement
    const updatedData = { ...onboardingData };
    switch (step) {
      case 1:
        updatedData.photo = data as OnboardingPhoto;
        break;
      case 2:
        updatedData.personalInfo = data as PersonalInfo;
        break;
      case 3:
        updatedData.appearance = data as Appearance;
        break;
    }

    try {
      await userApiRepository.completeOnboarding(updatedData);
    } catch (error) {
      console.error('Failed to auto-save onboarding:', error);
      // Ne pas bloquer l'utilisateur, mais afficher une erreur
      setError('Sauvegarde automatique échouée. Veuillez réessayer.');
    }
  };

  const getStepValidation = (): OnboardingStep[] => {
    return [
      {
        step: 1,
        isCompleted: !!onboardingData.photo,
        isValid: !!onboardingData.photo,
        title: 'Photo de profil'
      },
      {
        step: 2,
        isCompleted: !!onboardingData.personalInfo,
        isValid: !!onboardingData.personalInfo,
        title: 'Informations personnelles'
      },
      {
        step: 3,
        isCompleted: !!onboardingData.appearance,
        isValid: !!onboardingData.appearance,
        title: 'Apparence'
      }
    ];
  };

  // Déléguer au service de domaine
  const isOnboardingComplete = (): boolean => {
    const result = OnboardingValidationService.isOnboardingComplete(onboardingData);
    
    // Si on a détecté que l'onboarding est complet une fois, on le garde en mémoire
    if (result && isOnboardingCompleteCache !== true) {
      setIsOnboardingCompleteCache(true);
    }
    
    // Utiliser le cache si disponible et que les données sont vides (réinitialisation temporaire)
    const hasAnyData = onboardingData.photo || onboardingData.personalInfo || onboardingData.appearance;
    if (isOnboardingCompleteCache === true && !hasAnyData) {
      return true;
    }
    
    return result;
  };

  // Effet pour détecter quand l'onboarding devient complet
  useEffect(() => {
    if (onboardingData.photo && onboardingData.personalInfo && onboardingData.appearance) {
      if (isOnboardingCompleteCache !== true) {
        setIsOnboardingCompleteCache(true);
      }
    }
  }, [onboardingData, isOnboardingCompleteCache]);

  // Déléguer au service de domaine
  const getCompletionPercentage = (): number => {
    return OnboardingValidationService.calculateCompletionScore(onboardingData);
  };

  // Obtenir la prochaine étape à compléter
  const getNextStep = (): number | null => {
    return OnboardingValidationService.getNextStep(onboardingData);
  };

  // Nouvelle méthode pour obtenir l'étape actuelle où l'utilisateur doit reprendre
  const getCurrentStep = (): number => {
    const nextStep = getNextStep();
    return nextStep || 3; // Si onboarding complet, retourner la dernière étape, sinon la prochaine étape
  };

  // Méthode pour déterminer si l'utilisateur doit être redirigé vers l'onboarding
  const shouldRedirectToOnboarding = (): boolean => {
    return !isOnboardingComplete() && isAuthenticated;
  };

  // Nouvelle méthode pour obtenir le progrès détaillé
  const getProgress = () => {
    const steps = getStepValidation();
    const completedSteps = steps.filter(step => step.isCompleted).length;
    const nextStep = getNextStep();

    return {
      completedSteps,
      totalSteps: 3,
      percentage: getCompletionPercentage(),
      nextStep,
      isComplete: isOnboardingComplete()
    };
  };

  // Réinitialiser les données d'onboarding
  const resetOnboarding = () => {
    setOnboardingData({
      photo: undefined,
      personalInfo: undefined,
      appearance: undefined
    });
    setError(null);
  };

  // Méthodes utilitaires pour l'accès aux étapes
  const canAccessStep = (step: number): boolean => {
    return OnboardingValidationService.canAccessStep(step, onboardingData);
  };

  const getStepsStatus = () => {
    return OnboardingValidationService.getStepsStatus(onboardingData);
  };

  const clearError = () => {
    setError(null);
  };

  return {
    onboardingData,
    updateStep,
    handleStepComplete,
    getStepValidation,
    isOnboardingComplete,
    getCompletionPercentage,
    getNextStep,
    getCurrentStep,
    shouldRedirectToOnboarding,
    getProgress,
    resetOnboarding,
    canAccessStep,
    getStepsStatus,
    saveOnboarding,
    loading,
    saving,
    error,
    clearError,
    isAuthenticated,
    user
  };
} 
import { useState } from 'react';
import { PhotoUploadService } from '../../domain/services/PhotoUploadService';
import { ApiPhotoUploadService } from '../../infrastructure/services/ApiPhotoUploadService';

export function usePhotoUpload(photoUploadService: PhotoUploadService = new ApiPhotoUploadService()) {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadPhoto = async (file: File) => {
    setIsUploading(true);
    setError(null);

    try {
      // Validation côté client
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        throw new Error('Format d\'image non supporté. Utilisez JPG, PNG ou WebP');
      }

      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        throw new Error('La photo ne doit pas dépasser 5MB');
      }

      // Upload via le service
      const result = await photoUploadService.uploadProfilePhoto(file);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur lors de l\'upload';
      setError(errorMessage);
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    uploadPhoto,
    isUploading,
    error,
    clearError
  };
} 
import React, { useState } from 'react';
import { OnboardingPhoto } from '../../../domain/valueobjects/OnboardingPhoto';
import { usePhotoUpload } from '../../hooks/usePhotoUpload';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  useThemeClasses
} from '../../../../shared/presentation';

interface PhotosStepProps {
  data: OnboardingPhoto | undefined;
  onComplete: (data: OnboardingPhoto) => void;
  onSkip: () => void;
}

export default function PhotosStep({ data, onComplete, onSkip }: PhotosStepProps) {
  const [photo, setPhoto] = useState<OnboardingPhoto | undefined>(data);
  const [errors, setErrors] = useState<string[]>([]);
  const themeClasses = useThemeClasses();
  
  const { uploadPhoto, isUploading, error: uploadError, clearError } = usePhotoUpload();

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    try {
      if (!photo) {
        setErrors(['Une photo de profil est requise pour continuer']);
        return;
      }
      
      onComplete(photo);
    } catch (error) {
      if (error instanceof Error) {
        setErrors([error.message]);
      }
    }
  };

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setErrors([]);
    clearError();

    try {
      // Upload via le hook qui utilise l'architecture clean
      const uploadResult = await uploadPhoto(file);
      
      const uploadedPhoto = OnboardingPhoto.fromUpload({
        id: uploadResult.id,
        url: uploadResult.url,
        name: file.name
      });

      setPhoto(uploadedPhoto);
    } catch (error) {
      if (error instanceof Error) {
        setErrors([error.message]);
      }
    }
  };

  const removePhoto = () => {
    setPhoto(undefined);
    setErrors([]);
    clearError();
  };

  // Combiner les erreurs locales et d'upload
  const allErrors = [...errors, ...(uploadError ? [uploadError] : [])];

  return (
    <div className="w-full max-w-lg mx-auto">
      <Card variant="elevated" className="backdrop-blur-md bg-white border-white/20 shadow-2xl shadow-black/10">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-xl font-bold">
            Add a Profile Photo
          </CardTitle>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="flex flex-col items-center space-y-4">
              {photo ? (
                // Photo uploadée
                <div className="relative group">
                  <div className="relative">
                    <img
                      src={photo.url}
                      alt="Photo de profil"
                      className={`w-40 h-40 object-cover rounded-full border-4 ${themeClasses.borderPrimary} shadow-lg`}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-40 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <button
                        type="button"
                        onClick={removePhoto}
                        className="bg-red-500 hover:bg-red-600 text-white rounded-full p-2 transition-colors"
                        title="Changer la photo"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  
                  <div className="text-center mt-3">
                    <p className="text-sm text-green-600 font-medium flex items-center justify-center gap-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Uploaded
                    </p>
                  </div>
                </div>
              ) : (
                // Zone d'upload
                <div className="relative">
                  <input
                    id="photo-upload"
                    type="file"
                    accept="image/jpeg,image/jpg,image/png,image/webp"
                    onChange={handlePhotoUpload}
                    disabled={isUploading}
                    className="hidden"
                  />
                  
                  <label
                    htmlFor="photo-upload"
                    className={`
                      group relative flex flex-col items-center justify-center
                      w-40 h-40 border-2 border-dashed rounded-full
                      cursor-pointer transition-all duration-300 overflow-hidden
                      ${isUploading 
                        ? 'opacity-50 cursor-not-allowed border-neutral-300 bg-neutral-50' 
                        : `border-neutral-300 hover:border-[var(--color-primary-400)] hover:bg-[var(--color-primary-50)] bg-neutral-50/50`
                      }
                    `}
                  >
                    {isUploading ? (
                      <div className="flex flex-col items-center justify-center">
                        <div className={`animate-spin rounded-full h-8 w-8 border-2 border-transparent border-t-[var(--color-primary-500)] mb-2`}></div>
                        <p className={`text-xs font-medium ${themeClasses.textMuted}`}>Upload...</p>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center">
                        {/* Icône + grande et bien centrée */}
                        <div className="relative mb-2">
                          <div className={`w-16 h-16 rounded-full bg-white border-2 border-[var(--color-primary-200)] flex items-center justify-center group-hover:border-[var(--color-primary-400)] group-hover:bg-[var(--color-primary-50)] transition-all duration-300 shadow-sm`}>
                            <svg className={`w-8 h-8 ${themeClasses.textPrimary} group-hover:scale-110 transition-transform duration-300`} fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
                            </svg>
                          </div>
                        </div>
                        
                        {/* Texte descriptif */}
                        <div className="text-center">
                          <p className={`text-sm font-semibold ${themeClasses.textNeutral} mb-1 group-hover:${themeClasses.textPrimary} transition-colors duration-300`}>
                            Add a Profile Photo
                          </p>
                          <p className={`text-xs ${themeClasses.textMuted} leading-tight`}>
                            JPG, PNG • Max 5MB
                          </p>
                        </div>
                      </div>
                    )}
                    
                    {/* Effet de survol subtil */}
                    <div className={`absolute inset-0 rounded-full bg-gradient-to-br from-[var(--color-primary-100)] to-[var(--color-primary-200)] opacity-0 group-hover:opacity-20 transition-opacity duration-300`}></div>
                  </label>
                </div>
              )}
            </div>

            {/* Affichage des erreurs */}
            {allErrors.length > 0 && (
              <div className="space-y-2">
                {allErrors.map((error, index) => (
                  <div key={index} className="flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200">
                    <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>{error}</span>
                  </div>
                ))}
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
              <Button 
                type="submit" 
                size="lg" 
                className="px-8"
                disabled={!photo || isUploading}
              >
                Next
              </Button>
              <Button type="button" variant="ghost" onClick={onSkip}>
                Skip
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
import React, { useState } from "react";
import {
  Gender,
  PersonalInfo,
} from "../../../domain/valueobjects/PersonalInfo";
import { Country } from "../../../domain/valueobjects/Country";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  Input,
  Button,
  Select,
  useThemeClasses,
} from "../../../../shared/presentation";
import { RelationType } from "@/modules/users/domain/valueobjects/RelationType";

interface PersonalInfoStepProps {
  data: PersonalInfo | undefined;
  onComplete: (data: PersonalInfo) => void;
  onSkip: () => void;
}

export default function PersonalInfoStep({
  data,
  onComplete,
  onSkip,
}: PersonalInfoStepProps) {
  const [formData, setFormData] = useState({
    firstName: data?.firstName?.toString() || "",
    lastName: data?.lastName?.toString() || "",
    birthdate: data?.birthdate
      ? data.birthdate.value.toISOString().split("T")[0]
      : "",
    gender: data?.gender || Gender.MALE,
    country: data?.country?.getCode() || "FR",
    relationType: data?.relationType || "single",
  });

  const [errors, setErrors] = useState<string[]>([]);
  const themeClasses = useThemeClasses();

  // Remplace la signature de handleInputChange :
  const handleInputChange = (field: string, value: string | string[]) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    if (errors.length > 0) {
      setErrors([]);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    try {
      const personalInfo = PersonalInfo.create(
        formData.firstName,
        formData.lastName,
        new Date(formData.birthdate),
        formData.gender as Gender,
        formData.country,
        formData.relationType.toString() as any
      );

      onComplete(personalInfo);
    } catch (error) {
      if (error instanceof Error) {
        setErrors([error.message]);
      }
    }
  };

  const countries = Country.getAllSupported();
// 'single', 'in_a_relationship', 'married', 'divorced', 'widowed'
  const relationsTypes = [
    { value: "single", label: "Single" },
    { value: "friends", label: "Friends" },
    { value: "in_a_relationship", label: "In a relationship" },
    { value: "married", label: "Married" },
    { value: "divorced", label: "Divorced" },
    { value: "widowed", label: "Widowed" },
  ];

  const getAge = () => {
    if (!formData.birthdate) return null;
    const today = new Date();
    const birthDate = new Date(formData.birthdate);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age;
  };

  return (
    <div className="w-full max-w-lg mx-auto">
      <Card
        variant="elevated"
        className="backdrop-blur-md bg-white/80 border-white/20 shadow-2xl shadow-black/10"
      >
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-xl font-bold">
            Personal Information
          </CardTitle>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="First Name"
                value={formData.firstName}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleInputChange("firstName", e.target.value)
                }
                placeholder="Your first name"
                required
              />

              <Input
                label="Last Name"
                value={formData.lastName}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleInputChange("lastName", e.target.value)
                }
                placeholder="Your last name"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Input
                  label="Birthdate"
                  type="date"
                  value={formData.birthdate}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange("birthdate", e.target.value)
                  }
                  max={
                    new Date(Date.now() - 18 * 365.25 * 24 * 60 * 60 * 1000)
                      .toISOString()
                      .split("T")[0]
                  }
                  required
                />
                {getAge() && (
                  <p className={`text-xs ${themeClasses.textMuted} mt-1`}>
                    {getAge()} years old
                  </p>
                )}
              </div>

              <div>
                <label
                  className={`block text-sm font-medium ${themeClasses.textNeutral} mb-2`}
                >
                  Genre
                </label>
                <Select
                  value={formData.gender}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                    handleInputChange("gender", e.target.value)
                  }
                >
                  <option value={Gender.MALE}>Male</option>
                  <option value={Gender.FEMALE}>Female</option>
                  <option value={Gender.NON_BINARY}>Non-binary</option>
                  <option value={Gender.OTHER}>Other</option>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label
                  className={`block text-sm font-medium ${themeClasses.textNeutral} mb-2`}
                >
                  Country
                </label>
                <Select
                  value={formData.country}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                    handleInputChange("country", e.target.value)
                  }
                >
                  {countries.map((country) => (
                    <option key={country.code} value={country.code}>
                      {country.name}
                    </option>
                  ))}
                </Select>
              </div>
              <div>
                <label
                  className={`block text-sm font-medium ${themeClasses.textNeutral} mb-2`}
                >
                  Relation Type
                </label>
                <Select
                  value={formData.relationType as string}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                    handleInputChange("relationType", e.target.value)
                  }
                >
                  {relationsTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </Select>
                
              </div>
            </div>

            {errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <svg
                    className="w-4 h-4 text-red-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <h4 className="text-sm font-medium text-red-800">Error</h4>
                </div>
                {errors.map((error, index) => (
                  <p key={index} className="text-sm text-red-600">
                    {error}
                  </p>
                ))}
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
              <Button
                type="submit"
                size="lg"
                className="px-8"
                disabled={
                  !formData.firstName ||
                  !formData.lastName ||
                  !formData.birthdate
                }
              >
                Next
              </Button>
              <Button type="button" variant="ghost" onClick={onSkip}>
                Skip
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

import React from 'react';
import { useThemeClasses } from '../../../../shared/presentation';

interface ProgressBarProps {
  currentStep: number;
  totalSteps: number;
}

export default function ProgressBar({ currentStep, totalSteps }: ProgressBarProps) {
  const themeClasses = useThemeClasses();
  
  const getStepName = (step: number): string => {
    const stepNames = {
      1: 'Profile picture',
      2: 'Personal information',
      3: 'Appearance'
    };
    return stepNames[step as keyof typeof stepNames] || `Étape ${step}`;
  };

  const getStepIcon = (step: number): React.ReactElement => {
    const icons = {
      1: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      2: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      3: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
        </svg>
      )
    };
    return icons[step as keyof typeof icons] || <span>•</span>;
  };

  const isStepCompleted = (step: number): boolean => {
    return step < currentStep;
  };

  const isStepCurrent = (step: number): boolean => {
    return step === currentStep;
  };

  const progressPercentage = ((currentStep - 1) / (totalSteps - 1)) * 100;

  return (
    <div className="w-full">
      {/* Barre de progression compacte */}
      <div className="relative mb-6">
        <div className="flex justify-between items-center">
          {Array.from({ length: totalSteps }, (_, index) => {
            const step = index + 1;
            const completed = isStepCompleted(step);
            const current = isStepCurrent(step);

            return (
              <div key={step} className="flex flex-col items-center relative z-10">
                {/* Cercle d'étape plus petit */}
                <div
                  className={`
                    w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold mb-2 transition-all duration-300 border-2
                    ${completed 
                      ? `bg-green-500 border-green-500 text-white shadow-md` 
                      : current 
                        ? `${themeClasses.bgPrimary} ${themeClasses.borderPrimary} text-white shadow-md ring-2 ring-[var(--color-primary-200)]` 
                        : 'bg-neutral-100 border-neutral-300 text-neutral-400'
                    }
                  `}
                >
                  {completed ? (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : getStepIcon(step)}
                </div>

                {/* Nom de l'étape plus compact */}
                <span
                  className={`
                    text-xs text-center leading-tight transition-colors duration-300 font-medium
                    ${current 
                      ? themeClasses.textPrimary
                      : completed 
                        ? 'text-green-600' 
                        : themeClasses.textMuted
                    }
                  `}
                >
                  {getStepName(step)}
                </span>
              </div>
            );
          })}
        </div>

        {/* Ligne de progression */}
        <div className="absolute top-5 left-5 right-5 h-0.5 bg-neutral-200 rounded-full -z-10">
          <div
            className={`h-full rounded-full transition-all duration-700 ${themeClasses.bgGradientPrimary}`}
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      {/* Indicateur textuel minimal */}
      <div className="text-center">
        <div className={`text-base font-semibold ${themeClasses.textNeutral}`}>
          {getStepName(currentStep)}
        </div>
      </div>
    </div>
  );
} 
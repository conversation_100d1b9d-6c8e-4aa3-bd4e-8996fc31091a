import React, { useState } from 'react';
import { Ethnicity, Appearance, HairColor, EyeColor, BodyType, BodyColor, Religion } from '../../../domain/valueobjects/Appearance';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Input,
  Button,
  Select,
  useThemeClasses
} from '../../../../shared/presentation';
import { useLocation } from 'react-router-dom';

interface AppearanceStepProps {
  data: Appearance | undefined;
  onComplete: (data: Appearance) => void;
  onSkip: () => void;
}

export default function AppearanceStep({ data, onComplete, onSkip }: AppearanceStepProps) {
  const [formData, setFormData] = useState({
    ethnicity: data?.ethnicity || Ethnicity.PREFER_NOT_TO_SAY,
    height: data?.height || 170,
    hairColor: data?.hairColor || 'black',
    eyeColor: data?.eyeColor || 'brown',
    bodyType: data?.bodyType || 'average',
    bodyColor: data?.bodyColor || 'white',
    religion: data?.religion || 'christianity'
  });

  const [errors, setErrors] = useState<string[]>([]);
  const themeClasses = useThemeClasses();

  const location = useLocation();
    const form = location.state;
    console.log("form app: ", form);

  
  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ 
      ...prev, 
      [field]: field === 'height' ? parseInt(value.toString()) || 0 : value 
    }));
    
    if (errors.length > 0) {
      setErrors([]);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    try {
      const appearance = new Appearance(
        formData.ethnicity as Ethnicity,
        formData.height,
        formData.hairColor as HairColor,
        formData.eyeColor as EyeColor,
        formData.bodyType as BodyType,
        formData.bodyColor as BodyColor,
        formData.religion as Religion
      );
      
      onComplete(appearance);
    } catch (error) {
      if (error instanceof Error) {
        setErrors([error.message]);
      }
    }
  };

  const formatHeight = (heightCm: number) => {
    if (!heightCm) return '';
    const meters = Math.floor(heightCm / 100);
    const centimeters = heightCm % 100;
    return `${meters}m${centimeters.toString().padStart(2, '0')}`;
  };

  return (
    <div className="w-full max-w-lg mx-auto">
      <Card variant="elevated" className="backdrop-blur-md bg-white/80 border-white/20 shadow-2xl shadow-black/10">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-xl font-bold">
            Dernière étape !
          </CardTitle>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className={`block text-sm font-medium ${themeClasses.textNeutral} mb-2`}>
                Ethnicity
              </label>
              <Select
                value={formData.ethnicity}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('ethnicity', e.target.value)}
              >
                {Appearance.getEthnicityOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>

            <div>
              <label className={`block text-sm font-medium ${themeClasses.textNeutral} mb-2`}>
                Height
              </label>
              <div className="flex items-center space-x-3">
                <div className="flex-1">
                  <Input
                    type="number"
                    min="120"
                    max="250"
                    value={formData.height}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('height', e.target.value)}
                    placeholder="170"
                  />
                </div>
                <div className={`text-sm font-medium ${themeClasses.textNeutral} whitespace-nowrap min-w-16`}>
                  {formData.height ? (
                    <span className={`${themeClasses.textPrimary} font-semibold`}>
                      {formatHeight(formData.height)}
                    </span>
                  ) : (
                    <span className="text-neutral-400">cm</span>
                  )}
                </div>
              </div>
            </div>

            <div>
              <label className={`block text-sm font-medium ${themeClasses.textNeutral} mb-2`}>
                Hair Color
              </label>
              <Select
                value={formData.hairColor}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('hairColor', e.target.value)}
              >
                {Appearance.getHairColorOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                  )
                )}
              </Select>
            </div>

            <div>
              <label className={`block text-sm font-medium ${themeClasses.textNeutral} mb-2`}>
                Eye Color
              </label>
              <Select
                value={formData.eyeColor}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('eyeColor', e.target.value)}
              >
                {Appearance.getEyeColorOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                  )
                )}
              </Select>
            </div>

            <div>
              <label className={`block text-sm font-medium ${themeClasses.textNeutral} mb-2`}>
                Body Type
              </label>
              <Select
                value={formData.bodyType}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('bodyType', e.target.value)}
              >
                {Appearance.getBodyTypeOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                  )
                )}
              </Select>
            </div>

            <div>
              <label className={`block text-sm font-medium ${themeClasses.textNeutral} mb-2`}>
                Body Color
              </label>
              <Select
                value={formData.bodyColor}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('bodyColor', e.target.value)}
              >
                {Appearance.getBodyColorOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                  )
                )}
              </Select>
            </div>

            <div>
              <label className={`block text-sm font-medium ${themeClasses.textNeutral} mb-2`}>
                Religion
              </label>
              <Select
                value={formData.religion}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('religion', e.target.value)}
              >
                {Appearance.getReligionOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                  )
                )}
              </Select>
            </div>


            {errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h4 className="text-sm font-medium text-red-800">Erreur</h4>
                </div>
                {errors.map((error, index) => (
                  <p key={index} className="text-sm text-red-600">{error}</p>
                ))}
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
              <Button 
                type="submit" 
                size="lg" 
                className="px-8"
                disabled={!formData.height || formData.height < 120 || formData.height > 250}
              >
                🎉 Finaliser
              </Button>
              <Button type="button" variant="ghost" onClick={onSkip}>
                Passer
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
} 
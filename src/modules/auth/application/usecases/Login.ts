import { InvalidCredentials } from "../../domain/exceptions/InvalidCredentials";
import { AuthUserRepository } from "../../domain/repositories/AuthUserRepository";
import { Email } from "../../domain/valueobjects/Email";
import { Password } from "../../domain/valueobjects/Password";

export interface LoginResponse {
  token: string;
}

export class Login {
  constructor(
    private readonly authRepository: AuthUserRepository,
  ) {}

  async execute(email: string, password: string): Promise<LoginResponse> {
    try {
      const emailValue = new Email(email);
      const passwordValue = new Password(password);
      const token = await this.authRepository.login(emailValue, passwordValue);
      if (!token) {
        throw new InvalidCredentials("Invalid credentials");
      }
      return {
        token,
      };
    } catch (error) {
      throw error;
    }
  }
}
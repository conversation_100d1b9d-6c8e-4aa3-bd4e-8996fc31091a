import { InvalidResetPasswordToken } from "../../domain/exceptions/InvalidResetPasswordToken";
import { AuthUserRepository } from "../../domain/repositories/AuthUserRepository";
import { Password } from "../../domain/valueobjects/Password";

export class ResetPassword {
  constructor(
    private readonly authRepository: AuthUserRepository,
    private readonly token: string
  ) {}

  async execute(password: Password) {
    const isTokenValid = await this.authRepository.checkResetPasswordToken(
      this.token
    );
    if (!isTokenValid) {
      throw new InvalidResetPasswordToken("Invalid token");
    }
    await this.authRepository.resetPassword(this.token, password);
  }
}

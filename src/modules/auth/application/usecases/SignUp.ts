import { AuthUserRepository } from "../../domain/repositories/AuthUserRepository";
import { Email } from "../../domain/valueobjects/Email";
import { Password } from "../../domain/valueobjects/Password";
import { Username } from "../../domain/valueobjects/Username";

export class SignUp {
  constructor(private readonly authRepository: AuthUserRepository) {}

  async execute(email: string, password: string, username: string): Promise<void> {
    try {
      const emailValue = new Email(email);
      const passwordValue = new Password(password);
      const usernameValue = new Username(username);
      await this.authRepository.createUser(emailValue, passwordValue, usernameValue);
    } catch (error) {
      throw error;
    }
  }
}
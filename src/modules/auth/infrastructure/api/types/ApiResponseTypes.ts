// Types de réponse standardisés pour l'API backend
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

// Types spécifiques pour l'authentification
export interface LoginResponse {
  token: string;
}

export interface ProfileResponse {
  userId: string;
  email: string;
  username: string;
  roles: string[];
}

export interface CheckResetTokenResponse {
  isValid: boolean;
} 
import React, { create<PERSON>ontext, useEffect, useState } from "react";
import { AuthUser } from "../../domain/entities/AuthUser";
import { Login } from "../../application/usecases/Login";
import {
  AuthUserApiRepository,
} from "../../infrastructure/api/AuthUserApiRepository";
import { Password } from "../../domain/valueobjects/Password";
import { Email } from "../../domain/valueobjects/Email";
import { Username } from "../../domain/valueobjects/Username";
import { SignUp } from "../../application/usecases/SignUp";
import { ForgotPassword } from "../../application/usecases/ForgotPassword";
import { ResetPassword } from "../../application/usecases/ResetPassword";
import { useAppWebSocket } from "../../../shared/presentation/hooks/useAppWebSocket";
import { multiWebSocketService } from "../../../shared/infrastructure/services/MultiWebSocketService";
import { Api } from "@/modules/shared/domain/repositories/Api";

export interface AuthContextType {
  isAuthenticated: boolean;
  user: AuthUser | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, username: string) => Promise<void>;
  logout: () => void;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
  checkResetPasswordToken: (token: string) => Promise<boolean>;
  clearError: () => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: React.ReactNode;
  apiInstance: Api;
}

export const AuthProvider = ({ children, apiInstance }: AuthProviderProps) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const authRepository = new AuthUserApiRepository(apiInstance);
  const { connectAppWebSocket, disconnectAppWebSocket } = useAppWebSocket();

  const clearError = () => setError(null);

  const loadUserProfile = async () => {
    try {
      const profile = await authRepository.getUserProfile();
      if (profile) {
        setUser({
          id: profile.userId,
          email: new Email(profile.email),
          username: new Username(profile.username),
          roles: profile.roles,
        });
        setIsAuthenticated(true);
        
        // Connecter automatiquement au WebSocket global pour analytics
        const token = localStorage.getItem("token");
        if (token) {
          try {
            await connectAppWebSocket(token, {
              onConnect: () => {
                // Connected to global analytics WebSocket
              },
              onDisconnect: () => {
                // Disconnected from global analytics WebSocket
              },
              onUserStatus: (data) => {
                // Ici on pourrait dispatcher des événements pour mettre à jour l'UI
              },
              onError: (error) => {
                // Global WebSocket error
              }
            });
          } catch (error) {
            // Ne pas bloquer l'authentification si le WebSocket échoue
          }
        }
      } else {
        localStorage.removeItem("token");
        setIsAuthenticated(false);
        setUser(null);
        disconnectAppWebSocket();
      }
    } catch (error) {
      localStorage.removeItem("token");
      setIsAuthenticated(false);
      setUser(null);
      disconnectAppWebSocket();
    }
  };

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await new Login(authRepository).execute(email, password);
      localStorage.setItem("token", response.token);
      
      await loadUserProfile();
      
    } catch (error: any) {
      setError(error.message || 'Login error');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    // Émettre l'événement pour notifier le logout
    window.dispatchEvent(new CustomEvent('auth:logout'));
    
    // Déconnecter TOUS les WebSockets de manière coordonnée
    // Cela déconnecte d'abord chat puis app, dans le bon ordre
    multiWebSocketService.disconnectAll();
    
    localStorage.removeItem("token");
    setIsAuthenticated(false);
    setUser(null);
    setError(null);
  };

  const signup = async (email: string, password: string, username: string) => {
    setLoading(true);
    setError(null);
    try {
      await new SignUp(authRepository).execute(email, password, username);
      
      await login(email, password);
      
    } catch (error: any) {
      setError(error.message || 'Registration error');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const forgotPassword = async (email: string) => {
    setLoading(true);
    setError(null);
    try {
      await new ForgotPassword(authRepository).execute(new Email(email));
    } catch (error: any) {
      setError(error.message || 'Password reset request error');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (token: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      await new ResetPassword(authRepository, token).execute(
        new Password(password)
      );
    } catch (error: any) {
      setError(error.message || 'Password reset error');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const checkResetPasswordToken = async (token: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      return await authRepository.checkResetPasswordToken(token);
    } catch (error: any) {
      setError(error.message || 'Token verification error');
      return false;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem("token");
      if (token) {
        await loadUserProfile();
      }
      setLoading(false);
    };

    initializeAuth();
    
    // Cleanup function pour déconnecter lors du démontage
    return () => {
      disconnectAppWebSocket();
    };
  }, []);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user,
        loading,
        error,
        login,
        logout,
        signup,
        forgotPassword,
        resetPassword,
        checkResetPasswordToken,
        clearError,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

import { Email } from "@/modules/auth/domain/valueobjects/Email";
import { Password } from "../valueobjects/Password";
import { Username } from "../valueobjects/Username";

export interface AuthUserRepository {
    login(email: Email, password: Password): Promise<string | null>;
    createUser(email: Em<PERSON>, password: Password, username: Userna<PERSON>): Promise<void>;
    forgotPassword(email: Email): Promise<void>;
    resetPassword(token: string, password: Password): Promise<void>;
    checkResetPasswordToken(token: string): Promise<boolean>;
}
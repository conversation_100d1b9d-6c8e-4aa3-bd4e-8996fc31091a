import { InvalidPasswordException } from "../exceptions/InvalidPassword";
import { PasswordTooShortException } from "../exceptions/PasswordTooShort";

export class Password {
  constructor(private readonly value: string) {
    if (value.length < 8) {
      throw new PasswordTooShortException("Password must be at least 8 characters long");
    }
    if (!value.match(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&._\-+=])[A-Za-z\d@$!%*?&._\-+=]{8,}$/)) {
      throw new InvalidPasswordException(
        "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character",
      );
    }
  }

  getValue(): string {
    return this.value;
  }
}
import { InvalidUsernameException } from "../exceptions/InvalidUsername";

export class Username {
  private readonly value: string;

  constructor(username: string) {
    this.validateUsername(username);
    this.value = username;
  }

  private validateUsername(username: string): void {
    if (!username || username.trim().length === 0) {
      throw new InvalidUsernameException("Username cannot be empty");
    }

    if (username.length < 3) {
      throw new InvalidUsernameException("Username must be at least 3 characters long");
    }

    if (username.length > 20) {
      throw new InvalidUsernameException("Username cannot be longer than 20 characters");
    }

    const usernameRegex = /^[a-zA-Z0-9_]+$/;
    if (!usernameRegex.test(username)) {
      throw new InvalidUsernameException("Username can only contain letters, numbers and underscores");
    }
  }

  public getValue(): string {
    return this.value;
  }

  public equals(other: Username): boolean {
    return this.value === other.value;
  }

  public toString(): string {
    return this.value;
  }
} 
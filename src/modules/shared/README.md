# Module Shared

Le module `shared` contient tous les éléments transversaux réutilisables dans l'application, organisés selon la Clean Architecture.

## 🏗️ Structure

```
src/modules/shared/
├── domain/                    # Entités et logique métier partagées
├── application/              # Cas d'usage partagés
├── infrastructure/           # Services techniques et configuration
│   ├── services/            # Services WebSocket
│   │   ├── MultiWebSocketService.ts
│   │   └── AppWebSocketService.ts
│   ├── config/              # Configuration technique
│   │   └── api.config.ts
│   ├── utils/               # Utilitaires techniques
│   │   └── utils.ts
│   └── index.ts
├── presentation/             # Interface utilisateur partagée
│   ├── components/          # Composants UI réutilisables
│   │   └── ui/              # Composants shadcn/ui
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── input.tsx
│   │       ├── label.tsx
│   │       └── avatar.tsx
│   ├── hooks/               # Hooks React partagés
│   │   └── useAppWebSocket.ts
│   └── index.ts
├── index.ts                  # Point d'entrée principal
└── README.md
```

## 📦 Exports

### Infrastructure
- **Services WebSocket** : `multiWebSocketService`, `appWebSocketService`
- **Configuration** : `apiConfig`
- **Utilitaires** : `cn` (pour Tailwind CSS)

### Presentation
- **Composants UI** : `Button`, `Card`, `Input`, `Label`, `Avatar`, etc.
- **Hooks** : `useAppWebSocket`

## 🚀 Utilisation

### Import des composants UI
```typescript
import { Button, Card, Input } from '@/modules/shared/presentation';
```

### Import des services
```typescript
import { multiWebSocketService, apiConfig } from '@/modules/shared/infrastructure';
```

### Import des utilitaires
```typescript
import { cn } from '@/modules/shared/infrastructure';
```

## Services

### MultiWebSocketService
Service principal qui gère plusieurs connexions WebSocket simultanées vers différents namespaces.

**Fonctionnalités :**
- Connexions multiples simultanées
- Déconnexion coordonnée dans le bon ordre
- Gestion des événements par namespace

### AppWebSocketService
Service wrapper pour le namespace global (`/`) gérant :
- Analytics et tracking de connexions
- Statuts utilisateurs globaux
- Événements transversaux

## Hooks

### useAppWebSocket
Hook React pour gérer la connexion au WebSocket global.

**Usage :**
```typescript
const { connectAppWebSocket, disconnectAppWebSocket } = useAppWebSocket();
```

## Principes

- **Séparation des responsabilités** : Chaque service a une responsabilité claire
- **Inversion de dépendance** : Les services dépendent d'abstractions
- **Réutilisabilité** : Code partagé entre modules
- **Clean Architecture** : Respect des couches domain/application/infrastructure/presentation 
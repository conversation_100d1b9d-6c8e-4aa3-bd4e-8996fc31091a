interface ApiConfig {
  baseURL: string;
  timeout: number;
}

const getApiConfig = (): ApiConfig => {
  // Détection automatique de l'environnement
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';
  
  // URL de base selon l'environnement
  let baseURL: string;
  
  if (process.env.REACT_APP_API_URL) {
    // Utilise la variable d'environnement si définie
    baseURL = process.env.REACT_APP_API_URL;
  } else if (isDevelopment) {
    // Environnement de développement
    baseURL = 'http://localhost:3001';
  } else if (isProduction) {
    // Environnement de production - à adapter selon votre setup
    baseURL = 'https://your-production-api.com';
  } else {
    // Fallback
    baseURL = 'http://localhost:3001';
  }

  return {
    baseURL,
    timeout: 10000, // 10 secondes de timeout
  };
};

export const apiConfig = getApiConfig(); 
# Configuration de l'API

## Variables d'environnement

Pour configurer l'URL du backend, vous pouvez utiliser la variable d'environnement `REACT_APP_API_URL`.

### Fichier `.env.local` (recommandé pour le développement)

```bash
# URL du backend
REACT_APP_API_URL=http://localhost:3001

# Environnement
NODE_ENV=development
```

### Détection automatique

Si aucune variable d'environnement n'est définie, la configuration se base sur `NODE_ENV` :

- **Développement** : `http://localhost:3001`
- **Production** : `https://your-production-api.com` (à modifier dans `api.config.ts`)
- **Fallback** : `http://localhost:3001`

## Configuration avancée

La configuration se trouve dans `src/config/api.config.ts` et comprend :

- **baseURL** : URL de base du backend
- **timeout** : Timeout des requêtes (10 secondes par défaut)

## Service API

Le service API (`ApiService`) fournit :

- **Interceptors automatiques** pour l'authentification JWT
- **Gestion des erreurs** centralisée
- **Redirection automatique** en cas de token expiré
- **Méthodes HTTP** complètes (GET, POST, PUT, DELETE, PATCH)

## Utilisation

```typescript
import { apiService } from '@/services/api';

// Exemple d'utilisation
const response = await apiService.post('/api/auth/login', { email, password });
``` 
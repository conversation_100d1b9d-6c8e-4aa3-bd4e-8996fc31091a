import { io, Socket } from 'socket.io-client';

export interface WebSocketEventHandlers {
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
  [key: string]: ((data: any) => void) | undefined;
}

export interface WebSocketConfig {
  url: string;
  namespace?: string;
  auth?: {
    token: string;
  };
  transports?: string[];
}

interface NamespaceConnection {
  socket: Socket;
  isConnected: boolean;
  handlers: WebSocketEventHandlers;
}

class MultiWebSocketService {
  private connections = new Map<string, NamespaceConnection>();

  public connect(namespace: string, config: WebSocketConfig, handlers: WebSocketEventHandlers = {}): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Disconnect existing connection for this namespace if any
        if (this.connections.has(namespace)) {
          this.disconnect(namespace);
        }

        const socketUrl = config.namespace ? `${config.url}${config.namespace}` : config.url;
        
        const socket = io(socketUrl, {
          auth: config.auth,
          transports: config.transports || ['websocket'],
          upgrade: true,
          rememberUpgrade: true
        });

        const connection: NamespaceConnection = {
          socket,
          isConnected: false,
          handlers
        };

        this.connections.set(namespace, connection);
        this.setupEventHandlers(namespace, connection);

        socket.on('connect', () => {
          connection.isConnected = true;
          handlers.onConnect?.();
          resolve();
        });

        socket.on('connect_error', (error) => {
          connection.isConnected = false;
          this.connections.delete(namespace);
          reject(error);
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  public disconnect(namespace: string): void {
    const connection = this.connections.get(namespace);
    if (connection) {
      connection.socket.disconnect();
      this.connections.delete(namespace);
    }
  }

  public disconnectAll(): void {
    // Déconnecter dans l'ordre: /chat d'abord, puis /
    // Cela évite que AppGateway marque l'utilisateur offline avant que ChatGateway nettoie les indicateurs de frappe
    const namespaces = Array.from(this.connections.keys());
    const orderedNamespaces = namespaces.sort((a, b) => {
      // /chat en premier, / en dernier
      if (a === '/chat' && b === '/') return -1;
      if (a === '/' && b === '/chat') return 1;
      return 0;
    });
    
    orderedNamespaces.forEach(namespace => {
      this.disconnect(namespace);
    });
  }

  public isConnected(namespace: string): boolean {
    const connection = this.connections.get(namespace);
    return connection?.isConnected === true && connection.socket.connected === true;
  }

  public emit(namespace: string, event: string, data?: any): void {
    const connection = this.connections.get(namespace);
    if (!connection || !this.isConnected(namespace)) {
      return;
    }

    connection.socket.emit(event, data);
  }

  public getSocket(namespace: string): Socket | null {
    return this.connections.get(namespace)?.socket || null;
  }

  private setupEventHandlers(namespace: string, connection: NamespaceConnection): void {
    const { socket, handlers } = connection;

    // Setup generic event handlers
    socket.on('error', (error: any) => {
      handlers.onError?.(error);
    });

    socket.on('disconnect', (reason) => {
      connection.isConnected = false;
      handlers.onDisconnect?.();
    });

    // Setup custom event handlers
    Object.keys(handlers).forEach(eventName => {
      if (!['onConnect', 'onDisconnect', 'onError'].includes(eventName)) {
        const handler = handlers[eventName];
        if (handler) {
          socket.on(eventName, handler);
        }
      }
    });
  }
}

// Export singleton instance
export const multiWebSocketService = new MultiWebSocketService(); 
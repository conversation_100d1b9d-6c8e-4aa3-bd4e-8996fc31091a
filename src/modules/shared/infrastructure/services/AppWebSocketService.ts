import { multiWebSocketService, WebSocketEventHandlers } from './MultiWebSocketService';

export interface AppWebSocketEventHandlers {
  onConnect?: () => void;
  onDisconnect?: () => void;
  onUserStatus?: (data: { userId: string; isOnline: boolean }) => void;
  onError?: (error: any) => void;
}

class AppWebSocketService {
  private readonly namespace = '/';

  public async connect(token: string, handlers: AppWebSocketEventHandlers = {}): Promise<void> {
    const config = {
      url: process.env.REACT_APP_WS_URL || 'http://localhost:3001',
      namespace: '/', // Namespace racine pour analytics et statuts globaux
      auth: {
        token: token
      },
      transports: ['websocket']
    };

    // Map app-specific handlers to generic handlers
    const genericHandlers: WebSocketEventHandlers = {
      onConnect: () => {
        handlers.onConnect?.();
      },
      onDisconnect: () => {
        handlers.onDisconnect?.();
      },
      onError: (error) => {
        handlers.onError?.(error);
      },
      // App-specific events
      user_status: (data: { userId: string; isOnline: boolean }) => {
        handlers.onUserStatus?.(data);
      }
    };

    await multiWebSocketService.connect(this.namespace, config, genericHandlers);
  }

  public disconnect(): void {
    multiWebSocketService.disconnect(this.namespace);
  }

  public isConnected(): boolean {
    return multiWebSocketService.isConnected(this.namespace);
  }
}

// Export singleton instance
export const appWebSocketService = new AppWebSocketService(); 
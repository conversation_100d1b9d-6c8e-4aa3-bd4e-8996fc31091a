export interface ApplicationError {
  message: string;
  code: string;
  isUserFacing: boolean;
}

export class ErrorMappingService {
  static mapHttpErrorToApplicationError(status: number, message?: string): ApplicationError {
    switch (status) {
      case 400:
        return {
          message: message || 'Invalid request. Please check your input.',
          code: 'INVALID_REQUEST',
          isUserFacing: true
        };
      
      case 401:
        return {
          message: 'You need to log in to access this feature.',
          code: 'UNAUTHORIZED',
          isUserFacing: true
        };
      
      case 403:
        return {
          message: 'You do not have permission to perform this action.',
          code: 'FORBIDDEN',
          isUserFacing: true
        };
      
      case 404:
        return {
          message: 'The requested resource was not found.',
          code: 'NOT_FOUND',
          isUserFacing: true
        };
      
      case 409:
        return {
          message: message || 'This action conflicts with the current state.',
          code: 'CONFLICT',
          isUserFacing: true
        };
      
      case 422:
        return {
          message: message || 'The provided data is invalid.',
          code: 'VALIDATION_ERROR',
          isUserFacing: true
        };
      
      case 429:
        return {
          message: 'Too many requests. Please wait a moment before trying again.',
          code: 'RATE_LIMITED',
          isUserFacing: true
        };
      
      case 500:
        return {
          message: 'An internal error occurred. Please try again later.',
          code: 'INTERNAL_ERROR',
          isUserFacing: true
        };
      
      case 502:
      case 503:
      case 504:
        return {
          message: 'The service is temporarily unavailable. Please try again later.',
          code: 'SERVICE_UNAVAILABLE',
          isUserFacing: true
        };
      
      default:
        return {
          message: message || 'An unexpected error occurred.',
          code: 'UNKNOWN_ERROR',
          isUserFacing: false
        };
    }
  }

  static mapNetworkErrorToApplicationError(error: any): ApplicationError {
    if (error.code === 'NETWORK_ERROR') {
      return {
        message: 'Network connection failed. Please check your internet connection.',
        code: 'NETWORK_ERROR',
        isUserFacing: true
      };
    }

    if (error.code === 'TIMEOUT') {
      return {
        message: 'The request timed out. Please try again.',
        code: 'TIMEOUT',
        isUserFacing: true
      };
    }

    return {
      message: 'A connection error occurred. Please try again.',
      code: 'CONNECTION_ERROR',
      isUserFacing: true
    };
  }

  static isRetryableError(error: ApplicationError): boolean {
    const retryableCodes = [
      'NETWORK_ERROR',
      'TIMEOUT',
      'SERVICE_UNAVAILABLE',
      'INTERNAL_ERROR'
    ];
    
    return retryableCodes.includes(error.code);
  }
} 
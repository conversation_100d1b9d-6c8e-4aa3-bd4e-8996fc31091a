import { useCallback, useEffect, useRef } from 'react';
import { appWebSocketService, AppWebSocketEventHandlers } from '../../infrastructure/services/AppWebSocketService';

export const useAppWebSocket = () => {
  const isConnected = useRef(false);

  const connectAppWebSocket = useCallback(async (token: string, handlers: AppWebSocketEventHandlers = {}) => {
    if (isConnected.current) {
      console.log('App WebSocket already connected');
      return;
    }

    try {
      console.log('Connecting to App WebSocket (analytics & global features)...');
      await appWebSocketService.connect(token, {
        onConnect: () => {
          console.log('✅ Connected to App WebSocket');
          isConnected.current = true;
          handlers.onConnect?.();
        },
        onDisconnect: () => {
          console.log('❌ Disconnected from App WebSocket');
          isConnected.current = false;
          handlers.onDisconnect?.();
        },
        onUserStatus: handlers.onUserStatus,
        onError: (error) => {
          console.error('App WebSocket error:', error);
          handlers.onError?.(error);
        }
      });
    } catch (error) {
      console.error('Failed to connect to App WebSocket:', error);
      throw error;
    }
  }, []);

  const disconnectAppWebSocket = useCallback(() => {
    if (isConnected.current) {
      console.log('Disconnecting from App WebSocket...');
      appWebSocketService.disconnect();
      isConnected.current = false;
    }
  }, []);

  useEffect(() => {
    return () => {
      disconnectAppWebSocket();
    };
  }, [disconnectAppWebSocket]);

  return {
    connectAppWebSocket,
    disconnectAppWebSocket,
    isAppWebSocketConnected: () => isConnected.current
  };
}; 
import { useState } from "react";

export type MatchForm = {
  gender: string;
  lookingFor: string;
  ageFrom: string;
  ageTo: string;
};

export function useMatchForm(initial?: Partial<MatchForm>) {
  const [form, setForm] = useState<MatchForm>({
    gender: "",
    lookingFor: "",
    ageFrom: "18",
    ageTo: "18",
    ...initial,
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>
  ) => {
    setForm((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  return { form, setForm, handleChange };
}
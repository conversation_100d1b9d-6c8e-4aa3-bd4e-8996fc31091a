import React from 'react';
import { useThemeClasses } from '../../theme/ThemeProvider';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'gradient';
  children: React.ReactNode;
}

export function Card({
  variant = 'default',
  className = '',
  children,
  ...props
}: CardProps) {
  const themeClasses = useThemeClasses();

  const baseClasses = 'rounded-xl transition-all duration-200';
  
  const variantClasses = {
    default: `bg-white ${themeClasses.shadowMd} border border-neutral-100`,
    elevated: `bg-white ${themeClasses.shadowLg} hover:${themeClasses.shadowPrimary} border border-neutral-100`,
    outlined: 'bg-white border-2 border-neutral-200 hover:border-[var(--color-primary-200)]',
    gradient: `${themeClasses.bgGradientSubtle} ${themeClasses.shadowMd} border border-[var(--color-primary-100)]`
  };

  const classes = [
    baseClasses,
    variantClasses[variant],
    className
  ].join(' ');

  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
}

export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export function CardHeader({ className = '', children, ...props }: CardHeaderProps) {
  return (
    <div className={`p-6 pb-4 ${className}`} {...props}>
      {children}
    </div>
  );
}

export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export function CardContent({ className = '', children, ...props }: CardContentProps) {
  return (
    <div className={`p-6 pt-0 ${className}`} {...props}>
      {children}
    </div>
  );
}

export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export function CardFooter({ className = '', children, ...props }: CardFooterProps) {
  return (
    <div className={`p-6 pt-4 border-t border-neutral-100 ${className}`} {...props}>
      {children}
    </div>
  );
}

export interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

export function CardTitle({ 
  as: Component = 'h3', 
  className = '', 
  children, 
  ...props 
}: CardTitleProps) {
  const themeClasses = useThemeClasses();
  
  return (
    <Component 
      className={`text-xl font-semibold ${themeClasses.textNeutral} ${className}`} 
      {...props}
    >
      {children}
    </Component>
  );
}

export interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
}

export function CardDescription({ className = '', children, ...props }: CardDescriptionProps) {
  const themeClasses = useThemeClasses();
  
  return (
    <p className={`text-sm ${themeClasses.textMuted} mt-1 ${className}`} {...props}>
      {children}
    </p>
  );
}
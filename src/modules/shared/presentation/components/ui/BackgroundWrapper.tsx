import React from 'react';

interface BackgroundWrapperProps {
  children: React.ReactNode;
  variant?: 'auth' | 'app' | 'onboarding';
  className?: string;
}

export function BackgroundWrapper({ 
  children, 
  variant = 'app',
  className = '' 
}: BackgroundWrapperProps) {
  
  const getBackgroundClasses = () => {
    switch (variant) {
      case 'auth':
        // Gradient moderne et élégant avec des tons plus sombres pour les pages d'authentification
        return 'bg-white';
      
      case 'onboarding':
        // Gradient coloré avec des nuances plus profondes pour l'onboarding
        return 'bg-white relative overflow-hidden';
      
      case 'app':
      default:
        // Gradient sophistiqué avec des tons sombres pour l'application principale
        return 'bg-white relative overflow-hidden';
    }
  };

  const renderDecorations = () => {
    if (variant === 'onboarding') {
      return (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Cercles décoratifs animés avec couleurs adaptées au fond sombre */}
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-[var(--color-primary-400)] to-[var(--color-primary-500)] rounded-full opacity-30 blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-purple-400 to-pink-500 rounded-full opacity-35 blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-full opacity-25 blur-2xl animate-pulse" style={{ animationDelay: '0.5s' }}></div>
          
          {/* Motif de points subtil avec couleur claire */}
          <div className="absolute inset-0 opacity-10" style={{
            backgroundImage: 'radial-gradient(circle at 2px 2px, rgba(138, 32, 120, 0.3) 1px, transparent 0)',
            backgroundSize: '40px 40px'
          }}></div>
        </div>
      );
    }
    
    if (variant === 'app') {
      return (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Effets de lumière sophistiqués avec couleurs vives sur fond sombre */}
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/40 via-cyan-500/30 to-indigo-400/40 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '4s' }}></div>
          <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-purple-500/35 via-pink-500/30 to-blue-500/35 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '6s', animationDelay: '2s' }}></div>
          <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-r from-indigo-400/30 to-purple-500/25 rounded-full blur-2xl animate-pulse" style={{ animationDuration: '5s', animationDelay: '1s' }}></div>
          
          {/* Formes géométriques flottantes plus visibles */}
          <div className="absolute top-20 left-20 w-4 h-4 bg-blue-400/60 rounded-full animate-bounce" style={{ animationDuration: '3s', animationDelay: '0s' }}></div>
          <div className="absolute top-40 right-32 w-3 h-3 bg-purple-400/60 rounded-full animate-bounce" style={{ animationDuration: '4s', animationDelay: '1s' }}></div>
          <div className="absolute bottom-32 left-40 w-2 h-2 bg-indigo-400/60 rounded-full animate-bounce" style={{ animationDuration: '3.5s', animationDelay: '0.5s' }}></div>
          <div className="absolute bottom-20 right-20 w-3 h-3 bg-cyan-400/60 rounded-full animate-bounce" style={{ animationDuration: '4.5s', animationDelay: '2s' }}></div>
          
          {/* Motif subtil en arrière-plan avec couleur claire */}
          <div 
            className="absolute inset-0 opacity-[0.05]" 
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
              backgroundSize: '60px 60px'
            }}
          ></div>
          
          {/* Gradient mesh subtil avec couleurs claires */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-400/10 to-transparent"></div>
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-400/10 to-transparent"></div>
        </div>
      );
    }
    
    if (variant === 'auth') {
      return (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Décorations élégantes pour l'authentification avec couleurs adaptées */}
          <div className="absolute -top-32 -right-32 w-64 h-64 bg-gradient-to-br from-blue-500/35 to-indigo-500/30 rounded-full blur-2xl animate-pulse" style={{ animationDuration: '4s' }}></div>
          <div className="absolute -bottom-32 -left-32 w-80 h-80 bg-gradient-to-tr from-purple-500/30 to-blue-500/25 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '5s', animationDelay: '1s' }}></div>
          <div className="absolute top-1/2 right-1/3 w-48 h-48 bg-gradient-to-r from-indigo-500/25 to-purple-500/20 rounded-full blur-xl animate-pulse" style={{ animationDuration: '6s', animationDelay: '2s' }}></div>
          
          {/* Particules flottantes plus visibles sur fond sombre */}
          <div className="absolute top-16 left-16 w-2 h-2 bg-blue-400/50 rounded-full animate-ping" style={{ animationDuration: '3s' }}></div>
          <div className="absolute top-32 right-20 w-1 h-1 bg-purple-400/50 rounded-full animate-ping" style={{ animationDuration: '4s', animationDelay: '1s' }}></div>
          <div className="absolute bottom-28 left-24 w-1.5 h-1.5 bg-indigo-400/50 rounded-full animate-ping" style={{ animationDuration: '3.5s', animationDelay: '0.5s' }}></div>
        </div>
      );
    }
    
    return null;
  };

  return (
    <div className={`min-h-screen ${getBackgroundClasses()} ${className}`}>
      {renderDecorations()}
      {/* Overlay subtil adapté aux arrière-plans sombres pour améliorer la lisibilité */}
      <div className="absolute inset-0 bg-black/5 backdrop-blur-[0.5px]"></div>
      <div className="relative z-10 min-h-screen text-white">
        {children}
      </div>
    </div>
  );
} 
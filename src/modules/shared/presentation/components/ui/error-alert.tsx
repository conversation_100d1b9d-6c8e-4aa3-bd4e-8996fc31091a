import * as React from "react"
import { cn } from "../../../infrastructure/utils/utils"

interface ErrorAlertProps {
  errors: string[]
  title?: string
  className?: string
}

const ErrorAlert = React.forwardRef<HTMLDivElement, ErrorAlertProps>(
  ({ errors, title = "Veuillez corriger les erreurs suivantes :", className }, ref) => {
    if (errors.length === 0) return null;

    return (
      <div 
        ref={ref}
        className={cn(
          "bg-destructive/10 border border-destructive/20 rounded-lg p-4",
          className
        )}
      >
        <div className="text-destructive font-medium mb-2">
          {title}
        </div>
        <ul className="text-destructive text-sm space-y-1">
          {errors.map((error, index) => (
            <li key={index}>• {error}</li>
          ))}
        </ul>
      </div>
    )
  }
)
ErrorAlert.displayName = "ErrorAlert"

export { ErrorAlert } 
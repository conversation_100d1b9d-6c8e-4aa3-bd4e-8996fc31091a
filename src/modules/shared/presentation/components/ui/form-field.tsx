import * as React from "react"
import { Label } from "./label"
import { cn } from "../../../infrastructure/utils/utils"

interface FormFieldProps {
  label: string
  required?: boolean
  error?: string
  htmlFor?: string
  className?: string
  children: React.ReactNode
}

const FormField = React.forwardRef<HTMLDivElement, FormFieldProps>(
  ({ label, required = false, error, htmlFor, className, children }, ref) => {
    return (
      <div ref={ref} className={cn("space-y-2", className)}>
        <Label htmlFor={htmlFor} className="flex items-center gap-1">
          {label}
          {required && <span className="text-destructive">*</span>}
        </Label>
        {React.Children.map(children, (child) => {
          if (React.isValidElement(child)) {
            const childProps = child.props as any;
            return React.cloneElement(child, {
              ...childProps,
              className: cn(
                childProps.className,
                error ? 'border-destructive focus-visible:ring-destructive' : ''
              )
            });
          }
          return child;
        })}
        {error && (
          <p className="text-sm text-destructive font-medium">{error}</p>
        )}
      </div>
    )
  }
)
FormField.displayName = "FormField"

export { FormField } 
import React from 'react';
import { useThemeClasses } from '../../theme/ThemeProvider';
import { cn } from '@/modules/shared/infrastructure';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link' | 'destructive' | 'default';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  isLoading?: boolean;
  children: React.ReactNode;
}

export function Button({
  variant = 'primary',
  size = 'md',
  isLoading = false,
  className = '',
  disabled,
  children,
  ...props
}: ButtonProps) {
  const themeClasses = useThemeClasses();

  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none rounded-lg disabled:opacity-50 disabled:cursor-not-allowed';

  const variantClasses = {
    primary: `${themeClasses.btnPrimary} ${themeClasses.focusPrimary} ${themeClasses.shadowMd} hover:shadow-lg hover:scale-105`,
    default: `${themeClasses.btnPrimary} ${themeClasses.focusPrimary} ${themeClasses.shadowMd} hover:shadow-lg hover:scale-105`,
    secondary: `${themeClasses.btnSecondary} ${themeClasses.focusPrimary}`,
    outline: `${themeClasses.btnPrimaryOutline} ${themeClasses.focusPrimary} transition-all duration-200`,
    ghost: 'text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900',
    link: `${themeClasses.textPrimary} hover:${themeClasses.textPrimaryDark} hover:underline`,
    destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-opacity-20'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg'
  };

  const classes = cn(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    className
  );

  return (
    <button
      className={classes}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading ? (
        <>
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          Chargement...
        </>
      ) : (
        children
      )}
    </button>
  );
}
import React, { createContext, useContext, ReactNode } from 'react';
import { theme, Theme } from './colors';

interface ThemeContextValue {
  theme: Theme;
  isDark: boolean;
  toggleTheme?: () => void;
}

const ThemeContext = createContext<ThemeContextValue>({
  theme,
  isDark: false,
});

interface ThemeProviderProps {
  children: ReactNode;
  isDark?: boolean;
}

export function ThemeProvider({ children, isDark = false }: ThemeProviderProps) {
  const value: ThemeContextValue = {
    theme,
    isDark,
  };

  return (
    <ThemeContext.Provider value={value}>
      <div 
        className={isDark ? 'dark' : ''}
        style={{
          '--color-primary-50': theme.colors.primary[50],
          '--color-primary-100': theme.colors.primary[100],
          '--color-primary-200': theme.colors.primary[200],
          '--color-primary-300': theme.colors.primary[300],
          '--color-primary-400': theme.colors.primary[400],
          '--color-primary-500': theme.colors.primary[500],
          '--color-primary-600': theme.colors.primary[600],
          '--color-primary-700': theme.colors.primary[700],
          '--color-primary-800': theme.colors.primary[800],
          '--color-primary-900': theme.colors.primary[900],
          '--color-primary-950': theme.colors.primary[950],
          
          '--gradient-primary': theme.gradients.primary,
          '--gradient-primary-subtle': theme.gradients.primarySubtle,
          '--gradient-neutral': theme.gradients.neutral,
          '--gradient-dark': theme.gradients.dark,
          
          '--shadow-primary': theme.shadows.primary,
        } as React.CSSProperties}
      >
        {children}
      </div>
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Hook pour obtenir les classes CSS basées sur le thème
export function useThemeClasses() {
  const { theme } = useTheme();
  
  return {
    // Backgrounds
    bgPrimary: 'bg-[var(--color-primary-500)]',
    bgPrimaryLight: 'bg-[var(--color-primary-50)]',
    bgPrimarySubtle: 'bg-[var(--color-primary-100)]',
    bgGradientPrimary: 'bg-gradient-to-br from-[var(--color-primary-500)] to-[var(--color-primary-700)]',
    bgGradientSubtle: 'bg-gradient-to-br from-[var(--color-primary-50)] to-[var(--color-primary-100)]',
    
    // Text colors
    textPrimary: 'text-[var(--color-primary-500)]',
    textPrimaryDark: 'text-[var(--color-primary-700)]',
    textWhite: 'text-white',
    textNeutral: 'text-neutral-700',
    textMuted: 'text-neutral-500',
    textPinkLight: 'text-pink-300',
    
    // Borders
    borderPrimary: 'border-[var(--color-primary-500)]',
    borderPrimaryLight: 'border-[var(--color-primary-200)]',
    
    // Buttons
    btnPrimary: 'bg-[var(--color-primary-500)] hover:bg-[var(--color-primary-600)] text-white',
    btnPrimaryOutline: 'border-2 border-[var(--color-primary-500)] text-[var(--color-primary-500)] hover:bg-[var(--color-primary-500)] hover:text-white',
    btnSecondary: 'bg-neutral-100 hover:bg-neutral-200 text-neutral-700',
    
    // Shadows
    shadowPrimary: 'shadow-[var(--shadow-primary)]',
    shadowMd: 'shadow-md',
    shadowLg: 'shadow-lg',
    
    // Focus states
    focusPrimary: 'focus:ring-2 focus:ring-[var(--color-primary-500)] focus:ring-offset-2',
  };
} 
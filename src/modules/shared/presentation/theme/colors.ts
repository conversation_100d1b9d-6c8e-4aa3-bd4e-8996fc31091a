// Couleur principale demandée #CC42BD mais plus foncée
const primaryBase = '#A935A0'; // Version plus foncée de #CC42BD

export const colors = {
  // Couleurs principales
  primary: {
    50: '#FAF5F9',
    100: '#F4E8F2',
    200: '#E8D0E4',
    300: '#D7B0D1',
    400: '#C088B8',
    500: primaryBase, // #A935A0 - couleur principale
    600: '#8F2B8A',
    700: '#762474',
    800: '#5D1E5D',
    900: '#4A1949',
    950: '#2F0F2F',
  },

  // Couleurs neutres sobres
  neutral: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#E5E5E5',
    300: '#D4D4D4',
    400: '#A3A3A3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
    950: '#0A0A0A',
  },

  // Couleurs fonctionnelles
  success: {
    50: '#F0FDF4',
    500: '#22C55E',
    600: '#16A34A',
    700: '#15803D',
  },

  error: {
    50: '#FEF2F2',
    500: '#EF4444',
    600: '#DC2626',
    700: '#B91C1C',
  },

  warning: {
    50: '#FFFBEB',
    500: '#F59E0B',
    600: '#D97706',
    700: '#B45309',
  },

  // Couleurs spéciales
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
} as const;

// Thème principal
export const theme = {
  colors,
  
  // Gradients élégants
  gradients: {
    primary: `linear-gradient(135deg, ${colors.primary[500]} 0%, ${colors.primary[700]} 100%)`,
    primarySubtle: `linear-gradient(135deg, ${colors.primary[50]} 0%, ${colors.primary[100]} 100%)`,
    neutral: `linear-gradient(135deg, ${colors.neutral[50]} 0%, ${colors.neutral[100]} 100%)`,
    dark: `linear-gradient(135deg, ${colors.neutral[800]} 0%, ${colors.neutral[900]} 100%)`,
  },

  // Ombres sobres
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    primary: `0 10px 15px -3px ${colors.primary[500]}20, 0 4px 6px -4px ${colors.primary[500]}10`,
  },

  // Rayons de bordure
  borderRadius: {
    sm: '0.375rem', // 6px
    md: '0.5rem',   // 8px
    lg: '0.75rem',  // 12px
    xl: '1rem',     // 16px
    full: '9999px',
  },

  // Espacements
  spacing: {
    xs: '0.5rem',   // 8px
    sm: '0.75rem',  // 12px
    md: '1rem',     // 16px
    lg: '1.5rem',   // 24px
    xl: '2rem',     // 32px
    '2xl': '3rem',  // 48px
    '3xl': '4rem',  // 64px
  },
} as const;

export type Theme = typeof theme;
export type ColorName = keyof typeof colors;
export type ColorShade = keyof typeof colors.primary; 
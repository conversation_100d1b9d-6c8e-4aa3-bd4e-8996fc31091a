import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import "./App.css";
import HomePage from "./pages/HomePage";
import LoginPage from "./pages/LoginPage";
import SignupPage from "./pages/SignupPage";
import ForgotPassword from "./pages/ForgotPassword";
import { ResetPasswordPage } from "./pages/ResetPassword";
import OnboardingPage from "./pages/OnboardingPage";
import ChatOnlyPage from "./pages/ChatOnlyPage";
import ProfilePage from "./pages/ProfilePage";
import UserSearchPage from "./pages/UserSearchPage";
import LikedUsersPage from "./pages/LikedUsersPage";
import DislikedUsersPage from "./pages/DislikedUsersPage";
import { OnboardingGuard } from "@/modules/shared/presentation";
import { ConnectedUserProfilePage } from "./pages/ConnectedUserProfilePage";
import { UpdateProfilePage } from "./pages/UpdateProfilePage";
import BookingPage from "./pages/BookingPage";
import ReportPage from "./pages/ReportPage";
import { AdminRoute } from "./modules/shared/middleware/AdminRoute";
import UnauthorizedPage from "./pages/UnauthorizedPage";
import { Bounce, ToastContainer } from "react-toastify";
import LandingPage from "./pages/LandingPage";

function App() {
  return (
    <Router>
      <OnboardingGuard>
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick={false}
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
          transition={Bounce}
        />

        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/chat" element={<ChatOnlyPage />} />
          <Route path="/search" element={<UserSearchPage />} />
          <Route path="/liked" element={<LikedUsersPage />} />
          <Route path="/disliked" element={<DislikedUsersPage />} />
          <Route path="/profile/:userId" element={<ProfilePage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignupPage />} />
          <Route path="/onboarding" element={<OnboardingPage />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/splash" element={<LandingPage />} />
          <Route
            path="/reset-password/:token"
            element={<ResetPasswordPage />}
          />
          <Route
            path="/connected-user-profile"
            element={<ConnectedUserProfilePage />}
          />
          <Route path="edit-profile/:userId" element={<UpdateProfilePage />} />
          <Route path="/booking" element={<BookingPage />} />
          <Route path="/unauthorized" element={<UnauthorizedPage />} />
          {/* <Route path="/report" element={<ReportPage />} /> */}
          <Route
            path="/report"
            element={
              <AdminRoute>
                <ReportPage />
              </AdminRoute>
            }
          />
        </Routes>
      </OnboardingGuard>
    </Router>
  );
}

export default App;

import { MarkMessageAsReadUseCase } from "@/modules/chat/application/usecases/MarkMessageAsReadUseCase";
import { IChatRepository } from "@/modules/chat/domain/repositories/IChatRepository";
import { mock, instance, when, anything, verify } from "ts-mockito";

describe("MarkMessageAsReadUseCase", () => {
  let mockChatRepository: IChatRepository;
  let chatRepositoryInstance: IChatRepository;
  let markMessageAsReadUseCase: MarkMessageAsReadUseCase;

  beforeEach(() => {
    mockChatRepository = mock<IChatRepository>();
    chatRepositoryInstance = instance(mockChatRepository);
    markMessageAsReadUseCase = new MarkMessageAsReadUseCase(chatRepositoryInstance);
  });

  it("should mark message as read successfully", async () => {
    when(mockChatRepository.markMessageAsRead(anything())).thenResolve();

    await markMessageAsReadUseCase.execute("message-123");

    verify(mockChatRepository.markMessageAsRead("message-123")).once();
  });

  it("should handle repository errors", async () => {
    when(mockChatRepository.markMessageAsRead(anything()))
      .thenReject(new Error("Message not found"));

    await expect(markMessageAsReadUseCase.execute("invalid-message-id"))
      .rejects.toThrow("Message not found");
  });

  it("should handle empty message id", async () => {
    when(mockChatRepository.markMessageAsRead(anything())).thenResolve();

    await markMessageAsReadUseCase.execute("");

    verify(mockChatRepository.markMessageAsRead("")).once();
  });

  it("should handle null or undefined message id gracefully", async () => {
    when(mockChatRepository.markMessageAsRead(anything())).thenResolve();

    await markMessageAsReadUseCase.execute(null as any);
    await markMessageAsReadUseCase.execute(undefined as any);

    verify(mockChatRepository.markMessageAsRead(null as any)).once();
    verify(mockChatRepository.markMessageAsRead(undefined as any)).once();
  });
}); 
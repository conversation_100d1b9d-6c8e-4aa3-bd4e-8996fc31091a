import { GetChatHistoryUseCase } from "@/modules/chat/application/usecases/GetChatHistoryUseCase";
import { IChatRepository } from "@/modules/chat/domain/repositories/IChatRepository";
import { Message } from "@/modules/chat/domain/entities/Message";
import { mock, instance, when, anything, verify } from "ts-mockito";
import { MessageType } from "@/modules/chat/domain/valueobjects/MessageType";

describe("GetChatHistoryUseCase", () => {
  let mockChatRepository: IChatRepository;
  let chatRepositoryInstance: IChatRepository;
  let getChatHistoryUseCase: GetChatHistoryUseCase;

  beforeEach(() => {
    mockChatRepository = mock<IChatRepository>();
    chatRepositoryInstance = instance(mockChatRepository);
    getChatHistoryUseCase = new GetChatHistoryUseCase(chatRepositoryInstance);
  });

  it("should get chat history successfully", async () => {
    const mockMessages = [
      Message.create({
        content: "Hello!",
        senderId: "user-1",
        receiverId: "user-2",
        type: MessageType.text()
      }),
      Message.create({
        content: "Hi there!",
        senderId: "user-2",
        receiverId: "user-1",
        type: MessageType.text()
      })
    ];

    when(mockChatRepository.getChatHistory(anything(), anything(), anything(), anything()))
      .thenResolve(mockMessages);

    const result = await getChatHistoryUseCase.execute({
      userId: "user-1",
      otherUserId: "user-2"
    });

    expect(result).toBeDefined();
    expect(result).toHaveLength(2);
    expect(result[0].getContent().getValue()).toBe("Hello!");
    expect(result[1].getContent().getValue()).toBe("Hi there!");
    verify(mockChatRepository.getChatHistory("user-1", "user-2", undefined, undefined)).once();
  });

  it("should get chat history with limit", async () => {
    const mockMessages = [
      {
        id: "message-1",
        content: "Hello!",
        senderId: "user-1",
        receiverId: "user-2",
        timestamp: new Date("2024-01-01T10:00:00Z"),
        status: "read",
        type: "text"
      }
    ] as any[];

    when(mockChatRepository.getChatHistory(anything(), anything(), anything(), anything()))
      .thenResolve(mockMessages);

    const result = await getChatHistoryUseCase.execute({
      userId: "user-1",
      otherUserId: "user-2",
      limit: 10
    });

    expect(result).toBeDefined();
    expect(result).toHaveLength(1);
    verify(mockChatRepository.getChatHistory("user-1", "user-2", 10, undefined)).once();
  });

  it("should get chat history with before timestamp", async () => {
    const beforeDate = new Date("2024-01-01T12:00:00Z");
    const mockMessages = [] as any[];

    when(mockChatRepository.getChatHistory(anything(), anything(), anything(), anything()))
      .thenResolve(mockMessages);

    const result = await getChatHistoryUseCase.execute({
      userId: "user-1",
      otherUserId: "user-2",
      before: beforeDate
    });

    expect(result).toBeDefined();
    expect(result).toHaveLength(0);
    verify(mockChatRepository.getChatHistory("user-1", "user-2", undefined, beforeDate)).once();
  });

  it("should get chat history with both limit and before timestamp", async () => {
    const beforeDate = new Date("2024-01-01T12:00:00Z");
    const mockMessages = [] as any[];

    when(mockChatRepository.getChatHistory(anything(), anything(), anything(), anything()))
      .thenResolve(mockMessages);

    const result = await getChatHistoryUseCase.execute({
      userId: "user-1",
      otherUserId: "user-2",
      limit: 5,
      before: beforeDate
    });

    expect(result).toBeDefined();
    verify(mockChatRepository.getChatHistory("user-1", "user-2", 5, beforeDate)).once();
  });

  it("should handle repository errors", async () => {
    when(mockChatRepository.getChatHistory(anything(), anything(), anything(), anything()))
      .thenReject(new Error("Database connection failed"));

    await expect(getChatHistoryUseCase.execute({
      userId: "user-1",
      otherUserId: "user-2"
    })).rejects.toThrow("Database connection failed");
  });

  it("should return empty array when no messages found", async () => {
    when(mockChatRepository.getChatHistory(anything(), anything(), anything(), anything()))
      .thenResolve([]);

    const result = await getChatHistoryUseCase.execute({
      userId: "user-1",
      otherUserId: "user-2"
    });

    expect(result).toBeDefined();
    expect(result).toHaveLength(0);
  });
}); 
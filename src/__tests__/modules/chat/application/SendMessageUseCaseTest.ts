import { SendMessageUseCase } from '@/modules/chat/application/usecases/SendMessageUseCase';
import { IChatRepository } from '@/modules/chat/domain/repositories/IChatRepository';
import { InvalidMessageException } from '@/modules/chat/domain/exceptions/InvalidMessageException';
import { Message } from '@/modules/chat/domain/entities/Message';
import { MessageType } from '@/modules/chat/domain/valueobjects/MessageType';
import { MessageContent } from '@/modules/chat/domain/valueobjects/MessageContent';
import { MessageStatus } from '@/modules/chat/domain/valueobjects/MessageStatus';
import { mock, instance, when, anything, verify, capture } from 'ts-mockito';

describe('SendMessageUseCase', () => {
  let mockChatRepository: IChatRepository;
  let chatRepositoryInstance: IChatRepository;
  let sendMessageUseCase: SendMessageUseCase;

  beforeEach(() => {
    mockChatRepository = mock<IChatRepository>();
    chatRepositoryInstance = instance(mockChatRepository);
    sendMessageUseCase = new SendMessageUseCase(chatRepositoryInstance);
  });

  describe('Successful message sending', () => {
    it('should send a text message successfully', async () => {
      // Arrange
      const mockMessage = new Message(
        'msg-123',
        new MessageContent('Hello world!'),
        'user-1',
        'user-2',
        'chat-123',
        MessageType.text(),
        MessageStatus.sent(),
        new Date()
      );

      when(mockChatRepository.sendMessage(anything())).thenResolve(mockMessage);

      // Act
      const result = await sendMessageUseCase.execute({
        content: 'Hello world!',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      });

      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBe('msg-123');
      expect(result.content.getValue()).toBe('Hello world!');
      expect(result.senderId).toBe('user-1');
      expect(result.receiverId).toBe('user-2');
      expect(result.type.isText()).toBe(true);
      expect(result.status.isSent()).toBe(true);

      verify(mockChatRepository.sendMessage(anything())).once();
    });

    it('should send an image message successfully', async () => {
      // Arrange
      const mockMessage = new Message(
        'msg-124',
        new MessageContent('image-url.jpg'),
        'user-1',
        'user-2',
        'chat-123',
        MessageType.image(),
        MessageStatus.sent(),
        new Date()
      );

      when(mockChatRepository.sendMessage(anything())).thenResolve(mockMessage);

      // Act
      const result = await sendMessageUseCase.execute({
        content: 'image-url.jpg',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.image()
      });

      // Assert
      expect(result).toBeDefined();
      expect(result.type.isImage()).toBe(true);
      expect(result.content.getValue()).toBe('image-url.jpg');
      verify(mockChatRepository.sendMessage(anything())).once();
    });

    it('should send a file message successfully', async () => {
      // Arrange
      const mockMessage = new Message(
        'msg-125',
        new MessageContent('document.pdf'),
        'user-1',
        'user-2',
        'chat-123',
        MessageType.file(),
        MessageStatus.sent(),
        new Date()
      );

      when(mockChatRepository.sendMessage(anything())).thenResolve(mockMessage);

      // Act
      const result = await sendMessageUseCase.execute({
        content: 'document.pdf',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.file()
      });

      // Assert
      expect(result).toBeDefined();
      expect(result.type.isFile()).toBe(true);
      expect(result.content.getValue()).toBe('document.pdf');
      verify(mockChatRepository.sendMessage(anything())).once();
    });

    it('should send a system message successfully', async () => {
      // Arrange
      const mockMessage = new Message(
        'msg-126',
        new MessageContent('User joined the chat'),
        'system',
        'user-2',
        'chat-123',
        MessageType.system(),
        MessageStatus.sent(),
        new Date()
      );

      when(mockChatRepository.sendMessage(anything())).thenResolve(mockMessage);

      // Act
      const result = await sendMessageUseCase.execute({
        content: 'User joined the chat',
        senderId: 'system',
        receiverId: 'user-2',
        type: MessageType.system()
      });

      // Assert
      expect(result).toBeDefined();
      expect(result.type.isSystem()).toBe(true);
      expect(result.content.getValue()).toBe('User joined the chat');
      verify(mockChatRepository.sendMessage(anything())).once();
    });
  });

  describe('Message validation', () => {
    it('should throw error for empty message content', async () => {
      // Act & Assert
      await expect(sendMessageUseCase.execute({
        content: '',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      })).rejects.toThrow(InvalidMessageException);

      verify(mockChatRepository.sendMessage(anything())).never();
    });

    it('should throw error for whitespace-only content', async () => {
      // Act & Assert
      await expect(sendMessageUseCase.execute({
        content: '   ',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      })).rejects.toThrow(InvalidMessageException);

      verify(mockChatRepository.sendMessage(anything())).never();
    });

    it('should throw error for message content that exceeds limit', async () => {
      const tooLongContent = 'a'.repeat(5001);
      
      // Act & Assert
      await expect(sendMessageUseCase.execute({
        content: tooLongContent,
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      })).rejects.toThrow(InvalidMessageException);

      verify(mockChatRepository.sendMessage(anything())).never();
    });

    it('should accept content at the maximum limit', async () => {
      // Arrange
      const maxLengthContent = 'a'.repeat(5000);
      const mockMessage = new Message(
        'msg-127',
        new MessageContent(maxLengthContent),
        'user-1',
        'user-2',
        'chat-123',
        MessageType.text(),
        MessageStatus.sent(),
        new Date()
      );

      when(mockChatRepository.sendMessage(anything())).thenResolve(mockMessage);

      // Act
      const result = await sendMessageUseCase.execute({
        content: maxLengthContent,
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      });

      // Assert
      expect(result.content.getValue()).toBe(maxLengthContent);
      verify(mockChatRepository.sendMessage(anything())).once();
    });
  });

  describe('Repository interaction', () => {
    it('should pass correct message data to repository', async () => {
      // Arrange
      const mockMessage = new Message(
        'msg-128',
        new MessageContent('Test message'),
        'sender-123',
        'receiver-456',
        'chat-789',
        MessageType.text(),
        MessageStatus.sent(),
        new Date()
      );

      when(mockChatRepository.sendMessage(anything())).thenResolve(mockMessage);

      // Act
      await sendMessageUseCase.execute({
        content: 'Test message',
        senderId: 'sender-123',
        receiverId: 'receiver-456',
        type: MessageType.text()
      });

      // Assert
      const [capturedMessage] = capture(mockChatRepository.sendMessage).last();
      expect(capturedMessage.content.getValue()).toBe('Test message');
      expect(capturedMessage.senderId).toBe('sender-123');
      expect(capturedMessage.receiverId).toBe('receiver-456');
      expect(capturedMessage.type.isText()).toBe(true);
      expect(capturedMessage.status.isSent()).toBe(true);
      expect(capturedMessage.id).toBeDefined();
      expect(capturedMessage.createdAt).toBeInstanceOf(Date);
    });

    it('should handle repository errors gracefully', async () => {
      // Arrange
      const repositoryError = new Error('Network connection failed');
      when(mockChatRepository.sendMessage(anything())).thenReject(repositoryError);

      // Act & Assert
      await expect(sendMessageUseCase.execute({
        content: 'Hello world!',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      })).rejects.toThrow('Network connection failed');

      verify(mockChatRepository.sendMessage(anything())).once();
    });

    it('should handle unexpected repository errors', async () => {
      // Arrange
      when(mockChatRepository.sendMessage(anything())).thenReject(new Error('Unexpected error'));

      // Act & Assert
      await expect(sendMessageUseCase.execute({
        content: 'Hello world!',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      })).rejects.toThrow('Unexpected error');
    });
  });

  describe('Message creation', () => {
    it('should generate unique message IDs', async () => {
      // Arrange
      const messages: Message[] = [];
      when(mockChatRepository.sendMessage(anything())).thenCall((message: Message) => {
        messages.push(message);
        return Promise.resolve(message);
      });

      // Act
      await sendMessageUseCase.execute({
        content: 'Message 1',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      });

      await sendMessageUseCase.execute({
        content: 'Message 2',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      });

      // Assert
      expect(messages).toHaveLength(2);
      expect(messages[0].id).not.toBe(messages[1].id);
    });

    it('should set correct timestamps', async () => {
      // Arrange
      const beforeTime = new Date();
      let capturedMessage: Message;

      when(mockChatRepository.sendMessage(anything())).thenCall((message: Message) => {
        capturedMessage = message;
        return Promise.resolve(message);
      });

      // Act
      await sendMessageUseCase.execute({
        content: 'Timestamped message',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      });

      const afterTime = new Date();

      // Assert
      expect(capturedMessage!.createdAt.getTime()).toBeGreaterThanOrEqual(beforeTime.getTime());
      expect(capturedMessage!.createdAt.getTime()).toBeLessThanOrEqual(afterTime.getTime());
    });
  });

  describe('Content trimming', () => {
    it('should trim whitespace from message content', async () => {
      // Arrange
      const mockMessage = new Message(
        'msg-129',
        new MessageContent('Trimmed content'),
        'user-1',
        'user-2',
        'chat-123',
        MessageType.text(),
        MessageStatus.sent(),
        new Date()
      );

      when(mockChatRepository.sendMessage(anything())).thenResolve(mockMessage);

      // Act
      const result = await sendMessageUseCase.execute({
        content: '  Trimmed content  ',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      });

      // Assert
      const [capturedMessage] = capture(mockChatRepository.sendMessage).last();
      expect(capturedMessage.content.getValue()).toBe('Trimmed content');
    });
  });
}); 
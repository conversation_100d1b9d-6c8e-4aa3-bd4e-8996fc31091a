import { SendMessageUseCase } from '@/modules/chat/application/usecases/SendMessageUseCase';
import { GetChatHistoryUseCase } from '@/modules/chat/application/usecases/GetChatHistoryUseCase';
import { MarkMessageAsReadUseCase } from '@/modules/chat/application/usecases/MarkMessageAsReadUseCase';
import { MessageType } from '@/modules/chat/domain/valueobjects/MessageType';
import { Message } from '@/modules/chat/domain/entities/Message';
import { MessageContent } from '@/modules/chat/domain/valueobjects/MessageContent';
import { MessageStatus } from '@/modules/chat/domain/valueobjects/MessageStatus';
import { IChatRepository } from '@/modules/chat/domain/repositories/IChatRepository';
import { mock, instance, when, verify, anything } from 'ts-mockito';

// Mock axios completely
jest.mock('axios', () => ({
  default: {
    create: jest.fn(() => ({
      post: jest.fn(),
      get: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
    })),
  },
}));

// Mock WebSocket service
jest.mock('@/modules/chat/infrastructure/services/ChatWebSocketService', () => ({
  chatWebSocketService: {
    sendMessage: jest.fn(),
    markMessageAsRead: jest.fn(),
    subscribeToNewMessages: jest.fn(),
    unsubscribeFromNewMessages: jest.fn(),
  },
}));

describe('Chat Integration Tests', () => {
  let mockChatRepository: IChatRepository;
  let chatRepositoryInstance: IChatRepository;
  let sendMessageUseCase: SendMessageUseCase;
  let getChatHistoryUseCase: GetChatHistoryUseCase;
  let markMessageAsReadUseCase: MarkMessageAsReadUseCase;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock repository
    mockChatRepository = mock<IChatRepository>();
    chatRepositoryInstance = instance(mockChatRepository);
    
    // Create use cases with mocked repository
    sendMessageUseCase = new SendMessageUseCase(chatRepositoryInstance);
    getChatHistoryUseCase = new GetChatHistoryUseCase(chatRepositoryInstance);
    markMessageAsReadUseCase = new MarkMessageAsReadUseCase(chatRepositoryInstance);
  });

  describe('Send Message Integration Flow', () => {
    it('should complete the full send message flow', async () => {
      // Arrange
      const mockMessage = new Message(
        'msg-123',
        new MessageContent('Hello integration test!'),
        'user-1',
        'user-2',
        'chat-123',
        MessageType.text(),
        MessageStatus.sent(),
        new Date()
      );

      when(mockChatRepository.sendMessage(anything())).thenResolve(mockMessage);

      // Act
      const result = await sendMessageUseCase.execute({
        content: 'Hello integration test!',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      });

      // Assert
      expect(result.content.getValue()).toBe('Hello integration test!');
      expect(result.senderId).toBe('user-1');
      expect(result.receiverId).toBe('user-2');
      expect(result.type.isText()).toBe(true);
      verify(mockChatRepository.sendMessage(anything())).once();
    });

    it('should handle repository errors in send message flow', async () => {
      // Arrange
      const repositoryError = new Error('Repository connection failed');
      when(mockChatRepository.sendMessage(anything())).thenReject(repositoryError);

      // Act & Assert
      await expect(sendMessageUseCase.execute({
        content: 'This will fail',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      })).rejects.toThrow('Repository connection failed');
    });
  });

  describe('Chat History Integration Flow', () => {
    it('should retrieve chat history successfully', async () => {
      // Arrange
      const mockMessages = [
        new Message(
          'msg-1',
          new MessageContent('First message'),
          'user-1',
          'user-2',
          'chat-123',
          MessageType.text(),
          MessageStatus.delivered(),
          new Date('2023-01-01T10:00:00Z')
        ),
        new Message(
          'msg-2',
          new MessageContent('Second message'),
          'user-2',
          'user-1',
          'chat-123',
          MessageType.text(),
          MessageStatus.read(),
          new Date('2023-01-01T10:01:00Z')
        )
      ];

             when(mockChatRepository.getChatHistory('user-1', 'user-2', 50, undefined)).thenResolve(mockMessages);

      // Act
      const messages = await getChatHistoryUseCase.execute({
        userId: 'user-1',
        otherUserId: 'user-2',
        limit: 50
      });

      // Assert
      expect(messages).toHaveLength(2);
      expect(messages[0].content.getValue()).toBe('First message');
      expect(messages[1].content.getValue()).toBe('Second message');
             verify(mockChatRepository.getChatHistory('user-1', 'user-2', 50, undefined)).once();
    });

    it('should return empty array when no messages exist', async () => {
      // Arrange
             when(mockChatRepository.getChatHistory('user-1', 'user-3', 50, undefined)).thenResolve([]);

      // Act
      const messages = await getChatHistoryUseCase.execute({
        userId: 'user-1',
        otherUserId: 'user-3',
        limit: 50
      });

      // Assert
      expect(messages).toHaveLength(0);
             verify(mockChatRepository.getChatHistory('user-1', 'user-3', 50, undefined)).once();
    });
  });

  describe('Mark Message as Read Integration Flow', () => {
    it('should mark message as read successfully', async () => {
      // Arrange
      when(mockChatRepository.markMessageAsRead('msg-123')).thenResolve();

      // Act
      await markMessageAsReadUseCase.execute('msg-123');

      // Assert
      verify(mockChatRepository.markMessageAsRead('msg-123')).once();
    });

    it('should handle repository error when marking message as read', async () => {
      // Arrange
      const repositoryError = new Error('Message not found');
      when(mockChatRepository.markMessageAsRead('invalid-msg-id')).thenReject(repositoryError);

      // Act & Assert
      await expect(markMessageAsReadUseCase.execute('invalid-msg-id'))
        .rejects.toThrow('Message not found');
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle validation errors in send message flow', async () => {
      // Act & Assert
      await expect(sendMessageUseCase.execute({
        content: '', // Invalid empty content
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      })).rejects.toThrow('Message content cannot be empty');
    });

    it('should handle network timeouts gracefully', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
             when(mockChatRepository.getChatHistory('user-1', 'user-2', 50, undefined)).thenReject(timeoutError);

      // Act & Assert
      await expect(getChatHistoryUseCase.execute({
        userId: 'user-1',
        otherUserId: 'user-2',
        limit: 50
      })).rejects.toThrow('Request timeout');
    });
  });

  describe('Message Type Integration', () => {
    it('should handle different message types correctly', async () => {
      // Arrange
      const textMessage = new Message(
        'msg-text',
        new MessageContent('Text message'),
        'user-1',
        'user-2',
        'chat-123',
        MessageType.text(),
        MessageStatus.sent(),
        new Date()
      );

      const imageMessage = new Message(
        'msg-image',
        new MessageContent('image.jpg'),
        'user-1',
        'user-2',
        'chat-123',
        MessageType.image(),
        MessageStatus.sent(),
        new Date()
      );

      when(mockChatRepository.sendMessage(anything())).thenResolve(textMessage).thenResolve(imageMessage);

      // Act
      const textResult = await sendMessageUseCase.execute({
        content: 'Text message',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      });

      const imageResult = await sendMessageUseCase.execute({
        content: 'image.jpg',
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.image()
      });

      // Assert
      expect(textResult.type.isText()).toBe(true);
             expect(imageResult.type.isImage()).toBe(true);
    });
  });

  describe('Concurrent Operations Integration', () => {
    it('should handle concurrent message sending', async () => {
      // Arrange
      const message1 = new Message(
        'msg-1',
        new MessageContent('Concurrent message 1'),
        'user-1',
        'user-2',
        'chat-123',
        MessageType.text(),
        MessageStatus.sent(),
        new Date()
      );

      const message2 = new Message(
        'msg-2',
        new MessageContent('Concurrent message 2'),
        'user-1',
        'user-2',
        'chat-123',
        MessageType.text(),
        MessageStatus.sent(),
        new Date()
      );

      when(mockChatRepository.sendMessage(anything()))
        .thenResolve(message1)
        .thenResolve(message2);

      // Act
      const [result1, result2] = await Promise.all([
        sendMessageUseCase.execute({
          content: 'Concurrent message 1',
          senderId: 'user-1',
          receiverId: 'user-2',
          type: MessageType.text()
        }),
        sendMessageUseCase.execute({
          content: 'Concurrent message 2',
          senderId: 'user-1',
          receiverId: 'user-2',
          type: MessageType.text()
        })
      ]);

      // Assert
      expect(result1.content.getValue()).toBe('Concurrent message 1');
      expect(result2.content.getValue()).toBe('Concurrent message 2');
    });
  });

  describe('Business Logic Integration', () => {
    it('should enforce message content length limits', async () => {
      // Act & Assert
      const tooLongContent = 'a'.repeat(5001);
      await expect(sendMessageUseCase.execute({
        content: tooLongContent,
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      })).rejects.toThrow('Message content cannot exceed 5000 characters');
    });

    it('should accept content at maximum length', async () => {
      // Arrange
      const maxLengthContent = 'a'.repeat(5000);
      const mockMessage = new Message(
        'msg-max',
        new MessageContent(maxLengthContent),
        'user-1',
        'user-2',
        'chat-123',
        MessageType.text(),
        MessageStatus.sent(),
        new Date()
      );

      when(mockChatRepository.sendMessage(anything())).thenResolve(mockMessage);

      // Act
      const result = await sendMessageUseCase.execute({
        content: maxLengthContent,
        senderId: 'user-1',
        receiverId: 'user-2',
        type: MessageType.text()
      });

      // Assert
      expect(result.content.getLength()).toBe(5000);
    });
  });
}); 
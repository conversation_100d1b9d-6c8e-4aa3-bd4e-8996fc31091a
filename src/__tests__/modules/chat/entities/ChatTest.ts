import { Chat } from "@/modules/chat/domain/entities/Chat";
import { Message } from "@/modules/chat/domain/entities/Message";
import { MessageType } from "@/modules/chat/domain/valueobjects/MessageType";
import { Participant } from "@/modules/chat/domain/valueobjects/Participant";

describe("Chat", () => {
  const participant1 = Participant.create({
    id: "user-1",
    name: "<PERSON>",
    email: "<EMAIL>"
  });
  
  const participant2 = Participant.create({
    id: "user-2",
    name: "<PERSON>",
    email: "<EMAIL>"
  });
  
  const participant3 = Participant.create({
    id: "user-3",
    name: "<PERSON>",
    email: "<EMAIL>"
  });
  
  const participant4 = Participant.create({
    id: "user-4",
    name: "<PERSON>",
    email: "<EMAIL>"
  });
  
  const participants = [participant1, participant2];

  describe("Factory method create", () => {
    it("should create a chat with two participants", () => {
      const chat = Chat.create(participants);

      expect(chat.getId()).toBeDefined();
      expect(chat.getParticipants()).toEqual(participants);
      expect(chat.getLastMessage()).toBeUndefined();
      expect(chat.getCreatedAt()).toBeInstanceOf(Date);
      expect(chat.getUpdatedAt()).toBeInstanceOf(Date);
    });

    it("should throw error for invalid number of participants", () => {
      expect(() => Chat.create([participant1])).toThrow("Chat must have exactly 2 participants");
      expect(() => Chat.create([participant1, participant2, participant3])).toThrow("Chat must have exactly 2 participants");
    });

    it("should generate unique IDs for different chats", () => {
      const chat1 = Chat.create(participants);
      const chat2 = Chat.create(participants);

      expect(chat1.getId()).not.toBe(chat2.getId());
    });
  });

  describe("Participant management", () => {
    let chat: Chat;

    beforeEach(() => {
      chat = Chat.create(participants);
    });

    it("should identify if user is participant", () => {
      expect(chat.hasParticipant("user-1")).toBe(true);
      expect(chat.hasParticipant("user-2")).toBe(true);
      expect(chat.hasParticipant("user-3")).toBe(false);
    });

    it("should get other participant", () => {
      const otherParticipant1 = chat.getOtherParticipant("user-1");
      const otherParticipant2 = chat.getOtherParticipant("user-2");
      
      expect(otherParticipant1?.getId()).toBe("user-2");
      expect(otherParticipant2?.getId()).toBe("user-1");
      // When user is not in chat, returns first participant that doesn't match the ID
      expect(chat.getOtherParticipant("user-3")?.getId()).toBe("user-1");
    });

    it("should get all other participants", () => {
      const otherParticipants1 = chat.getAllOtherParticipants("user-1");
      const otherParticipants2 = chat.getAllOtherParticipants("user-2");
      const otherParticipants3 = chat.getAllOtherParticipants("user-3");
      
      expect(otherParticipants1).toHaveLength(1);
      expect(otherParticipants1[0].getId()).toBe("user-2");
      expect(otherParticipants2).toHaveLength(1);
      expect(otherParticipants2[0].getId()).toBe("user-1");
      expect(otherParticipants3).toHaveLength(2);
      expect(otherParticipants3.map(p => p.getId())).toEqual(["user-1", "user-2"]);
    });

    it("should return copy of participants array", () => {
      const participantsCopy = chat.getParticipants();
      participantsCopy.push(participant3);

      expect(chat.getParticipants()).toHaveLength(2);
      expect(chat.getParticipants().map(p => p.getId())).toEqual(["user-1", "user-2"]);
    });

    it("should get participant count", () => {
      expect(chat.getParticipantCount()).toBe(2);
    });
  });

  describe("Group chat detection", () => {
    it("should identify as not a group chat for 2 participants", () => {
      const chat = Chat.create([participant1, participant2]);
      expect(chat.isGroup()).toBe(false);
    });

    it("should identify as group chat for more than 2 participants", () => {
      // Note: This test would fail with current implementation since create() only allows 2 participants
      // We'll create a chat directly for testing
      const groupChat = new Chat(
        "group-123",
        [participant1, participant2, participant3],
        new Date(),
        new Date()
      );
      expect(groupChat.isGroup()).toBe(true);
    });
  });

  describe("Last message management", () => {
    let chat: Chat;
    let message: Message;

    beforeEach(() => {
      chat = Chat.create(participants);
      message = Message.create({
        content: "Hello!",
        senderId: "user-1",
        receiverId: "user-2",
        type: MessageType.text()
      });
    });

    it("should update last message", () => {
      const updatedChat = chat.withLastMessage(message);

      expect(updatedChat.getLastMessage()).toBe(message);
      expect(updatedChat.getUpdatedAt().getTime()).toBeGreaterThanOrEqual(chat.getUpdatedAt().getTime());
      // Original chat should be unchanged
      expect(chat.getLastMessage()).toBeUndefined();
    });

    it("should detect unread messages", () => {
      const chatWithMessage = chat.withLastMessage(message);

      expect(chatWithMessage.hasUnreadMessages("user-2")).toBe(true);
      expect(chatWithMessage.hasUnreadMessages("user-1")).toBe(false);
      expect(chatWithMessage.hasUnreadMessages("user-3")).toBe(false);
    });

    it("should not detect unread messages when message is read", () => {
      const readMessage = message.markAsRead();
      const chatWithReadMessage = chat.withLastMessage(readMessage);

      expect(chatWithReadMessage.hasUnreadMessages("user-2")).toBe(false);
    });

    it("should not detect unread messages when no last message", () => {
      expect(chat.hasUnreadMessages("user-1")).toBe(false);
      expect(chat.hasUnreadMessages("user-2")).toBe(false);
    });
  });

  describe("Activity status", () => {
    it("should be active for recently created chat", () => {
      const chat = Chat.create(participants);
      expect(chat.isActive()).toBe(true);
    });

    it("should be inactive for old chat", () => {
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 31); // 31 days ago

      const oldChat = new Chat(
        "old-chat",
        participants,
        oldDate,
        oldDate
      );

      expect(oldChat.isActive()).toBe(false);
    });

    it("should be active if updated recently", () => {
      const oldCreatedDate = new Date();
      oldCreatedDate.setDate(oldCreatedDate.getDate() - 31);
      const recentUpdatedDate = new Date();

      const chat = new Chat(
        "updated-chat",
        participants,
        oldCreatedDate,
        recentUpdatedDate
      );

      expect(chat.isActive()).toBe(true);
    });
  });

  describe("Display name", () => {
    it("should return other participant name for 1-on-1 chat", () => {
      const chat = Chat.create([participant1, participant2]);

      expect(chat.getDisplayName("user-1")).toBe("Bob");
      expect(chat.getDisplayName("user-2")).toBe("Alice");
    });

    it("should return other participant name when user is not in chat", () => {
      const chat = Chat.create([participant1, participant2]);

      // When user is not in chat, returns name of first participant that doesn't match the ID
      expect(chat.getDisplayName("user-3")).toBe("Alice");
    });

    it("should return \"Utilisateur inconnu\" when no other participant found", () => {
      // Create a chat with just one participant for testing edge case
      // Since create() requires 2 participants, we'll use constructor directly
      const singleParticipantChat = new Chat(
        "single-chat",
        [participant1],
        new Date(),
        new Date()
      );

      expect(singleParticipantChat.getDisplayName("user-1")).toBe("Utilisateur inconnu");
    });

    it("should return group name for group chat", () => {
      const groupChat = new Chat(
        "group-123",
        [participant1, participant2, participant3],
        new Date(),
        new Date()
      );

      // Pour un groupe, on peut maintenant juste retourner le nom de l'autre participant principal
      // ou adapter selon la logique métier souhaitée
      expect(groupChat.getDisplayName("user-1")).toBeDefined();
    });

    it("should handle large group chat names", () => {
      const participant5 = Participant.create({
        id: "user-5",
        name: "Eve",
        email: "<EMAIL>"
      });
      
      const largeGroupChat = new Chat(
        "large-group",
        [participant1, participant2, participant3, participant4, participant5],
        new Date(),
        new Date()
      );

      const displayName = largeGroupChat.getDisplayName("user-1");
      expect(displayName).toBeDefined();
    });
  });

  describe("Immutability", () => {
    it("should create new instance when updating last message", () => {
      const originalChat = Chat.create(participants);
      const message = Message.create({
        content: "Test message",
        senderId: "user-1",
        receiverId: "user-2",
        type: MessageType.text()
      });

      const updatedChat = originalChat.withLastMessage(message);

      expect(originalChat).not.toBe(updatedChat);
      expect(originalChat.getLastMessage()).toBeUndefined();
      expect(updatedChat.getLastMessage()).toBe(message);
    });
  });

  describe("Getter methods", () => {
    let chat: Chat;

    beforeEach(() => {
      chat = Chat.create(participants);
    });

    it("should return correct ID", () => {
      expect(chat.getId()).toBe(chat.id);
    });

    it("should return copy of participants", () => {
      const participantsResult = chat.getParticipants();
      expect(participantsResult).toHaveLength(2);
      expect(participantsResult.map(p => p.getId())).toEqual(["user-1", "user-2"]);
      expect(participantsResult).not.toBe(chat.participants);
    });

    it("should return correct timestamps", () => {
      expect(chat.getCreatedAt()).toBe(chat.createdAt);
      expect(chat.getUpdatedAt()).toBe(chat.updatedAt);
    });

    it("should return participant IDs for backward compatibility", () => {
      expect(chat.participantIds).toEqual(["user-1", "user-2"]);
    });
  });
}); 
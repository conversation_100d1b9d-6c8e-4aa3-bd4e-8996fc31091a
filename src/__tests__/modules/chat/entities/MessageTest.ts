import { Message } from "@/modules/chat/domain/entities/Message";
import { MessageContent } from "@/modules/chat/domain/valueobjects/MessageContent";
import { MessageType } from "@/modules/chat/domain/valueobjects/MessageType";
import { MessageStatus } from "@/modules/chat/domain/valueobjects/MessageStatus";
import { InvalidMessageException } from "@/modules/chat/domain/exceptions/InvalidMessageException";

describe("Message", () => {
  const validMessageData = {
    content: "Hello world!",
    senderId: "user-1",
    receiverId: "user-2",
    type: MessageType.text()
  };

  describe("Factory method create", () => {
    it("should create a message with valid data", () => {
      const message = Message.create(validMessageData);

      expect(message).toBeDefined();
      expect(message.id).toBeDefined();
      expect(message.content.getValue()).toBe("Hello world!");
      expect(message.senderId).toBe("user-1");
      expect(message.receiverId).toBe("user-2");
      expect(message.type.isText()).toBe(true);
      expect(message.status.isSent()).toBe(true);
      expect(message.createdAt).toBeInstanceOf(Date);
    });

    it("should generate unique IDs for different messages", () => {
      const message1 = Message.create(validMessageData);
      const message2 = Message.create(validMessageData);

      expect(message1.id).not.toBe(message2.id);
    });

    it("should create message with different types", () => {
      const textMessage = Message.create({ ...validMessageData, type: MessageType.text() });
      const imageMessage = Message.create({ ...validMessageData, type: MessageType.image() });
      const fileMessage = Message.create({ ...validMessageData, type: MessageType.file() });

      expect(textMessage.type.isText()).toBe(true);
      expect(imageMessage.type.isImage()).toBe(true);
      expect(fileMessage.type.isFile()).toBe(true);
    });

    it("should throw error for invalid content", () => {
      expect(() => Message.create({
        ...validMessageData,
        content: ""
      })).toThrow(InvalidMessageException);
    });
  });

  describe("Constructor", () => {
    it("should create message with all properties", () => {
      const now = new Date();
      const content = new MessageContent("Test message");
      const type = MessageType.text();
      const status = MessageStatus.sent();

      const message = new Message(
        "msg-123",
        content,
        "sender-1",
        "receiver-1",
        "chat-123",
        type,
        status,
        now,
        now
      );

      expect(message.id).toBe("msg-123");
      expect(message.content).toBe(content);
      expect(message.senderId).toBe("sender-1");
      expect(message.receiverId).toBe("receiver-1");
      expect(message.chatId).toBe("chat-123");
      expect(message.type).toBe(type);
      expect(message.status).toBe(status);
      expect(message.createdAt).toBe(now);
      expect(message.updatedAt).toBe(now);
    });
  });

  describe("Status management", () => {
    let message: Message;

    beforeEach(() => {
      message = Message.create(validMessageData);
    });

    it("should mark message as read", async () => {
      // Add a small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 1));
      const readMessage = message.markAsRead();

      expect(readMessage.status.isRead()).toBe(true);
      expect(readMessage.updatedAt).toBeInstanceOf(Date);
      expect(readMessage.updatedAt!.getTime()).toBeGreaterThanOrEqual(message.createdAt.getTime());
      // Original message should be unchanged
      expect(message.status.isSent()).toBe(true);
    });

    it("should mark message as delivered", async () => {
      // Add a small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 1));
      const deliveredMessage = message.markAsDelivered();

      expect(deliveredMessage.status.isDelivered()).toBe(true);
      expect(deliveredMessage.updatedAt).toBeInstanceOf(Date);
      expect(deliveredMessage.updatedAt!.getTime()).toBeGreaterThanOrEqual(message.createdAt.getTime());
      // Original message should be unchanged
      expect(message.status.isSent()).toBe(true);
    });
  });

  describe("User relationship methods", () => {
    let message: Message;

    beforeEach(() => {
      message = Message.create(validMessageData);
    });

    it("should identify if message is from user", () => {
      expect(message.isFromUser("user-1")).toBe(true);
      expect(message.isFromUser("user-2")).toBe(false);
      expect(message.isFromUser("user-3")).toBe(false);
    });

    it("should identify if message is to user", () => {
      expect(message.isToUser("user-2")).toBe(true);
      expect(message.isToUser("user-1")).toBe(false);
      expect(message.isToUser("user-3")).toBe(false);
    });
  });

  describe("Time-based methods", () => {
    it("should check if message was sent after a specific date", () => {
      const pastDate = new Date("2023-01-01");
      const futureDate = new Date("2030-01-01");
      const message = Message.create(validMessageData);

      expect(message.isSentAfter(pastDate)).toBe(true);
      expect(message.isSentAfter(futureDate)).toBe(false);
    });

    it("should format time correctly", () => {
      const message = Message.create(validMessageData);
      const formattedTime = message.getFormattedTime();

      expect(formattedTime).toMatch(/\d{1,2}:\d{2} [AP]M/);
    });

    it("should determine if message can be edited", () => {
      const recentMessage = Message.create(validMessageData);
      expect(recentMessage.canBeEdited()).toBe(true);

      // Create an old message by manipulating the created date
      const oldDate = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago
      const oldMessage = new Message(
        "old-msg",
        new MessageContent("Old message"),
        "user-1",
        "user-2",
        "chat-1",
        MessageType.text(),
        MessageStatus.sent(),
        oldDate
      );
      expect(oldMessage.canBeEdited()).toBe(false);
    });
  });

  describe("Getter methods", () => {
    let message: Message;

    beforeEach(() => {
      message = Message.create(validMessageData);
    });

    it("should return correct ID", () => {
      expect(message.getId()).toBe(message.id);
    });

    it("should return correct content", () => {
      expect(message.getContent()).toBe(message.content);
    });

    it("should return correct sender ID", () => {
      expect(message.getSenderId()).toBe("user-1");
    });

    it("should return correct receiver ID", () => {
      expect(message.getReceiverId()).toBe("user-2");
    });

    it("should return correct status", () => {
      expect(message.getStatus()).toBe(message.status);
    });

    it("should return correct type", () => {
      expect(message.getType()).toBe(message.type);
    });

    it("should return correct timestamp", () => {
      expect(message.getTimestamp()).toBe(message.createdAt);
    });
  });

  describe("Immutability", () => {
    it("should create new instances when changing status", () => {
      const originalMessage = Message.create(validMessageData);
      const readMessage = originalMessage.markAsRead();
      const deliveredMessage = originalMessage.markAsDelivered();

      expect(originalMessage).not.toBe(readMessage);
      expect(originalMessage).not.toBe(deliveredMessage);
      expect(readMessage).not.toBe(deliveredMessage);

      // Original should remain unchanged
      expect(originalMessage.status.isSent()).toBe(true);
      expect(readMessage.status.isRead()).toBe(true);
      expect(deliveredMessage.status.isDelivered()).toBe(true);
    });
  });

  describe("Edge cases", () => {
    it("should handle messages with system type", () => {
      const systemMessage = Message.create({
        ...validMessageData,
        type: MessageType.system(),
        content: "User joined the chat"
      });

      expect(systemMessage.type.isSystem()).toBe(true);
      expect(systemMessage.content.getValue()).toBe("User joined the chat");
    });

    it("should handle very long content (within limits)", () => {
      const longContent = "a".repeat(4999); // Just under the 5000 limit
      const message = Message.create({
        ...validMessageData,
        content: longContent
      });

      expect(message.content.getValue()).toBe(longContent);
      expect(message.content.getLength()).toBe(4999);
    });
  });
}); 
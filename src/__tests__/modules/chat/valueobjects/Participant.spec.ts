import { Participant } from '../../../../modules/chat/domain/valueobjects/Participant';

describe('Participant', () => {
  describe('create', () => {
    it('should create a participant with valid data', () => {
      const participantData = {
        id: 'user-123',
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: 'https://avatar.com/john.jpg',
        isOnline: true
      };

      const participant = Participant.create(participantData);

      expect(participant.getId()).toBe('user-123');
      expect(participant.getName()).toBe('<PERSON>');
      expect(participant.getEmail()).toBe('<EMAIL>');
      expect(participant.getAvatar()).toBe('https://avatar.com/john.jpg');
      expect(participant.isUserOnline()).toBe(true);
    });

    it('should create a participant without optional fields', () => {
      const participantData = {
        id: 'user-456',
        name: '<PERSON>',
        email: '<EMAIL>'
      };

      const participant = Participant.create(participantData);

      expect(participant.getId()).toBe('user-456');
      expect(participant.getName()).toBe('Jane Smith');
      expect(participant.getEmail()).toBe('<EMAIL>');
      expect(participant.getAvatar()).toBeUndefined();
      expect(participant.isUserOnline()).toBe(false);
    });

    it('should create a participant with explicit offline status', () => {
      const participantData = {
        id: 'user-789',
        name: 'Bob Wilson',
        email: '<EMAIL>',
        isOnline: false
      };

      const participant = Participant.create(participantData);

      expect(participant.getId()).toBe('user-789');
      expect(participant.getName()).toBe('Bob Wilson');
      expect(participant.getEmail()).toBe('<EMAIL>');
      expect(participant.isUserOnline()).toBe(false);
    });

    it('should handle undefined isOnline as offline', () => {
      const participantData = {
        id: 'user-999',
        name: 'Alice Brown',
        email: '<EMAIL>',
        isOnline: undefined
      };

      const participant = Participant.create(participantData);

      expect(participant.isUserOnline()).toBe(false);
    });

    it('should throw error when id is empty', () => {
      const participantData = {
        id: '',
        name: 'John Doe',
        email: '<EMAIL>'
      };

      expect(() => Participant.create(participantData)).toThrow('Participant ID cannot be empty');
    });

    it('should throw error when name is empty', () => {
      const participantData = {
        id: 'user-123',
        name: '',
        email: '<EMAIL>'
      };

      expect(() => Participant.create(participantData)).toThrow('Participant name cannot be empty');
    });

    it('should throw error when email is empty', () => {
      const participantData = {
        id: 'user-123',
        name: 'John Doe',
        email: ''
      };

      expect(() => Participant.create(participantData)).toThrow('Participant email cannot be empty');
    });
  });

  describe('getDisplayInfo', () => {
    it('should return formatted display info', () => {
      const participant = Participant.create({
        id: 'user-123',
        name: 'John Doe',
        email: '<EMAIL>'
      });

      expect(participant.getDisplayInfo()).toBe('John Doe (<EMAIL>)');
    });
  });

  describe('equals', () => {
    it('should return true for participants with same id', () => {
      const participant1 = Participant.create({
        id: 'user-123',
        name: 'John Doe',
        email: '<EMAIL>'
      });

      const participant2 = Participant.create({
        id: 'user-123',
        name: 'Different Name',
        email: '<EMAIL>'
      });

      expect(participant1.equals(participant2)).toBe(true);
    });

    it('should return false for participants with different id', () => {
      const participant1 = Participant.create({
        id: 'user-123',
        name: 'John Doe',
        email: '<EMAIL>'
      });

      const participant2 = Participant.create({
        id: 'user-456',
        name: 'John Doe',
        email: '<EMAIL>'
      });

      expect(participant1.equals(participant2)).toBe(false);
    });
  });
}); 
import { MessageContent } from '@/modules/chat/domain/valueobjects/MessageContent';
import { InvalidMessageException } from '@/modules/chat/domain/exceptions/InvalidMessageException';

describe('MessageContent', () => {
  describe('Valid content creation', () => {
    it('should create content with valid text', () => {
      const content = new MessageContent('Hello world!');
      expect(content.getValue()).toBe('Hello world!');
    });

    it('should trim whitespace from content', () => {
      const content = new MessageContent('  Hello world!  ');
      expect(content.getValue()).toBe('Hello world!');
    });

    it('should accept content up to 5000 characters', () => {
      const longContent = 'a'.repeat(5000);
      const content = new MessageContent(longContent);
      expect(content.getValue()).toBe(longContent);
      expect(content.getLength()).toBe(5000);
    });

    it('should accept single character content', () => {
      const content = new MessageContent('a');
      expect(content.getValue()).toBe('a');
      expect(content.getLength()).toBe(1);
    });
  });

  describe('Validation', () => {
    it('should throw error for empty content', () => {
      expect(() => new MessageContent('')).toThrow(InvalidMessageException);
      expect(() => new MessageContent('')).toThrow('Message content cannot be empty');
    });

    it('should throw error for whitespace-only content', () => {
      expect(() => new MessageContent('   ')).toThrow(InvalidMessageException);
      expect(() => new MessageContent('   ')).toThrow('Message content cannot be empty');
    });

    it('should throw error for content exceeding 5000 characters', () => {
      const tooLongContent = 'a'.repeat(5001);
      expect(() => new MessageContent(tooLongContent)).toThrow(InvalidMessageException);
      expect(() => new MessageContent(tooLongContent)).toThrow('Message content cannot exceed 5000 characters');
    });

    it('should throw error for null content', () => {
      expect(() => new MessageContent(null as any)).toThrow(InvalidMessageException);
    });

    it('should throw error for undefined content', () => {
      expect(() => new MessageContent(undefined as any)).toThrow(InvalidMessageException);
    });
  });

  describe('Content methods', () => {
    it('should return correct length', () => {
      const content = new MessageContent('Hello');
      expect(content.getLength()).toBe(5);
    });

    it('should check if content is empty (this should never happen due to validation)', () => {
      const content = new MessageContent('Hello');
      expect(content.isEmpty()).toBe(false);
    });

    it('should check if content contains text', () => {
      const content = new MessageContent('Hello world!');
      expect(content.contains('world')).toBe(true);
      expect(content.contains('WORLD')).toBe(true); // case insensitive
      expect(content.contains('foo')).toBe(false);
    });

    it('should check if content starts with text', () => {
      const content = new MessageContent('Hello world!');
      expect(content.startsWith('Hello')).toBe(true);
      expect(content.startsWith('world')).toBe(false);
    });

    it('should check if content ends with text', () => {
      const content = new MessageContent('Hello world!');
      expect(content.endsWith('world!')).toBe(true);
      expect(content.endsWith('Hello')).toBe(false);
    });

    it('should get word count', () => {
      const content = new MessageContent('Hello beautiful world!');
      expect(content.getWordCount()).toBe(3);
    });

    it('should handle single word', () => {
      const content = new MessageContent('Hello');
      expect(content.getWordCount()).toBe(1);
    });

    it('should handle multiple spaces', () => {
      const content = new MessageContent('Hello    world!');
      expect(content.getWordCount()).toBe(2);
    });
  });

  describe('Preview functionality', () => {
    it('should return full content when shorter than max length', () => {
      const content = new MessageContent('Short message');
      expect(content.getPreview(50)).toBe('Short message');
    });

    it('should truncate content when longer than max length', () => {
      const content = new MessageContent('This is a very long message that should be truncated');
      expect(content.getPreview(20)).toBe('This is a very long ...');
    });

    it('should use default max length of 100', () => {
      const longContent = 'a'.repeat(150);
      const content = new MessageContent(longContent);
      const preview = content.getPreview();
      expect(preview.length).toBe(103); // 100 + '...'
      expect(preview.endsWith('...')).toBe(true);
    });
  });

  describe('URL detection', () => {
    it('should detect HTTP URLs', () => {
      const content = new MessageContent('Check this out: http://example.com');
      expect(content.hasUrl()).toBe(true);
      expect(content.extractUrls()).toEqual(['http://example.com']);
    });

    it('should detect HTTPS URLs', () => {
      const content = new MessageContent('Visit https://secure-site.com for more info');
      expect(content.hasUrl()).toBe(true);
      expect(content.extractUrls()).toEqual(['https://secure-site.com']);
    });

    it('should detect multiple URLs', () => {
      const content = new MessageContent('Check http://site1.com and https://site2.com');
      expect(content.hasUrl()).toBe(true);
      expect(content.extractUrls()).toEqual(['http://site1.com', 'https://site2.com']);
    });

    it('should return false when no URLs', () => {
      const content = new MessageContent('Just a regular message');
      expect(content.hasUrl()).toBe(false);
      expect(content.extractUrls()).toEqual([]);
    });

    it('should handle URLs with query parameters', () => {
      const content = new MessageContent('Search: https://google.com/search?q=test');
      expect(content.hasUrl()).toBe(true);
      expect(content.extractUrls()).toEqual(['https://google.com/search?q=test']);
    });
  });

  describe('Equality', () => {
    it('should be equal when same content', () => {
      const content1 = new MessageContent('Hello world!');
      const content2 = new MessageContent('Hello world!');
      expect(content1.equals(content2)).toBe(true);
    });

    it('should not be equal when different content', () => {
      const content1 = new MessageContent('Hello world!');
      const content2 = new MessageContent('Goodbye world!');
      expect(content1.equals(content2)).toBe(false);
    });

    it('should be equal after trimming', () => {
      const content1 = new MessageContent('Hello world!');
      const content2 = new MessageContent('  Hello world!  ');
      expect(content1.equals(content2)).toBe(true);
    });
  });

  describe('String conversion', () => {
    it('should convert to string correctly', () => {
      const content = new MessageContent('Hello world!');
      expect(content.toString()).toBe('Hello world!');
    });
  });
}); 
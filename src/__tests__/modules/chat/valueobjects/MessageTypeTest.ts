import { MessageType, MessageTypeEnum } from '@/modules/chat/domain/valueobjects/MessageType';

describe('MessageType', () => {
  describe('Factory methods', () => {
    it('should create a TEXT message type', () => {
      const messageType = MessageType.text();
      
      expect(messageType.getValue()).toBe(MessageTypeEnum.TEXT);
      expect(messageType.isText()).toBe(true);
      expect(messageType.isImage()).toBe(false);
      expect(messageType.isFile()).toBe(false);
      expect(messageType.isSystem()).toBe(false);
    });

    it('should create an IMAGE message type', () => {
      const messageType = MessageType.image();
      
      expect(messageType.getValue()).toBe(MessageTypeEnum.IMAGE);
      expect(messageType.isText()).toBe(false);
      expect(messageType.isImage()).toBe(true);
      expect(messageType.isFile()).toBe(false);
      expect(messageType.isSystem()).toBe(false);
    });

    it('should create a FILE message type', () => {
      const messageType = MessageType.file();
      
      expect(messageType.getValue()).toBe(MessageTypeEnum.FILE);
      expect(messageType.isText()).toBe(false);
      expect(messageType.isImage()).toBe(false);
      expect(messageType.isFile()).toBe(true);
      expect(messageType.isSystem()).toBe(false);
    });

    it('should create a SYSTEM message type', () => {
      const messageType = MessageType.system();
      
      expect(messageType.getValue()).toBe(MessageTypeEnum.SYSTEM);
      expect(messageType.isText()).toBe(false);
      expect(messageType.isImage()).toBe(false);
      expect(messageType.isFile()).toBe(false);
      expect(messageType.isSystem()).toBe(true);
    });
  });

  describe('Constructor', () => {
    it('should create message type with valid enum value', () => {
      const messageType = new MessageType(MessageTypeEnum.TEXT);
      expect(messageType.getValue()).toBe(MessageTypeEnum.TEXT);
    });

    it('should accept all valid enum values', () => {
      Object.values(MessageTypeEnum).forEach(type => {
        const messageType = new MessageType(type);
        expect(messageType.getValue()).toBe(type);
      });
    });
  });

  describe('Equality', () => {
    it('should be equal when same type', () => {
      const type1 = MessageType.text();
      const type2 = MessageType.text();
      
      expect(type1.equals(type2)).toBe(true);
    });

    it('should not be equal when different types', () => {
      const type1 = MessageType.text();
      const type2 = MessageType.image();
      
      expect(type1.equals(type2)).toBe(false);
    });
  });

  describe('String conversion', () => {
    it('should convert to string correctly', () => {
      const messageType = MessageType.text();
      expect(messageType.toString()).toBe('text');
    });

    it('should convert all types to string correctly', () => {
      expect(MessageType.text().toString()).toBe('text');
      expect(MessageType.image().toString()).toBe('image');
      expect(MessageType.file().toString()).toBe('file');
      expect(MessageType.system().toString()).toBe('system');
    });
  });

  describe('Type checking', () => {
    it('should correctly identify text messages', () => {
      const textType = MessageType.text();
      const imageType = MessageType.image();
      
      expect(textType.isText()).toBe(true);
      expect(imageType.isText()).toBe(false);
    });

    it('should correctly identify image messages', () => {
      const textType = MessageType.text();
      const imageType = MessageType.image();
      
      expect(textType.isImage()).toBe(false);
      expect(imageType.isImage()).toBe(true);
    });

    it('should correctly identify file messages', () => {
      const textType = MessageType.text();
      const fileType = MessageType.file();
      
      expect(textType.isFile()).toBe(false);
      expect(fileType.isFile()).toBe(true);
    });

    it('should correctly identify system messages', () => {
      const textType = MessageType.text();
      const systemType = MessageType.system();
      
      expect(textType.isSystem()).toBe(false);
      expect(systemType.isSystem()).toBe(true);
    });
  });
}); 
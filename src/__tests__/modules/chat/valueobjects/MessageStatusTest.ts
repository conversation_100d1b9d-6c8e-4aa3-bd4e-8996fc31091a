import { MessageStatus, MessageStatusEnum } from '@/modules/chat/domain/valueobjects/MessageStatus';

describe('MessageStatus', () => {
  describe('Factory methods', () => {
    it('should create a SENT status', () => {
      const status = MessageStatus.sent();
      
      expect(status.getValue()).toBe(MessageStatusEnum.SENT);
      expect(status.isSent()).toBe(true);
      expect(status.isDelivered()).toBe(false);
      expect(status.isRead()).toBe(false);
    });

    it('should create a DELIVERED status', () => {
      const status = MessageStatus.delivered();
      
      expect(status.getValue()).toBe(MessageStatusEnum.DELIVERED);
      expect(status.isSent()).toBe(false);
      expect(status.isDelivered()).toBe(true);
      expect(status.isRead()).toBe(false);
    });

    it('should create a READ status', () => {
      const status = MessageStatus.read();
      
      expect(status.getValue()).toBe(MessageStatusEnum.READ);
      expect(status.isSent()).toBe(false);
      expect(status.isDelivered()).toBe(false);
      expect(status.isRead()).toBe(true);
    });
  });

  describe('Constructor', () => {
    it('should create status with valid enum value', () => {
      const status = new MessageStatus(MessageStatusEnum.SENT);
      expect(status.getValue()).toBe(MessageStatusEnum.SENT);
    });

    it('should accept all valid enum values', () => {
      Object.values(MessageStatusEnum).forEach(statusValue => {
        const status = new MessageStatus(statusValue);
        expect(status.getValue()).toBe(statusValue);
      });
    });
  });

  describe('Status checking', () => {
    it('should correctly identify sent status', () => {
      const sentStatus = MessageStatus.sent();
      const deliveredStatus = MessageStatus.delivered();
      const readStatus = MessageStatus.read();
      
      expect(sentStatus.isSent()).toBe(true);
      expect(deliveredStatus.isSent()).toBe(false);
      expect(readStatus.isSent()).toBe(false);
    });

    it('should correctly identify delivered status', () => {
      const sentStatus = MessageStatus.sent();
      const deliveredStatus = MessageStatus.delivered();
      const readStatus = MessageStatus.read();
      
      expect(sentStatus.isDelivered()).toBe(false);
      expect(deliveredStatus.isDelivered()).toBe(true);
      expect(readStatus.isDelivered()).toBe(false);
    });

    it('should correctly identify read status', () => {
      const sentStatus = MessageStatus.sent();
      const deliveredStatus = MessageStatus.delivered();
      const readStatus = MessageStatus.read();
      
      expect(sentStatus.isRead()).toBe(false);
      expect(deliveredStatus.isRead()).toBe(false);
      expect(readStatus.isRead()).toBe(true);
    });
  });

  describe('Display text', () => {
    it('should return correct display text for SENT', () => {
      const status = MessageStatus.sent();
      expect(status.getDisplayText()).toBe('Sent');
    });

    it('should return correct display text for DELIVERED', () => {
      const status = MessageStatus.delivered();
      expect(status.getDisplayText()).toBe('Delivered');
    });

    it('should return correct display text for READ', () => {
      const status = MessageStatus.read();
      expect(status.getDisplayText()).toBe('Read');
    });
  });

  describe('Icons', () => {
    it('should return correct icon for SENT', () => {
      const status = MessageStatus.sent();
      expect(status.getIcon()).toBe('✓');
    });

    it('should return correct icon for DELIVERED', () => {
      const status = MessageStatus.delivered();
      expect(status.getIcon()).toBe('✓✓');
    });

    it('should return correct icon for READ', () => {
      const status = MessageStatus.read();
      expect(status.getIcon()).toBe('✓✓');
    });
  });

  describe('Timestamp visibility', () => {
    it('should show timestamp for READ messages', () => {
      const readStatus = MessageStatus.read();
      expect(readStatus.shouldShowTimestamp()).toBe(true);
    });

    it('should not show timestamp for sent messages', () => {
      const sentStatus = MessageStatus.sent();
      expect(sentStatus.shouldShowTimestamp()).toBe(false);
    });

    it('should not show timestamp for delivered messages', () => {
      const deliveredStatus = MessageStatus.delivered();
      expect(deliveredStatus.shouldShowTimestamp()).toBe(false);
    });
  });

  describe('Equality', () => {
    it('should be equal when same status', () => {
      const status1 = MessageStatus.sent();
      const status2 = MessageStatus.sent();
      
      expect(status1.equals(status2)).toBe(true);
    });

    it('should not be equal when different status', () => {
      const status1 = MessageStatus.sent();
      const status2 = MessageStatus.delivered();
      
      expect(status1.equals(status2)).toBe(false);
    });

    it('should be equal when created with same enum value', () => {
      const status1 = new MessageStatus(MessageStatusEnum.READ);
      const status2 = MessageStatus.read();
      
      expect(status1.equals(status2)).toBe(true);
    });
  });

  describe('String conversion', () => {
    it('should convert to string correctly', () => {
      const status = MessageStatus.sent();
      expect(status.toString()).toBe('sent');
    });

    it('should convert all status to string correctly', () => {
      expect(MessageStatus.sent().toString()).toBe('sent');
      expect(MessageStatus.delivered().toString()).toBe('delivered');
      expect(MessageStatus.read().toString()).toBe('read');
    });
  });

  describe('Status progression logic', () => {
    it('should represent correct progression order', () => {
      // Test that the enum values represent the logical progression
      const sent = MessageStatus.sent();
      const delivered = MessageStatus.delivered();
      const read = MessageStatus.read();
      
      // These should be mutually exclusive
      expect(sent.isSent() && !sent.isDelivered() && !sent.isRead()).toBe(true);
      expect(!delivered.isSent() && delivered.isDelivered() && !delivered.isRead()).toBe(true);
      expect(!read.isSent() && !read.isDelivered() && read.isRead()).toBe(true);
    });
  });
}); 
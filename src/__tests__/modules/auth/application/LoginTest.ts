import { Login } from "@/modules/auth/application/usecases/Login";
import { InvalidCredentials } from "@/modules/auth/domain/exceptions/InvalidCredentials";
import { InvalidEmailException } from "@/modules/auth/domain/exceptions/InvalidEmail";
import { InvalidPasswordException } from "@/modules/auth/domain/exceptions/InvalidPassword";
import { AuthUserRepository } from "@/modules/auth/domain/repositories/AuthUserRepository";
import { mock, instance, when, anything } from "ts-mockito";

describe("Login", () => {
  let mockAuthUserRepository: AuthUserRepository;
  let authUserRepositoryInstance: AuthUserRepository;

  beforeEach(() => {
    mockAuthUserRepository = mock<AuthUserRepository>();
    authUserRepositoryInstance = instance(mockAuthUserRepository);
  });

  it("should login a user", async () => {
    when(mockAuthUserRepository.login(anything(), anything())).thenResolve("fake-jwt-token");
    
    const login = new Login(authUserRepositoryInstance);

    const result = await login.execute("<EMAIL>", "Password123!");

    expect(result).toBeDefined();
    expect(result.token).toBeDefined();
    expect(result.token).toBe("fake-jwt-token");
  });

  it("should throw an error if the email is invalid", async () => {
    const login = new Login(authUserRepositoryInstance);
    
    await expect(login.execute("invalid-email", "Password123!"))
      .rejects.toThrow(InvalidEmailException);
  });

  it("should throw an error if the password is invalid", async () => {
    const login = new Login(authUserRepositoryInstance);

    await expect(login.execute("<EMAIL>", "invalid-password"))
      .rejects.toThrow(InvalidPasswordException);
  });

  it("should throw an error for bad credentials", async () => {
    when(mockAuthUserRepository.login(anything(), anything())).thenResolve(null);

    const login = new Login(authUserRepositoryInstance);

    await expect(login.execute("<EMAIL>", "Password123!"))
      .rejects.toThrow(InvalidCredentials);
  });
});
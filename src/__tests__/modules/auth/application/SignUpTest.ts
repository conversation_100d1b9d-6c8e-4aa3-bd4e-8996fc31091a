import { SignUp } from "@/modules/auth/application/usecases/SignUp";
import { EmailAlreadyTaken } from "@/modules/auth/domain/exceptions/EmailAlreadyTaken";
import { InvalidEmailException } from "@/modules/auth/domain/exceptions/InvalidEmail";
import { InvalidPasswordException } from "@/modules/auth/domain/exceptions/InvalidPassword";
import { InvalidUsernameException } from "@/modules/auth/domain/exceptions/InvalidUsername";
import { AuthUserRepository } from "@/modules/auth/domain/repositories/AuthUserRepository";
import { anything, instance, when, verify, mock } from "ts-mockito";

describe("SignUp", () => {
  let mockAuthUserRepository: AuthUserRepository;
  let authUserRepositoryInstance: AuthUserRepository;

  beforeEach(() => {
    mockAuthUserRepository = mock<AuthUserRepository>();
    authUserRepositoryInstance = instance(mockAuthUserRepository);
  });

  it("should sign up a user", async () => {
    when(mockAuthUserRepository.createUser(anything(), anything(), anything())).thenResolve();
    const signUp = new SignUp(authUserRepositoryInstance);

    await signUp.execute("<EMAIL>", "Password123!", "testuser");

    verify(mockAuthUserRepository.createUser(anything(), anything(), anything())).once();
  });

  it("should throw an error if the email is invalid", async () => {
    const signUp = new SignUp(authUserRepositoryInstance);

    await expect(signUp.execute("invalid-email", "Password123!", "testuser"))
      .rejects.toThrow(InvalidEmailException);
  });

  it("should throw an error if the password is invalid", async () => {
    const signUp = new SignUp(authUserRepositoryInstance);

    await expect(signUp.execute("<EMAIL>", "invalid-password", "testuser"))
      .rejects.toThrow(InvalidPasswordException);
  });

  it("should throw an error if the username is invalid", async () => {
    const signUp = new SignUp(authUserRepositoryInstance);

    await expect(signUp.execute("<EMAIL>", "Password123!", "ab"))
      .rejects.toThrow(InvalidUsernameException);
  });

  it("should throw an error if the email is already taken", async () => {
    when(mockAuthUserRepository.createUser(anything(), anything(), anything()))
      .thenReject(new EmailAlreadyTaken("Email aleady taken"));
    const signUp = new SignUp(authUserRepositoryInstance);

    await expect(signUp.execute("<EMAIL>", "Password123!", "testuser"))
      .rejects.toThrow(EmailAlreadyTaken);
  });
});
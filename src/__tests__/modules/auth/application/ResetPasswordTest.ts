import { ResetPassword } from "@/modules/auth/application/usecases/ResetPassword";
import { InvalidResetPasswordToken } from "@/modules/auth/domain/exceptions/InvalidResetPasswordToken";
import { Password } from "@/modules/auth/domain/valueobjects/Password";

const mockAuthRepository = {
  resetPassword: jest.fn(),
  login: jest.fn(),
  createUser: jest.fn(),
  forgotPassword: jest.fn(),
  checkResetPasswordToken: jest.fn(),
};

describe("ResetPassword", () => {
  it("should reset the password", async () => {
    mockAuthRepository.checkResetPasswordToken.mockResolvedValue(true);
    const resetPassword = new ResetPassword(mockAuthRepository, "token");

    await resetPassword.execute(new Password("Password123!"));

    expect(mockAuthRepository.checkResetPasswordToken).toHaveBeenCalledWith(
      "token"
    );
    expect(mockAuthRepository.resetPassword).toHaveBeenCalledWith(
      "token",
      new Password("Password123!")
    );
  });

  it("should throw an error if the token is invalid", async () => {
    mockAuthRepository.checkResetPasswordToken.mockResolvedValue(false);
    const resetPassword = new ResetPassword(mockAuthRepository, "token");

    await expect(
      resetPassword.execute(new Password("Password123!"))
    ).rejects.toThrow(InvalidResetPasswordToken);
    expect(mockAuthRepository.checkResetPasswordToken).toHaveBeenCalledWith(
      "token"
    );
  });
});

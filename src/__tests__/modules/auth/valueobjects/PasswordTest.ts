import { InvalidPasswordException } from "@/modules/auth/domain/exceptions/InvalidPassword";
import { PasswordTooShortException } from "@/modules/auth/domain/exceptions/PasswordTooShort";
import { Password } from "@/modules/auth/domain/valueobjects/Password";

describe("Password", () => {
  it("should create a password", () => {
    const password = new Password("Password123!");
    expect(password).toBeDefined();
  });

  it("should throw an error if the password is too short", () => {
    expect(() => new Password("1234567")).toThrow(PasswordTooShortException);
  });

  it("should throw an error if the password does not contain at least one uppercase letter", () => {
    expect(() => new Password("password123!")).toThrow(InvalidPasswordException);
  });

  it("should throw an error if the password does not contain at least one lowercase letter", () => {
    expect(() => new Password("PASSWORD123!")).toThrow(InvalidPasswordException);
  });

  it("should throw an error if the password does not contain at least one number", () => {
    expect(() => new Password("Password!")).toThrow(InvalidPasswordException);
  });

  it("should throw an error if the password does not contain at least one special character", () => {
    expect(() => new Password("Password123")).toThrow(InvalidPasswordException);
  });

  it("should accept password with dot as special character", () => {
    const password = new Password("Password123.");
    expect(password.getValue()).toBe("Password123.");
  });

  it("should accept password with various special characters", () => {
    expect(() => new Password("Password123.")).not.toThrow();
    expect(() => new Password("Password123_")).not.toThrow();
    expect(() => new Password("Password123-")).not.toThrow();
    expect(() => new Password("Password123+")).not.toThrow();
    expect(() => new Password("Password123=")).not.toThrow();
  });
});
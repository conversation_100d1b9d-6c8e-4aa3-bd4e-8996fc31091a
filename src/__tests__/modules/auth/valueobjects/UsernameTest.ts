import { InvalidUsernameException } from "@/modules/auth/domain/exceptions/InvalidUsername";
import { Username } from "@/modules/auth/domain/valueobjects/Username";

describe("Username", () => {
  it("should create a username", () => {
    const username = new Username("testuser");
    expect(username).toBeDefined();
    expect(username.getValue()).toBe("testuser");
  });

  it("should throw an error if the username is empty", () => {
    expect(() => new Username("")).toThrow(InvalidUsernameException);
    expect(() => new Username("   ")).toThrow(InvalidUsernameException);
  });

  it("should throw an error if the username is too short", () => {
    expect(() => new Username("ab")).toThrow(InvalidUsernameException);
  });

  it("should throw an error if the username is too long", () => {
    const longUsername = "a".repeat(21);
    expect(() => new Username(longUsername)).toThrow(InvalidUsernameException);
  });

  it("should throw an error if the username contains invalid characters", () => {
    expect(() => new Username("test@user")).toThrow(InvalidUsernameException);
    expect(() => new Username("test user")).toThrow(InvalidUsernameException);
    expect(() => new Username("test-user")).toThrow(InvalidUsernameException);
  });

  it("should accept valid usernames with letters, numbers and underscores", () => {
    expect(new Username("test_user123").getValue()).toBe("test_user123");
    expect(new Username("USER123").getValue()).toBe("USER123");
    expect(new Username("user_name").getValue()).toBe("user_name");
  });

  it("should check equality", () => {
    const username1 = new Username("testuser");
    const username2 = new Username("testuser");
    const username3 = new Username("otheruser");

    expect(username1.equals(username2)).toBe(true);
    expect(username1.equals(username3)).toBe(false);
  });

  it("should return string representation", () => {
    const username = new Username("testuser");
    expect(username.toString()).toBe("testuser");
  });
}); 
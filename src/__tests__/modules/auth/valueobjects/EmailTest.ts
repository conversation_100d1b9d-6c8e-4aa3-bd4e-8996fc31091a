import { InvalidEmailException } from "@/modules/auth/domain/exceptions/InvalidEmail";
import { Email } from "@/modules/auth/domain/valueobjects/Email";

describe("Email", () => {
  it("should create an email", () => {
    const email = new Email("<EMAIL>");
    expect(email).toBeDefined();
  });

  it("should throw an error if the email is invalid", () => {
    expect(() => new Email("invalid-email")).toThrow(InvalidEmailException);
  });

  it("should throw an error if the email is empty", () => {
    expect(() => new Email("")).toThrow(InvalidEmailException);
  });
});
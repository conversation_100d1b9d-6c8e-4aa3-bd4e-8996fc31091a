/**
 * Test pour vérifier que le logout ferme correctement la connexion WebSocket
 */

// Test pour la gestion WebSocket lors de la déconnexion
// Ajouter un export vide pour satisfaire isolatedModules
export {};

describe('Logout WebSocket Handling', () => {
  beforeEach(() => {
    // Setup spy for console.log to verify that the logout event is handled
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should emit auth:logout event when logout is called', () => {
    // Arrange
    const eventSpy = jest.spyOn(window, 'dispatchEvent');

    // Act
    window.dispatchEvent(new CustomEvent('auth:logout'));

    // Assert
    expect(eventSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'auth:logout'
      })
    );
  });

  it('should handle auth:logout event', () => {
    // Arrange
    const handleLogout = jest.fn();
    window.addEventListener('auth:logout', handleLogout);

    // Act
    window.dispatchEvent(new CustomEvent('auth:logout'));

    // Assert
    expect(handleLogout).toHaveBeenCalled();

    // Cleanup
    window.removeEventListener('auth:logout', handleLogout);
  });
}); 
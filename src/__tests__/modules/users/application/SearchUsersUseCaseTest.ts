import { SearchUsersUseCase } from "@/modules/users/application/usecases/SearchUsersUseCase";
import { UserRepository } from "@/modules/users/domain/repositories/UserRepository";
import { User } from "@/modules/users/domain/entities/User";
import { mock, instance, when, anything } from "ts-mockito";

describe("SearchUsersUseCase", () => {
  let mockUserRepository: UserRepository;
  let userRepositoryInstance: UserRepository;
  let searchUsersUseCase: SearchUsersUseCase;

  beforeEach(() => {
    mockUserRepository = mock<UserRepository>();
    userRepositoryInstance = instance(mockUserRepository);
    searchUsersUseCase = new SearchUsersUseCase(userRepositoryInstance);
  });

  describe("execute", () => {
    it("should return users when valid search term is provided", async () => {
      const mockUsers = [
        User.create({
          id: "user-1",
          username: "alice_martin",
          email: "<EMAIL>",
          isOnline: true
        }),
        User.create({
          id: "user-2",
          username: "bob_alice",
          email: "<EMAIL>",
          isOnline: false
        })
      ];

      when(mockUserRepository.searchUsers("alice")).thenResolve(mockUsers);

      const result = await searchUsersUseCase.execute({ searchTerm: "alice" });

      expect(result.users).toHaveLength(2);
      expect(result.users[0].username).toBe("alice_martin");
      expect(result.users[1].username).toBe("bob_alice");
    });

    it("should return empty array when no users found", async () => {
      when(mockUserRepository.searchUsers("nonexistent")).thenResolve([]);

      const result = await searchUsersUseCase.execute({ searchTerm: "nonexistent" });

      expect(result.users).toHaveLength(0);
    });

    it("should return empty array when search term is too short", async () => {
      const result = await searchUsersUseCase.execute({ searchTerm: "a" });

      expect(result.users).toHaveLength(0);
    });

    it("should return empty array when search term is empty", async () => {
      const result = await searchUsersUseCase.execute({ searchTerm: "" });

      expect(result.users).toHaveLength(0);
    });

    it("should return empty array when search term is only whitespace", async () => {
      const result = await searchUsersUseCase.execute({ searchTerm: "   " });

      expect(result.users).toHaveLength(0);
    });

    it("should trim search term before processing", async () => {
      const mockUsers = [
        User.create({
          id: "user-1",
          username: "alice_martin",
          email: "<EMAIL>"
        })
      ];

      when(mockUserRepository.searchUsers("alice")).thenResolve(mockUsers);

      const result = await searchUsersUseCase.execute({ searchTerm: "  alice  " });

      expect(result.users).toHaveLength(1);
      expect(result.users[0].username).toBe("alice_martin");
    });

    it("should handle repository errors", async () => {
      when(mockUserRepository.searchUsers(anything())).thenReject(new Error("Database error"));

      await expect(searchUsersUseCase.execute({ searchTerm: "alice" }))
        .rejects.toThrow("Database error");
    });
  });
}); 
import { CompleteOnboardingUseCase, OnboardingData } from '../../../../modules/users/application/usecases/CompleteOnboardingUseCase';
import { PersonalInfo, Gender } from '../../../../modules/users/domain/valueobjects/PersonalInfo';
import { Appearance, Ethnicity } from '../../../../modules/users/domain/valueobjects/Appearance';
import { OnboardingPhoto } from '../../../../modules/users/domain/valueobjects/OnboardingPhoto';
import { UserRepository } from '../../../../modules/users/domain/repositories/UserRepository';

describe('CompleteOnboardingUseCase', () => {
  let useCase: CompleteOnboardingUseCase;
  let mockUserRepository: jest.Mocked<UserRepository>;

  beforeEach(() => {
    mockUserRepository = {
      updateUserProfile: jest.fn(),
      updateOnboardingStep: jest.fn(),
      getUserProfile: jest.fn(),
      getUserOnboardingData: jest.fn(),
      searchUsers: jest.fn(),
      getUserById: jest.fn()
    };
    useCase = new CompleteOnboardingUseCase(mockUserRepository);
  });

  describe('execute', () => {
    it('devrait terminer avec succès un onboarding complet', async () => {
      // Arrange
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');
      
      const personalInfo = PersonalInfo.create(
        'Jean',
        'Dupont',
        new Date('1990-01-01'),
        Gender.MALE,
        'FR'
      );

      const appearance = new Appearance(
        Ethnicity.CAUCASIAN,
        175
      );

      const onboardingData: OnboardingData = {
        photo,
        personalInfo,
        appearance
      };

      const request = {
        userId: 'user123',
        onboardingData
      };

      // Act
      const result = await useCase.execute(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.profileCompletionPercentage).toBe(100);
    });

    it('devrait gérer un onboarding partiel (2 étapes sur 3)', async () => {
      // Arrange
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');
      
      const personalInfo = PersonalInfo.create(
        'Jean',
        'Dupont',
        new Date('1990-01-01'),
        Gender.MALE,
        'FR'
      );

      const onboardingData: OnboardingData = {
        photo,
        personalInfo,
        appearance: undefined // Étape 3 manquante
      };

      const request = {
        userId: 'user123',
        onboardingData
      };

      // Act
      const result = await useCase.execute(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.profileCompletionPercentage).toBe(67); // 2/3 = 67%
    });

    it('devrait calculer correctement le pourcentage de complétude', async () => {
      // Arrange - Seulement l'étape 1 (photo)
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');

      const onboardingData: OnboardingData = {
        photo,
        personalInfo: undefined,
        appearance: undefined
      };

      const request = {
        userId: 'user123',
        onboardingData
      };

      // Act
      const result = await useCase.execute(request);

      // Assert
      expect(result.profileCompletionPercentage).toBe(33); // 1/3 = 33%
    });

    it('devrait gérer les erreurs de validation des value objects', async () => {
      // Arrange - Les erreurs de validation sont maintenant gérées par les value objects
      const request = {
        userId: 'user123',
        onboardingData: {
          photo: undefined,
          personalInfo: undefined,
          appearance: undefined
        }
      };

      // Act
      const result = await useCase.execute(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.profileCompletionPercentage).toBe(0);
    });

    it('devrait valider le format de l\'URL de la photo', async () => {
      // Test que OnboardingPhoto valide correctement les URLs
      expect(() => {
        OnboardingPhoto.create('1', 'invalid-url', 'photo.jpg');
      }).toThrow('L\'URL de la photo n\'est pas valide');
    });

    it('devrait valider le format de fichier de la photo', async () => {
      // Test que OnboardingPhoto valide les formats supportés
      expect(() => {
        OnboardingPhoto.create('1', 'https://example.com/photo.gif', 'photo.gif');
      }).toThrow('Format d\'image non supporté. Utilisez JPG, PNG ou WebP');
    });

    it('devrait convertir OnboardingPhoto vers ProfilePhoto', async () => {
      // Arrange
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');
      
      const personalInfo = PersonalInfo.create(
        'Jean',
        'Dupont',
        new Date('1990-01-01'),
        Gender.MALE,
        'FR'
      );

      const appearance = new Appearance(Ethnicity.CAUCASIAN, 175);

      const onboardingData: OnboardingData = {
        photo,
        personalInfo,
        appearance
      };

      const request = {
        userId: 'user123',
        onboardingData
      };

      // Act
      await useCase.execute(request);

      // Assert
      expect(mockUserRepository.updateUserProfile).toHaveBeenCalledWith(
        'user123',
        expect.objectContaining({
          photos: expect.arrayContaining([
            expect.objectContaining({
              id: '1',
              url: 'https://example.com/photo1.jpg',
              isPrimary: true,
              order: 0
            })
          ]),
          isProfileComplete: true
        })
      );
    });
  });
});
import { OnboardingValidationService } from '../../../../../modules/users/domain/services/OnboardingValidationService';
import { PersonalInfo, Gender } from '../../../../../modules/users/domain/valueobjects/PersonalInfo';
import { OnboardingPhoto } from '../../../../../modules/users/domain/valueobjects/OnboardingPhoto';
import { Appearance, Ethnicity } from '../../../../../modules/users/domain/valueobjects/Appearance';

describe('OnboardingValidationService', () => {
  describe('isOnboardingComplete', () => {
    test('devrait retourner true pour un onboarding complet', () => {
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');
      
      const personalInfo = PersonalInfo.create(
        'Jean',
        'Dupont',
        new Date('1990-01-01'),
        Gender.MALE,
        'FR'
      );

      const appearance = new Appearance(Ethnicity.CAUCASIAN, 175);

      const completeData = {
        photo,
        personalInfo,
        appearance
      };

      const result = OnboardingValidationService.isOnboardingComplete(completeData);
      expect(result).toBe(true);
    });

    test('devrait retourner false pour un onboarding incomplet', () => {
      const incompleteData = {
        photo: undefined,
        personalInfo: undefined,
        appearance: undefined
      };

      const result = OnboardingValidationService.isOnboardingComplete(incompleteData);
      expect(result).toBe(false);
    });
  });

  describe('calculateCompletionScore', () => {
    test('devrait calculer 100% pour un profil complet', () => {
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');
      
      const personalInfo = PersonalInfo.create(
        'Jean',
        'Dupont',
        new Date('1990-01-01'),
        Gender.MALE,
        'FR'
      );

      const appearance = new Appearance(Ethnicity.CAUCASIAN, 175);

      const completeData = {
        photo,
        personalInfo,
        appearance
      };

      const score = OnboardingValidationService.calculateCompletionScore(completeData);
      expect(score).toBe(100);
    });

    test('devrait calculer le pourcentage correct pour un profil partiel', () => {
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');

      const partialData = {
        photo,
        personalInfo: undefined,
        appearance: undefined
      };

      const score = OnboardingValidationService.calculateCompletionScore(partialData);
      expect(score).toBe(33); // 1/3 des étapes complétées
    });
  });

  describe('canAccessStep', () => {
    test('devrait permettre l\'accès à l\'étape 1 (photo)', () => {
      const emptyData = {
        photo: undefined,
        personalInfo: undefined,
        appearance: undefined
      };

      const result = OnboardingValidationService.canAccessStep(1, emptyData);
      expect(result).toBe(true);
    });

    test('devrait permettre l\'accès à l\'étape 2 si l\'étape 1 est complète', () => {
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');

      const dataWithPhoto = {
        photo,
        personalInfo: undefined,
        appearance: undefined
      };

      const result = OnboardingValidationService.canAccessStep(2, dataWithPhoto);
      expect(result).toBe(true);
    });

    test('devrait empêcher l\'accès à l\'étape 3 si l\'étape 2 n\'est pas complète', () => {
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');

      const dataWithOnlyPhoto = {
        photo,
        personalInfo: undefined,
        appearance: undefined
      };

      const result = OnboardingValidationService.canAccessStep(3, dataWithOnlyPhoto);
      expect(result).toBe(false);
    });
  });

  describe('getNextStep', () => {
    test('devrait retourner l\'étape 1 pour un profil vide', () => {
      const emptyData = {
        photo: undefined,
        personalInfo: undefined,
        appearance: undefined
      };

      const nextStep = OnboardingValidationService.getNextStep(emptyData);
      expect(nextStep).toBe(1);
    });

    test('devrait retourner l\'étape 2 si seule la photo est complète', () => {
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');

      const dataWithPhoto = {
        photo,
        personalInfo: undefined,
        appearance: undefined
      };

      const nextStep = OnboardingValidationService.getNextStep(dataWithPhoto);
      expect(nextStep).toBe(2);
    });

    test('devrait retourner null pour un profil complet', () => {
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');
      
      const personalInfo = PersonalInfo.create(
        'Jean',
        'Dupont',
        new Date('1990-01-01'),
        Gender.MALE,
        'FR'
      );

      const appearance = new Appearance(Ethnicity.CAUCASIAN, 175);

      const completeData = {
        photo,
        personalInfo,
        appearance
      };

      const nextStep = OnboardingValidationService.getNextStep(completeData);
      expect(nextStep).toBe(null);
    });
  });

  describe('validateOnboardingCoherence', () => {
    test('devrait valider un profil cohérent', () => {
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');
      
      const personalInfo = PersonalInfo.create(
        'Jean',
        'Dupont',
        new Date('1990-01-01'),
        Gender.MALE,
        'FR'
      );

      const appearance = new Appearance(Ethnicity.CAUCASIAN, 175);

      const completeData = {
        photo,
        personalInfo,
        appearance
      };

      const result = OnboardingValidationService.validateOnboardingCoherence(completeData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('devrait rejeter un profil avec une personne mineure', () => {
      const photo = OnboardingPhoto.create('1', 'https://example.com/photo1.jpg', 'photo1.jpg');
      
      // On ne peut pas créer PersonalInfo avec un mineur, donc testons avec un mock
      const mockPersonalInfo = {
        getAge: () => 17
      } as PersonalInfo;

      const invalidData = {
        photo,
        personalInfo: mockPersonalInfo,
        appearance: new Appearance(Ethnicity.CAUCASIAN, 175)
      };

      const result = OnboardingValidationService.validateOnboardingCoherence(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Vous devez être majeur pour vous inscrire');
    });
  });
}); 
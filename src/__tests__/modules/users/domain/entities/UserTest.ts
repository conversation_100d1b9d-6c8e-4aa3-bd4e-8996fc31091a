import { User } from "@/modules/users/domain/entities/User";

describe("User Entity", () => {
  describe("create", () => {
    it("should create a user with all properties", () => {
      const userData = {
        id: "user-123",
        username: "john_doe",
        email: "<EMAIL>",
        avatar: "avatar-url",
        isOnline: true
      };

      const user = User.create(userData);

      expect(user.id).toBe("user-123");
      expect(user.username).toBe("john_doe");
      expect(user.email).toBe("<EMAIL>");
      expect(user.avatar).toBe("avatar-url");
      expect(user.isOnline).toBe(true);
    });

    it("should create a user with optional properties", () => {
      const userData = {
        id: "user-123",
        username: "john_doe",
        email: "<EMAIL>"
      };

      const user = User.create(userData);

      expect(user.id).toBe("user-123");
      expect(user.username).toBe("john_doe");
      expect(user.email).toBe("<EMAIL>");
      expect(user.avatar).toBeUndefined();
      expect(user.isOnline).toBeUndefined();
    });
  });

  describe("getDisplayName", () => {
    it("should return the username as display name", () => {
      const user = User.create({
        id: "user-123",
        username: "john_doe",
        email: "<EMAIL>"
      });

      expect(user.getDisplayName()).toBe("john_doe");
    });
  });

  describe("getInitials", () => {
    it("should return first character of username in uppercase", () => {
      const user = User.create({
        id: "user-123",
        username: "john_doe",
        email: "<EMAIL>"
      });

      expect(user.getInitials()).toBe("J");
    });

    it("should handle lowercase username", () => {
      const user = User.create({
        id: "user-123",
        username: "alice",
        email: "<EMAIL>"
      });

      expect(user.getInitials()).toBe("A");
    });
  });

  describe("isCurrentlyOnline", () => {
    it("should return true when user is online", () => {
      const user = User.create({
        id: "user-123",
        username: "john_doe",
        email: "<EMAIL>",
        isOnline: true
      });

      expect(user.isCurrentlyOnline()).toBe(true);
    });

    it("should return false when user is offline", () => {
      const user = User.create({
        id: "user-123",
        username: "john_doe",
        email: "<EMAIL>",
        isOnline: false
      });

      expect(user.isCurrentlyOnline()).toBe(false);
    });

    it("should return false when isOnline is undefined", () => {
      const user = User.create({
        id: "user-123",
        username: "john_doe",
        email: "<EMAIL>"
      });

      expect(user.isCurrentlyOnline()).toBe(false);
    });
  });
}); 
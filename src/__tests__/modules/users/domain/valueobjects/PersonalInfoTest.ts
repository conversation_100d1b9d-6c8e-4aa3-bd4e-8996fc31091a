import { PersonalInfo, Gender } from '../../../../../modules/users/domain/valueobjects/PersonalInfo';
import { PersonName } from '../../../../../modules/users/domain/valueobjects/PersonName';
import { Birthdate } from '../../../../../modules/users/domain/valueobjects/Birthdate';
import { Country } from '../../../../../modules/users/domain/valueobjects/Country';

describe('PersonalInfo', () => {
  const validPersonalInfo = {
    firstName: new PersonName('Jean'),
    lastName: new PersonName('Dupont'),
    birthdate: new Birthdate(new Date(1990, 5, 15)), // 15 juin 1990
    gender: Gender.MALE,
    country: new Country('FR')
  };

  describe('construction valide', () => {
    test('devrait créer un PersonalInfo valide', () => {
      const personalInfo = new PersonalInfo(
        validPersonalInfo.firstName,
        validPersonalInfo.lastName,
        validPersonalInfo.birthdate,
        validPersonalInfo.gender,
        validPersonalInfo.country
      );

      expect(personalInfo.firstName.toString()).toBe('Jean');
      expect(personalInfo.lastName.toString()).toBe('Dupont');
      expect(personalInfo.birthdate.value).toEqual(new Date(1990, 5, 15));
      expect(personalInfo.gender).toBe(Gender.MALE);
      expect(personalInfo.country.getCode()).toBe('FR');
    });

    test('devrait créer via la méthode statique create', () => {
      const personalInfo = PersonalInfo.create(
        'Jean',
        'Dupont',
        new Date(1990, 5, 15),
        Gender.MALE,
        'FR'
      );

      expect(personalInfo.firstName.toString()).toBe('Jean');
      expect(personalInfo.lastName.toString()).toBe('Dupont');
      expect(personalInfo.getAge()).toBeGreaterThan(30);
      expect(personalInfo.gender).toBe(Gender.MALE);
      expect(personalInfo.country.getCode()).toBe('FR');
    });
  });

  describe('validation', () => {
    test('devrait rejeter un genre invalide', () => {
      expect(() => new PersonalInfo(
        validPersonalInfo.firstName,
        validPersonalInfo.lastName,
        validPersonalInfo.birthdate,
        'invalid_gender' as Gender,
        validPersonalInfo.country
      )).toThrow('Le genre spécifié n\'est pas valide');
    });

    test('devrait rejeter via PersonName si prénom invalide', () => {
      expect(() => PersonalInfo.create(
        'J', // Trop court
        'Dupont',
        new Date(1990, 5, 15),
        Gender.MALE,
        'FR'
      )).toThrow('Le nom doit contenir au moins 2 caractères');
    });

    test('devrait rejeter via Birthdate si trop jeune', () => {
      const tooYoung = new Date();
      tooYoung.setFullYear(tooYoung.getFullYear() - 17);

      expect(() => PersonalInfo.create(
        'Jean',
        'Dupont',
        tooYoung,
        Gender.MALE,
        'FR'
      )).toThrow('Vous devez avoir au moins 18 ans pour vous inscrire');
    });

    test('devrait rejeter via Country si pays non supporté', () => {
      expect(() => PersonalInfo.create(
        'Jean',
        'Dupont',
        new Date(1990, 5, 15),
        Gender.MALE,
        'INVALID'
      )).toThrow('Le pays "INVALID" n\'est pas supporté actuellement');
    });
  });

  describe('méthodes utilitaires', () => {
    let personalInfo: PersonalInfo;

    beforeEach(() => {
      personalInfo = new PersonalInfo(
        validPersonalInfo.firstName,
        validPersonalInfo.lastName,
        validPersonalInfo.birthdate,
        validPersonalInfo.gender,
        validPersonalInfo.country
      );
    });

    test('getAge devrait déléguer à Birthdate', () => {
      const age = personalInfo.getAge();
      expect(age).toBe(validPersonalInfo.birthdate.getAge());
    });

    test('getDisplayAge devrait déléguer à Birthdate', () => {
      const displayAge = personalInfo.getDisplayAge();
      expect(displayAge).toBe(validPersonalInfo.birthdate.getDisplayAge());
    });

    test('getFullName devrait retourner noms capitalisés', () => {
      expect(personalInfo.getFullName()).toBe('Jean Dupont');
    });

    test('getDisplayName devrait être identique à getFullName', () => {
      expect(personalInfo.getDisplayName()).toBe(personalInfo.getFullName());
    });

    test('getCountryName devrait déléguer à Country', () => {
      expect(personalInfo.getCountryName()).toBe('France');
    });

    test('isFromEurope devrait déléguer à Country', () => {
      expect(personalInfo.isFromEurope()).toBe(true);
    });

    test('isFrancophone devrait déléguer à Country', () => {
      expect(personalInfo.isFrancophone()).toBe(true);
    });
  });

  describe('toApiFormat', () => {
    test('devrait convertir vers le format API', () => {
      const personalInfo = PersonalInfo.create(
        'Jean',
        'Dupont',
        new Date(1990, 5, 15),
        Gender.MALE,
        'FR'
      );

      const apiFormat = personalInfo.toApiFormat();

      expect(apiFormat).toEqual({
        firstName: 'Jean',
        lastName: 'Dupont',
        birthdate: new Date(1990, 5, 15),
        gender: Gender.MALE,
        country: 'FR'
      });
    });
  });

  describe('composition avec autres value objects', () => {
    test('devrait fonctionner avec différents pays', () => {
      const personalInfo = PersonalInfo.create(
        'John',
        'Smith',
        new Date(1985, 3, 20),
        Gender.MALE,
        'US'
      );

      expect(personalInfo.getCountryName()).toBe('États-Unis');
      expect(personalInfo.isFromEurope()).toBe(false);
      expect(personalInfo.isFrancophone()).toBe(false);
    });

    test('devrait gérer les noms avec caractères spéciaux', () => {
      const personalInfo = PersonalInfo.create(
        'Jean-Claude',
        "O'Connor",
        new Date(1990, 5, 15),
        Gender.MALE,
        'FR'
      );

      expect(personalInfo.getFullName()).toBe("Jean-Claude O'Connor");
    });
  });
}); 
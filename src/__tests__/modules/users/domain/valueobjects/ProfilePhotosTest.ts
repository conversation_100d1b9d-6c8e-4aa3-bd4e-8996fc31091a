import { ProfilePhotos } from '../../../../../modules/users/domain/valueobjects/ProfilePhotos';
import { ProfilePhoto } from '../../../../../modules/users/domain/valueobjects/UserProfile';

describe('ProfilePhotos', () => {
  const createValidPhotos = (): ProfilePhoto[] => [
    new ProfilePhoto('1', 'photo1.jpg', true, 0),
    new ProfilePhoto('2', 'photo2.jpg', false, 1),
    new ProfilePhoto('3', 'photo3.jpg', false, 2)
  ];

  describe('constructor', () => {
    it('devrait créer un ProfilePhotos avec des photos valides', () => {
      // Arrange
      const photos = createValidPhotos();

      // Act
      const profilePhotos = new ProfilePhotos(photos);

      // Assert
      expect(profilePhotos.photos).toEqual(photos);
      expect(profilePhotos.count()).toBe(3);
    });

    it('devrait rejeter si aucune photo n\'est fournie', () => {
      // Act & Assert
      expect(() => new ProfilePhotos([])).toThrow('Au moins une photo est requise');
    });

    it('devrait rejeter si plus de 6 photos sont fournies', () => {
      // Arrange
      const photos = Array(7).fill(null).map((_, i) => 
        new ProfilePhoto(`${i + 1}`, `photo${i + 1}.jpg`, i === 0, i)
      );

      // Act & Assert
      expect(() => new ProfilePhotos(photos)).toThrow('Maximum 6 photos autorisées');
    });

    it('devrait rejeter si aucune photo principale n\'est définie', () => {
      // Arrange
      const photos = [
        new ProfilePhoto('1', 'photo1.jpg', false, 0),
        new ProfilePhoto('2', 'photo2.jpg', false, 1)
      ];

      // Act & Assert
      expect(() => new ProfilePhotos(photos)).toThrow('Une photo principale doit être définie');
    });

    it('devrait rejeter s\'il y a plusieurs photos principales', () => {
      // Arrange
      const photos = [
        new ProfilePhoto('1', 'photo1.jpg', true, 0),
        new ProfilePhoto('2', 'photo2.jpg', true, 1)
      ];

      // Act & Assert
      expect(() => new ProfilePhotos(photos)).toThrow('Une seule photo peut être définie comme principale');
    });
  });

  describe('méthodes d\'accès', () => {
    it('devrait retourner la photo principale', () => {
      // Arrange
      const photos = createValidPhotos();
      const profilePhotos = new ProfilePhotos(photos);

      // Act
      const primaryPhoto = profilePhotos.getPrimaryPhoto();

      // Assert
      expect(primaryPhoto).toBeDefined();
      expect(primaryPhoto!.isPrimary).toBe(true);
      expect(primaryPhoto!.id).toBe('1');
    });

    it('devrait retourner les photos secondaires', () => {
      // Arrange
      const photos = createValidPhotos();
      const profilePhotos = new ProfilePhotos(photos);

      // Act
      const secondaryPhotos = profilePhotos.getSecondaryPhotos();

      // Assert
      expect(secondaryPhotos).toHaveLength(2);
      expect(secondaryPhotos.every(photo => !photo.isPrimary)).toBe(true);
    });

    it('devrait indiquer si la collection est vide', () => {
      // Arrange
      const photos = createValidPhotos();
      const profilePhotos = new ProfilePhotos(photos);

      // Act & Assert
      expect(profilePhotos.isEmpty()).toBe(false);
    });

    it('devrait indiquer si on peut ajouter plus de photos', () => {
      // Arrange
      const photos = createValidPhotos(); // 3 photos
      const profilePhotos = new ProfilePhotos(photos);

      // Act & Assert
      expect(profilePhotos.canAddMore()).toBe(true);
    });
  });

  describe('méthodes immutables', () => {
    it('devrait ajouter une photo de manière immutable', () => {
      // Arrange
      const photos = [new ProfilePhoto('1', 'photo1.jpg', true, 0)];
      const profilePhotos = new ProfilePhotos(photos);
      const newPhoto = new ProfilePhoto('2', 'photo2.jpg', false, 1);

      // Act
      const updatedPhotos = profilePhotos.addPhoto(newPhoto);

      // Assert
      expect(updatedPhotos.count()).toBe(2);
      expect(profilePhotos.count()).toBe(1); // L'original n'est pas modifié
    });

    it('devrait rejeter l\'ajout si déjà 6 photos', () => {
      // Arrange
      const photos = Array(6).fill(null).map((_, i) => 
        new ProfilePhoto(`${i + 1}`, `photo${i + 1}.jpg`, i === 0, i)
      );
      const profilePhotos = new ProfilePhotos(photos);
      const newPhoto = new ProfilePhoto('7', 'photo7.jpg', false, 6);

      // Act & Assert
      expect(() => profilePhotos.addPhoto(newPhoto)).toThrow('Maximum 6 photos autorisées');
    });

    it('devrait supprimer une photo de manière immutable', () => {
      // Arrange
      const photos = createValidPhotos();
      const profilePhotos = new ProfilePhotos(photos);

      // Act
      const updatedPhotos = profilePhotos.removePhoto('2');

      // Assert
      expect(updatedPhotos.count()).toBe(2);
      expect(profilePhotos.count()).toBe(3); // L'original n'est pas modifié
    });

    it('devrait changer la photo principale de manière immutable', () => {
      // Arrange
      const photos = createValidPhotos();
      const profilePhotos = new ProfilePhotos(photos);

      // Act
      const updatedPhotos = profilePhotos.setPrimaryPhoto('2');

      // Assert
      expect(updatedPhotos.getPrimaryPhoto()!.id).toBe('2');
      expect(profilePhotos.getPrimaryPhoto()!.id).toBe('1'); // L'original n'est pas modifié
    });
  });
}); 
import { Birthdate } from '../../../../../modules/users/domain/valueobjects/Birthdate';

describe('Birthdate', () => {
  const currentYear = new Date().getFullYear();
  const validDate = new Date(1990, 5, 15); // 15 juin 1990
  const tooYoung = new Date(currentYear - 17, 5, 15); // 17 ans
  const tooOld = new Date(currentYear - 101, 5, 15); // 101 ans

  describe('construction valide', () => {
    test('devrait créer une date de naissance valide', () => {
      const birthdate = new Birthdate(validDate);
      expect(birthdate.value).toEqual(validDate);
    });

    test('devrait accepter une personne de 18 ans exactement', () => {
      const eighteenYearsAgo = new Date();
      eighteenYearsAgo.setFullYear(eighteenYearsAgo.getFullYear() - 18);
      expect(() => new Birthdate(eighteenYearsAgo)).not.toThrow();
    });

    test('devrait accepter une personne de 100 ans exactement', () => {
      const hundredYearsAgo = new Date();
      hundredYearsAgo.setFullYear(hundredYearsAgo.getFullYear() - 100);
      expect(() => new Birthdate(hundredYearsAgo)).not.toThrow();
    });
  });

  describe('validation', () => {
    test('devrait rejeter une date null ou undefined', () => {
      expect(() => new Birthdate(null as any)).toThrow('La date de naissance est requise');
      expect(() => new Birthdate(undefined as any)).toThrow('La date de naissance est requise');
    });

    test('devrait rejeter une date invalide', () => {
      const invalidDate = new Date('invalid');
      expect(() => new Birthdate(invalidDate)).toThrow('La date de naissance doit être une date valide');
    });

    test('devrait rejeter une personne trop jeune (moins de 18 ans)', () => {
      expect(() => new Birthdate(tooYoung)).toThrow('Vous devez avoir au moins 18 ans pour vous inscrire');
    });

    test('devrait rejeter une personne trop âgée (plus de 100 ans)', () => {
      expect(() => new Birthdate(tooOld)).toThrow('Veuillez vérifier votre date de naissance');
    });

    test('devrait rejeter une date dans le futur', () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() + 1);
      expect(() => new Birthdate(futureDate)).toThrow('La date de naissance ne peut pas être dans le futur');
    });

    test('devrait rejeter une date trop ancienne (plus de 125 ans)', () => {
      const ancientDate = new Date();
      ancientDate.setFullYear(ancientDate.getFullYear() - 126);
      expect(() => new Birthdate(ancientDate)).toThrow('La date de naissance semble trop ancienne');
    });
  });

  describe('getAge', () => {
    test('devrait calculer l\'âge correctement', () => {
      const birthdate = new Birthdate(validDate);
      const expectedAge = currentYear - 1990;
      const actualAge = birthdate.getAge();
      
      // L'âge peut varier de +/-1 selon la date actuelle vs 15 juin
      expect(actualAge).toBeGreaterThanOrEqual(expectedAge - 1);
      expect(actualAge).toBeLessThanOrEqual(expectedAge);
    });

    test('devrait gérer les anniversaires non encore passés cette année', () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() - 25);
      futureDate.setMonth(11); // Décembre
      futureDate.setDate(31); // 31 décembre
      
      const birthdate = new Birthdate(futureDate);
      const age = birthdate.getAge();
      
      if (new Date().getMonth() < 11) {
        expect(age).toBe(24); // Anniversaire pas encore passé
      } else {
        expect(age).toBe(25); // Anniversaire passé
      }
    });
  });

  describe('getAgeAt', () => {
    test('devrait calculer l\'âge à une date donnée', () => {
      const birthdate = new Birthdate(new Date(1990, 5, 15)); // 15 juin 1990
      const targetDate = new Date(2020, 5, 15); // 15 juin 2020
      
      expect(birthdate.getAgeAt(targetDate)).toBe(30);
    });

    test('devrait calculer l\'âge avant l\'anniversaire', () => {
      const birthdate = new Birthdate(new Date(1990, 5, 15)); // 15 juin 1990
      const targetDate = new Date(2020, 5, 14); // 14 juin 2020
      
      expect(birthdate.getAgeAt(targetDate)).toBe(29);
    });
  });

  describe('getDisplayAge', () => {
    test('devrait retourner l\'âge avec "ans"', () => {
      const birthdate = new Birthdate(validDate);
      const displayAge = birthdate.getDisplayAge();
      expect(displayAge).toMatch(/^\d+ ans$/);
    });
  });

  describe('isBirthdayToday', () => {
    test('devrait retourner true si c\'est l\'anniversaire aujourd\'hui', () => {
      const today = new Date();
      const sameMonthDay = new Date(1990, today.getMonth(), today.getDate());
      const birthdate = new Birthdate(sameMonthDay);
      
      expect(birthdate.isBirthdayToday()).toBe(true);
    });

    test('devrait retourner false si ce n\'est pas l\'anniversaire', () => {
      const birthdate = new Birthdate(validDate);
      const today = new Date();
      
      // Si ce n'est pas le 15 juin aujourd'hui
      if (today.getMonth() !== 5 || today.getDate() !== 15) {
        expect(birthdate.isBirthdayToday()).toBe(false);
      }
    });
  });

  describe('getNextBirthday', () => {
    test('devrait retourner le prochain anniversaire cette année', () => {
      const today = new Date();
      // Créer un anniversaire dans le futur cette année
      const futureBirthday = new Date(1990, today.getMonth() + 1, 15);
      const birthdate = new Birthdate(futureBirthday);
      
      const nextBirthday = birthdate.getNextBirthday();
      expect(nextBirthday.getFullYear()).toBe(today.getFullYear());
      expect(nextBirthday.getMonth()).toBe(today.getMonth() + 1);
      expect(nextBirthday.getDate()).toBe(15);
    });

    test('devrait retourner le prochain anniversaire l\'année prochaine si déjà passé', () => {
      const today = new Date();
      // Créer un anniversaire dans le passé cette année
      const pastBirthday = new Date(1990, 0, 1); // 1er janvier 1990
      const birthdate = new Birthdate(pastBirthday);
      
      const nextBirthday = birthdate.getNextBirthday();
      
      if (today.getMonth() > 0 || today.getDate() > 1) {
        expect(nextBirthday.getFullYear()).toBe(today.getFullYear() + 1);
      } else {
        expect(nextBirthday.getFullYear()).toBe(today.getFullYear());
      }
    });
  });

  describe('equals', () => {
    test('devrait retourner true pour des dates identiques', () => {
      const date1 = new Date(1990, 5, 15);
      const date2 = new Date(1990, 5, 15);
      const birthdate1 = new Birthdate(date1);
      const birthdate2 = new Birthdate(date2);
      
      expect(birthdate1.equals(birthdate2)).toBe(true);
    });

    test('devrait retourner false pour des dates différentes', () => {
      const date1 = new Date(1990, 5, 15);
      const date2 = new Date(1990, 5, 16);
      const birthdate1 = new Birthdate(date1);
      const birthdate2 = new Birthdate(date2);
      
      expect(birthdate1.equals(birthdate2)).toBe(false);
    });
  });
}); 
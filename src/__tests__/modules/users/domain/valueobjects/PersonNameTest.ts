import { PersonName } from '../../../../../modules/users/domain/valueobjects/PersonName';

describe('PersonName', () => {
  describe('construction valide', () => {
    test('devrait créer un nom valide', () => {
      const name = new PersonName('<PERSON>');
      expect(name.value).toBe('<PERSON>');
      expect(name.toString()).toBe('<PERSON>');
    });

    test('devrait accepter les noms avec espaces', () => {
      const name = new PersonName('<PERSON>');
      expect(name.toString()).toBe('<PERSON>');
    });

    test('devrait accepter les noms avec tirets', () => {
      const name = new PersonName('<PERSON><PERSON><PERSON>');
      expect(name.toString()).toBe('Marie-Claire');
    });

    test('devrait accepter les noms avec apostrophes', () => {
      const name = new PersonName("<PERSON>'Connor");
      expect(name.toString()).toBe("<PERSON><PERSON><PERSON>");
    });

    test('devrait accepter les caractères accentués', () => {
      const name = new PersonName('<PERSON>');
      expect(name.toString()).toBe('François');
    });
  });

  describe('validation', () => {
    test('devrait rejeter un nom vide', () => {
      expect(() => new PersonName('')).toThrow('Le nom est requis');
    });

    test('devrait rejeter un nom avec seulement des espaces', () => {
      expect(() => new PersonName('   ')).toThrow('Le nom est requis');
    });

    test('devrait rejeter un nom trop court', () => {
      expect(() => new PersonName('A')).toThrow('Le nom doit contenir au moins 2 caractères');
    });

    test('devrait rejeter un nom trop long', () => {
      const longName = 'A'.repeat(51);
      expect(() => new PersonName(longName)).toThrow('Le nom ne peut pas dépasser 50 caractères');
    });

    test('devrait rejeter les caractères spéciaux non autorisés', () => {
      expect(() => new PersonName('Jean123')).toThrow('Le nom ne peut contenir que des lettres, espaces, tirets et apostrophes');
      expect(() => new PersonName('Jean@')).toThrow('Le nom ne peut contenir que des lettres, espaces, tirets et apostrophes');
      expect(() => new PersonName('Jean#')).toThrow('Le nom ne peut contenir que des lettres, espaces, tirets et apostrophes');
    });

    test('devrait accepter un nom de 2 caractères', () => {
      expect(() => new PersonName('Li')).not.toThrow();
    });

    test('devrait accepter un nom de 50 caractères', () => {
      const name50 = 'A'.repeat(50);
      expect(() => new PersonName(name50)).not.toThrow();
    });
  });

  describe('toCapitalized', () => {
    test('devrait capitaliser un nom simple', () => {
      const name = new PersonName('jean');
      expect(name.toCapitalized()).toBe('Jean');
    });

    test('devrait capitaliser chaque mot', () => {
      const name = new PersonName('jean claude');
      expect(name.toCapitalized()).toBe('Jean Claude');
    });

    test('devrait gérer les noms déjà capitalisés', () => {
      const name = new PersonName('Jean Claude');
      expect(name.toCapitalized()).toBe('Jean Claude');
    });

    test('devrait gérer les noms en majuscules', () => {
      const name = new PersonName('JEAN CLAUDE');
      expect(name.toCapitalized()).toBe('Jean Claude');
    });
  });

  describe('equals', () => {
    test('devrait retourner true pour des noms identiques', () => {
      const name1 = new PersonName('Jean');
      const name2 = new PersonName('Jean');
      expect(name1.equals(name2)).toBe(true);
    });

    test('devrait retourner true pour des noms avec différentes capitalisations', () => {
      const name1 = new PersonName('Jean');
      const name2 = new PersonName('jean');
      expect(name1.equals(name2)).toBe(true);
    });

    test('devrait retourner true pour des noms avec espaces différents', () => {
      const name1 = new PersonName('Jean ');
      const name2 = new PersonName(' Jean');
      expect(name1.equals(name2)).toBe(true);
    });

    test('devrait retourner false pour des noms différents', () => {
      const name1 = new PersonName('Jean');
      const name2 = new PersonName('Pierre');
      expect(name1.equals(name2)).toBe(false);
    });
  });

  describe('toString avec trim', () => {
    test('devrait supprimer les espaces en début et fin', () => {
      const name = new PersonName('  Jean  ');
      expect(name.toString()).toBe('Jean');
    });
  });
}); 
import { fireEvent, render, screen } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import { ResetPasswordPage } from "@/pages/ResetPassword";


const mockUseParams = jest.fn().mockReturnValue({ token: "token" });
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => mockUseParams(),
}));

const mockResetPassword = jest.fn();

// Mock du hook useAuth
jest.mock("@/modules/auth/presentation/hooks/useAuth", () => ({
  useAuth: () => ({
    resetPassword: mockResetPassword,
  }),
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe("ResetPassword", () => {
  it("should render the page", () => {
    mockUseParams.mockReturnValue({ token: "token" });
    renderWithRouter(<ResetPasswordPage />);

    expect(screen.getByPlaceholderText("New Password")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Confirm Password")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Reset Password" })).toBeInTheDocument();
  });

  it("should display an error message if the token is not found", () => {
    mockUseParams.mockReturnValue({ token: undefined });
    renderWithRouter(<ResetPasswordPage />);

    expect(screen.getByText("Invalid url")).toBeInTheDocument();
  });

  it("should reset the password", () => {
    mockUseParams.mockReturnValue({ token: "token" });
    renderWithRouter(<ResetPasswordPage />);

    fireEvent.change(screen.getByPlaceholderText("New Password"), {
      target: { value: "newPassword123!" },
    });
    fireEvent.change(screen.getByPlaceholderText("Confirm Password"), {
      target: { value: "newPassword123!" },
    });
    fireEvent.click(screen.getByRole("button", { name: "Reset Password" }));

    expect(mockResetPassword).toHaveBeenCalledWith("token", "newPassword123!");
  });

  it("should display an error message if passwords do not match", () => {
    mockUseParams.mockReturnValue({ token: "token" });
    renderWithRouter(<ResetPasswordPage />);

    fireEvent.change(screen.getByPlaceholderText("New Password"), {
      target: { value: "newPassword123!" },
    });
    fireEvent.change(screen.getByPlaceholderText("Confirm Password"), {
      target: { value: "newPassword123!!" },
    });
    fireEvent.click(screen.getByRole("button", { name: "Reset Password" }));

    expect(screen.getByText("Passwords do not match")).toBeInTheDocument();
    expect(mockResetPassword).not.toHaveBeenCalled();
  });

  it("should display an error message if the password is not valid", () => {
    mockUseParams.mockReturnValue({ token: "token" });
    renderWithRouter(<ResetPasswordPage />);

    fireEvent.change(screen.getByPlaceholderText("New Password"), {
      target: { value: "newPassword123" },
    });
    fireEvent.change(screen.getByPlaceholderText("Confirm Password"), {
      target: { value: "newPassword123" },
    });
    fireEvent.click(screen.getByRole("button", { name: "Reset Password" }));

    expect(screen.getByText("Password must contain at least one uppercase letter, one lowercase letter, one number and one special character")).toBeInTheDocument();
    expect(mockResetPassword).not.toHaveBeenCalled();
  });

  it("should display an error message if the password is too short", () => {
    mockUseParams.mockReturnValue({ token: "token" });
    renderWithRouter(<ResetPasswordPage />);

    fireEvent.change(screen.getByPlaceholderText("New Password"), {
      target: { value: "newPa" },
    });
    fireEvent.change(screen.getByPlaceholderText("Confirm Password"), {
      target: { value: "newPa" },
    });
    fireEvent.click(screen.getByRole("button", { name: "Reset Password" }));

    expect(screen.getByText("Password must be at least 8 characters long")).toBeInTheDocument();
    expect(mockResetPassword).not.toHaveBeenCalled();
  });
});
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import ForgotPassword from "@/pages/ForgotPassword";

// Mock simple du hook useAuth sans aucune dépendance externe
const mockLogin = jest.fn();
const mockSignup = jest.fn();
const mockLogout = jest.fn();
const mockForgotPassword = jest.fn();

// Mock du hook useAuth
jest.mock("@/modules/auth/presentation/hooks/useAuth", () => ({
  useAuth: () => ({
    login: mockLogin,
    signup: mockSignup,
    logout: mockLogout,
    forgotPassword: mockForgotPassword,
    isAuthenticated: false,
    user: null,
  }),
}));

describe("ForgotPassword", () => {
  it("should render the forgot password page", () => {
    render(<ForgotPassword />);
    expect(screen.getByText("Forgot Password")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Enter your email")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Send" })).toBeInTheDocument();
  });

  it("should show an error message if the email is not valid", () => {
    render(<ForgotPassword />);

    const emailInput = screen.getByPlaceholderText("Enter your email");
    const sendButton = screen.getByRole("button", { name: "Send" });
    fireEvent.change(emailInput, { target: { value: "invalid-email" } });
    fireEvent.click(sendButton);

    expect(screen.getByText("Invalid email")).toBeInTheDocument();
  });

  it("should display an info message if the email is valid", async () => {
    mockForgotPassword.mockResolvedValue(undefined);
    render(<ForgotPassword />);

    const emailInput = screen.getByPlaceholderText("Enter your email");
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(screen.getByRole("button", { name: "Send" }));

    await waitFor(() => {
      expect(mockForgotPassword).toHaveBeenCalledWith("<EMAIL>");
      expect(screen.getByText("Email sent")).toBeInTheDocument();
    });
  });
});
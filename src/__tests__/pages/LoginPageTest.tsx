import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import LoginPage from "@/pages/LoginPage";

// Mock de useNavigate
const mockNavigate = jest.fn();
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => mockNavigate,
}));

// Mock simple du hook useAuth sans aucune dépendance externe
const mockLogin = jest.fn();
const mockSignup = jest.fn();
const mockLogout = jest.fn();

// Mock du hook useAuth
jest.mock("@/modules/auth/presentation/hooks/useAuth", () => ({
  useAuth: () => ({
    login: mockLogin,
    signup: mockSignup,
    logout: mockLogout,
    isAuthenticated: false,
    user: null,
  }),
}));

// Wrapper pour fournir le contexte Router
const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe("LoginPage Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the login page with all elements", () => {
    renderWithRouter(<LoginPage />);
    
    expect(screen.getByLabelText("Email")).toBeInTheDocument();
    expect(screen.getByLabelText("Password")).toBeInTheDocument();
    expect(screen.getByRole("link", { name: "Forgot password?" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Login" })).toBeInTheDocument();
    expect(screen.getByText("Don't have an account?")).toBeInTheDocument();
    expect(screen.getByRole("link", { name: "Sign up" })).toBeInTheDocument();
  });

  it("should update username input when user types", () => {
    renderWithRouter(<LoginPage />);
    
    const emailInput = screen.getByLabelText("Email") as HTMLInputElement;
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    
    expect(emailInput.value).toBe("<EMAIL>");
  });

  it("should update password input when user types", () => {
    renderWithRouter(<LoginPage />);
    
    const passwordInput = screen.getByLabelText("Password") as HTMLInputElement;
    fireEvent.change(passwordInput, { target: { value: "testpassword" } });
    
    expect(passwordInput.value).toBe("testpassword");
  });

  it("should call login function when form is submitted with valid data", async () => {
    renderWithRouter(<LoginPage />);
    
    const emailInput = screen.getByLabelText("Email");
    const passwordInput = screen.getByLabelText("Password");
    const form = screen.getByRole("button", { name: "Login" }).closest("form")!;
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "testpassword" } });
    fireEvent.submit(form);
    
    expect(mockLogin).toHaveBeenCalledWith("<EMAIL>", "testpassword");
    expect(mockLogin).toHaveBeenCalledTimes(1);
  });

  it("should call login function when button is clicked", async () => {
    renderWithRouter(<LoginPage />);
    
    const emailInput = screen.getByLabelText("Email");
    const passwordInput = screen.getByLabelText("Password");
    const submitButton = screen.getByRole("button", { name: "Login" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "testpassword" } });
    fireEvent.click(submitButton);
    
    expect(mockLogin).toHaveBeenCalledWith("<EMAIL>", "testpassword");
    expect(mockLogin).toHaveBeenCalledTimes(1);
  });

  it("should have correct input types and placeholders", () => {
    renderWithRouter(<LoginPage />);
    
    const emailInput = screen.getByLabelText("Email");
    const passwordInput = screen.getByLabelText("Password");
    
    expect(emailInput).toHaveAttribute("type", "text");
    expect(emailInput).toHaveAttribute("placeholder", "Enter your email");
    expect(passwordInput).toHaveAttribute("type", "password");
    expect(passwordInput).toHaveAttribute("placeholder", "Enter your password");
  });

  it("should have required attributes on inputs", () => {
    renderWithRouter(<LoginPage />);
    
    const emailInput = screen.getByLabelText("Email");
    const passwordInput = screen.getByLabelText("Password");
    
    expect(emailInput).toBeRequired();
    expect(passwordInput).toBeRequired();
  });

  it("should handle successful login", async () => {
    mockLogin.mockResolvedValue(undefined);
    
    renderWithRouter(<LoginPage />);
    
    const emailInput = screen.getByLabelText("Email");
    const passwordInput = screen.getByLabelText("Password");
    const submitButton = screen.getByRole("button", { name: "Login" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "validpassword" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith("<EMAIL>", "validpassword");
    });
  });

  it("should display an error message when login fails", async () => {
    mockLogin.mockRejectedValue(new Error("Login failed"));

    renderWithRouter(<LoginPage />);

    const emailInput = screen.getByLabelText("Email");
    const passwordInput = screen.getByLabelText("Password");
    const submitButton = screen.getByRole("button", { name: "Login" });

    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "validpassword" } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText("Invalid email or password")).toBeInTheDocument();
    });
  });
});
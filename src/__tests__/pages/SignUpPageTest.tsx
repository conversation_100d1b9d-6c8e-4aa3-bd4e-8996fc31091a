import { fireEvent, render, screen } from "@testing-library/react";
import { <PERSON>rowserRouter } from "react-router-dom";
import SignupPage from "../../pages/SignupPage";

// Mock de useNavigate
const mockNavigate = jest.fn();
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => mockNavigate,
}));

// Mock du hook useAuth
const mockSignup = jest.fn();
jest.mock("@/modules/auth/presentation/hooks/useAuth", () => ({
  useAuth: () => ({
    signup: mockSignup,
    login: jest.fn(),
    logout: jest.fn(),
    isAuthenticated: false,
    user: null,
  }),
}));

// Wrapper pour fournir le contexte Router
const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe("SignupPage", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the signup page", () => {
    renderWithRouter(<SignupPage />);

    expect(screen.getByLabelText("Enter your username")).toBeInTheDocument();
    expect(screen.getByLabelText("Enter your email")).toBeInTheDocument();
    expect(screen.getByLabelText("Enter your password")).toBeInTheDocument();
    expect(screen.getByLabelText("Confirm password")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Sign up" })).toBeInTheDocument();
    expect(screen.getByText("Already have an account?")).toBeInTheDocument();
    expect(screen.getByRole("link", { name: "Login" })).toBeInTheDocument();
  });

  it("should show an error message if the passwords do not match", () => {
    renderWithRouter(<SignupPage />);

    const usernameInput = screen.getByLabelText("Enter your username");
    const emailInput = screen.getByLabelText("Enter your email");
    const passwordInput = screen.getByLabelText("Enter your password");
    const confirmPasswordInput = screen.getByLabelText("Confirm password");
    const signupButton = screen.getByRole("button", { name: "Sign up" });

    fireEvent.change(usernameInput, { target: { value: "testuser" } });
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "Password321!" } });
    fireEvent.click(signupButton);

    expect(screen.getByText("Passwords do not match")).toBeInTheDocument();
  });

  it("should show an error message if the email is not valid", () => {
    renderWithRouter(<SignupPage />);

    const usernameInput = screen.getByLabelText("Enter your username");
    const emailInput = screen.getByLabelText("Enter your email");
    const passwordInput = screen.getByLabelText("Enter your password");
    const confirmPasswordInput = screen.getByLabelText("Confirm password");
    const signupButton = screen.getByRole("button", { name: "Sign up" });

    fireEvent.change(usernameInput, { target: { value: "testuser" } });
    fireEvent.change(emailInput, { target: { value: "invalid-email" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "Password123!" } });
    fireEvent.click(signupButton);

    expect(screen.getByText("Invalid email")).toBeInTheDocument();
  });

  it("should show an error message if the password is too short", () => {
    renderWithRouter(<SignupPage />);

    const usernameInput = screen.getByLabelText("Enter your username");
    const emailInput = screen.getByLabelText("Enter your email");
    const passwordInput = screen.getByLabelText("Enter your password");
    const confirmPasswordInput = screen.getByLabelText("Confirm password");
    const signupButton = screen.getByRole("button", { name: "Sign up" });

    fireEvent.change(usernameInput, { target: { value: "testuser" } });
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "123" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "123" } });
    fireEvent.click(signupButton);

    expect(screen.getByText("Password must be at least 8 characters long")).toBeInTheDocument();
  });

  it("should show an error message if the password is not valid", () => {
    renderWithRouter(<SignupPage />);

    const usernameInput = screen.getByLabelText("Enter your username");
    const emailInput = screen.getByLabelText("Enter your email");
    const passwordInput = screen.getByLabelText("Enter your password");
    const confirmPasswordInput = screen.getByLabelText("Confirm password");
    const signupButton = screen.getByRole("button", { name: "Sign up" });

    fireEvent.change(usernameInput, { target: { value: "testuser" } });
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "123456789" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "123456789" } });
    fireEvent.click(signupButton);

    expect(screen.getByText("Password must contain at least one uppercase letter, one lowercase letter, one number and one special character")).toBeInTheDocument();
  });

  it("should show an error message if the username is not valid", () => {
    renderWithRouter(<SignupPage />);

    const usernameInput = screen.getByLabelText("Enter your username");
    const emailInput = screen.getByLabelText("Enter your email");
    const passwordInput = screen.getByLabelText("Enter your password");
    const confirmPasswordInput = screen.getByLabelText("Confirm password");
    const signupButton = screen.getByRole("button", { name: "Sign up" });

    fireEvent.change(usernameInput, { target: { value: "ab" } });
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "Password123!" } });
    fireEvent.click(signupButton);

    expect(screen.getByText("Username must be at least 3 characters long")).toBeInTheDocument();
  });

  it("should call signup function when form is submitted successfully", async () => {
    mockSignup.mockResolvedValue(undefined);
    renderWithRouter(<SignupPage />);

    const usernameInput = screen.getByLabelText("Enter your username");
    const emailInput = screen.getByLabelText("Enter your email");
    const passwordInput = screen.getByLabelText("Enter your password");
    const confirmPasswordInput = screen.getByLabelText("Confirm password");
    const signupButton = screen.getByRole("button", { name: "Sign up" });

    fireEvent.change(usernameInput, { target: { value: "testuser" } });
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "Password123!" } });
    fireEvent.click(signupButton);

    expect(mockSignup).toHaveBeenCalledWith("<EMAIL>", "Password123!", "testuser");
  });
});
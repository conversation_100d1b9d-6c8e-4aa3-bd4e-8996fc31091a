# Tests du Module Chat

Cette documentation explique la stratégie de test et l'organisation des tests pour le module chat.

## Structure des Tests

```
src/__tests__/
├── modules/
│   └── chat/
│       ├── application/          # Tests des Use Cases
│       ├── valueobjects/         # Tests des Value Objects
│       ├── entities/            # Tests des Entités
│       └── integration/         # Tests d'Intégration
```

## Types de Tests

### 1. Tests des Value Objects (`valueobjects/`)

Tests unitaires pour les objets de valeur du domaine :

- **MessageContent** : Validation du contenu des messages
- **MessageType** : Types de messages (TEXT, IMAGE, FILE, SYSTEM)
- **MessageStatus** : Statuts des messages (SENT, DELIVERED, READ)

**Objectif** : S'assurer que la validation métier est correcte et que les invariants du domaine sont respectés.

### 2. Tests des Entités (`entities/`)

Tests unitaires pour les entités du domaine :

- **Message** : Création, modification, règles métier
- **Chat** : Gestion des conversations

**Objectif** : Valider la logique métier des entités et leur comportement.

### 3. Tests des Use Cases (`application/`)

Tests unitaires pour les cas d'usage :

- **SendMessageUseCase** : Envoi de messages
- **GetChatHistoryUseCase** : Récupération de l'historique
- **MarkMessageAsReadUseCase** : Marquage comme lu

**Objectif** : Tester la logique applicative avec des mocks des dépendances externes.

### 4. Tests d'Intégration (`integration/`)

Tests d'intégration qui valident l'interaction entre les couches :

- **ChatIntegrationTest** : Tests de bout en bout des flux complets

**Objectif** : S'assurer que les différentes couches fonctionnent correctement ensemble.

## Scripts de Test

```bash
# Lancer tous les tests
npm test

# Lancer les tests en mode watch
npm run test:watch

# Générer un rapport de couverture
npm run test:coverage

# Tests spécifiques au module chat
npm run test:chat

# Tests d'intégration seulement
npm run test:integration

# Tests unitaires seulement
npm run test:unit

# Lancer tous les tests (unitaires + intégration)
npm run test:all
```

## Stratégie de Test

### Principes

1. **Test Pyramid** : Plus de tests unitaires, moins de tests d'intégration
2. **AAA Pattern** : Arrange, Act, Assert dans chaque test
3. **Isolation** : Chaque test est indépendant
4. **Mocking** : Utilisation de ts-mockito pour les dépendances

### Couverture de Code

Objectifs de couverture :
- **Branches** : 80%
- **Fonctions** : 80%
- **Lignes** : 80%
- **Statements** : 80%

### Bonnes Pratiques

1. **Nommage des Tests** : Utiliser un format descriptif
   ```typescript
   it('should create a message with valid data', () => {
     // Test implementation
   });
   ```

2. **Organisation des Tests** : Grouper par fonctionnalité avec `describe`
   ```typescript
   describe('MessageContent', () => {
     describe('Validation', () => {
       // Tests de validation
     });
   });
   ```

3. **Setup/Teardown** : Utiliser `beforeEach`/`afterEach` pour la préparation
   ```typescript
   beforeEach(() => {
     // Setup commun
   });
   ```

4. **Données de Test** : Utiliser des factories pour créer des données cohérentes
   ```typescript
   const validMessageData = {
     content: 'Hello world!',
     senderId: 'user-1',
     receiverId: 'user-2',
     type: MessageType.text()
   };
   ```

## Mocking

### WebSocket Service

```typescript
jest.mock('@/modules/chat/infrastructure/services/ChatWebSocketService');
```

### API Client

```typescript
jest.mock('@/modules/chat/infrastructure/api/ChatApiClient');
```

### Repositories

Utilisation de ts-mockito pour créer des mocks typés :

```typescript
import { mock, instance, when, verify } from 'ts-mockito';

const mockRepository = mock<IChatRepository>();
const repositoryInstance = instance(mockRepository);
```

## Configuration Jest

La configuration Jest se trouve dans `jest.config.js` avec :

- **Environment** : jsdom pour les tests React
- **Module Mapping** : Support des alias `@/`
- **Coverage** : Configuration des seuils
- **Setup** : Configuration globale dans `setupTests.ts`

## Debugging

Pour débugger les tests :

```bash
# Lancer un test spécifique
npm test -- --testNamePattern="should send a text message"

# Mode verbose
npm test -- --verbose

# Voir les mocks appelés
npm test -- --verbose --no-cache
```

## Maintenance

- **Révision régulière** : Vérifier que les tests restent pertinents
- **Refactoring** : Maintenir les tests lors des refactorings du code
- **Documentation** : Documenter les cas de test complexes 
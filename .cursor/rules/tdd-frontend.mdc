---
description: 
globs: 
alwaysApply: false
---
# TDD Frontend React - Règles Strictes

## Cycle TDD Obligatoire (Baby Steps)

### 1. RED - Écrire un test qui échoue
- **Toujours commencer par un test en échec**
- **Un seul comportement par test**
- **Nommer les tests avec "should" + comportement attendu**
- **Utiliser Given/When/Then dans les commentaires**

### 2. GREEN - Code minimal pour passer le test
- **Écrire le code le plus simple possible**
- **Pas d'optimisation prématurée**
- **Hardcoder les valeurs si nécessaire**

### 3. REFACTOR - Améliorer sans casser
- **Refactoriser uniquement après que tous les tests passent**
- **Améliorer la lisibilité et éliminer la duplication**
- **Garder les tests verts pendant le refactoring**

## Structure des Tests Frontend

### Tests de Composants React
```typescript
// Exemple dans [src/__tests__/pages/LoginPageTest.tsx](mdc:amorzee/amorzee/src/__tests__/pages/LoginPageTest.tsx)
describe('LoginPage', () => {
  it('should display login form when user is not authenticated', () => {
    // Given: utilisateur non connecté
    // When: on affiche la page de login  
    // Then: le formulaire doit être visible
  });
});
```

### Tests d'Hooks
- **Tester les hooks personnalisés isolément**
- **Utiliser @testing-library/react-hooks**
- **Tester les effets de bord (side effects)**

### Tests de Contexte
- **Tester les providers de contexte**
- **Vérifier les états et transitions**
- **Mocker les dépendances externes**

## Règles Spécifiques Frontend

### Ne PAS tester :
- **Détails d'implémentation (état interne, méthodes privées)**
- **Librairies tierces (React, Material-UI, etc.)**
- **Styles CSS (sauf si critique pour l'UX)**

### TOUJOURS tester :
- **Comportements utilisateur visibles**
- **Interactions (clicks, saisies, navigation)**
- **Rendu conditionnel**
- **Gestion des erreurs**
- **Intégration avec les APIs**

## Architecture Hexagonale Frontend

### Domain Layer
```typescript
// Exemple dans [src/modules/auth/domain/entities/AuthUser.ts](mdc:amorzee/amorzee/src/modules/auth/domain/entities/AuthUser.ts)
// Tester les entités métier
it('should validate email format', () => {
  // Given: une adresse email invalide
  // When: on crée un utilisateur
  // Then: une exception doit être levée
});
```

### Application Layer  
```typescript
// Exemple dans [src/modules/auth/application/usecases/Login.ts](mdc:amorzee/amorzee/src/modules/auth/application/usecases/Login.ts)
// Tester les cas d'usage
it('should authenticate user with valid credentials', () => {
  // Given: des identifiants valides
  // When: on exécute le login
  // Then: l'utilisateur doit être connecté
});
```

### Presentation Layer
```typescript
// Exemple dans [src/modules/auth/presentation/hooks/useAuth.tsx](mdc:amorzee/amorzee/src/modules/auth/presentation/hooks/useAuth.tsx)
// Tester les hooks et composants
it('should redirect to dashboard when login succeeds', () => {
  // Given: un utilisateur non connecté
  // When: le login réussit
  // Then: redirection vers le dashboard
});
```

## Mocking et Stubs

### APIs
```typescript
// Mocker les appels API
jest.mock('../api/AuthUserApiRepository');
```

### Contextes
```typescript
// Mocker les contextes React
const mockAuthContext = {
  user: null,
  isAuthenticated: false,
  login: jest.fn(),
  logout: jest.fn()
};
```

## Couverture de Tests

### Objectifs
- **Couverture de branche : 90%+**
- **Couverture de ligne : 95%+**
- **Tous les cas d'usage critiques : 100%**

### Commandes
```bash
npm test -- --coverage
npm run test:watch  # Mode watch pour TDD
```

## Bonnes Pratiques

### Nommage
- **Fichiers de test : `*.test.tsx` ou `*Test.tsx`**
- **Describe : nom du composant/hook**
- **It : "should + comportement attendu"**

### Organisation
- **Un fichier de test par composant/hook**
- **Regrouper les tests par fonctionnalité**
- **Setup et teardown dans beforeEach/afterEach**

### Lisibilité
- **Arrange/Act/Assert ou Given/When/Then**
- **Éviter les tests trop longs**
- **Noms de variables explicites**
- **Extraire les helpers de test**

## Refactoring Sûr

### Avant de refactoriser
1. **Tous les tests passent**
2. **Pas de code mort**
3. **Couverture de test suffisante**

### Pendant le refactoring
1. **Garder les tests verts**
2. **Refactoriser par petites étapes**
3. **Commit fréquent**

### Après le refactoring
1. **Vérifier que tous les tests passent**
2. **Pas de régression fonctionnelle**
3. **Code plus lisible et maintenable**
